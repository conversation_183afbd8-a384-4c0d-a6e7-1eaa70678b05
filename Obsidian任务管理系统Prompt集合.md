# Obsidian 任务管理系统 Prompt 集合

## 🎯 **系统开发 Prompt**

### 📋 **基础任务管理系统**
```
我需要在Obsidian中创建一个任务管理系统，要求：

1. 使用Dataview插件实现
2. 支持任务时间分类：今天、明天、本周、未来、逾期、进行中
3. 任务格式：- [ ] 任务名 📅 YYYY-MM-DD 🍅数字 ⏫🔼🔽 #重要
4. 进行中任务标记：🔄 或 "进行中"
5. 创建可视化仪表盘，用彩色卡片显示各类任务数量
6. 点击卡片可查看详细任务列表
7. 使用中国标准周计算（周一开始，周日结束）

请提供完整的dataviewjs代码实现。
```

### 🍅 **番茄钟管理系统**
```
在现有任务管理系统基础上，添加番茄钟功能：

1. 支持Daily Log中设置每日目标：tomato_goal: 8
2. 创建番茄钟看板，显示：
   - 总计划、已完成、今日目标、今日完成
   - 总体完成率、目标达成率
3. 支持预估→实际番茄钟记录：🍅3→5
4. 自动读取Daily Log属性并计算达成率
5. 提供复盘模板和工作流程指导

请提供dataviewjs代码和使用指南。
```

### 📁 **项目任务分布系统**
```
创建项目管理功能，实现：

1. 识别两种任务状态：
   - 已规划：6_Project Notes/文件夹中的任务
   - 待整理：日记中带[[项目名]]双链的任务
2. 显示项目统计表格：已规划、待整理、已完成、完成率
3. 点击"待整理"可查看具体任务和来源日记
4. 支持快速跳转到原文件
5. 符合用户工作流：快速捕获→整理→执行

请提供完整实现方案。
```

## 🔧 **功能优化 Prompt**

### 📊 **数据统计优化**
```
优化任务统计功能：

1. 修复时间分类逻辑，确保统计数字与显示一致
2. 改进周计算为中国标准（周一开始）
3. 添加调试信息显示功能
4. 优化性能，减少重复计算
5. 增强错误处理和边界情况

请提供优化后的代码。
```

### 🎨 **界面交互优化**
```
改进用户界面体验：

1. 添加悬停效果和点击反馈
2. 实现任务详情弹窗显示
3. 支持任务状态快速切换
4. 添加加载状态和错误提示
5. 优化移动端显示效果
6. 增加键盘快捷键支持

请提供界面优化方案。
```

## 📝 **内容生成 Prompt**

### 📖 **文档生成**
```
为Obsidian任务管理系统生成完整文档：

1. 技术文档：架构设计、实现原理、API说明
2. 使用指南：分步骤教程、工作流程、最佳实践
3. 故障排除：常见问题、解决方案、调试方法
4. 模板文件：Daily Log模板、项目模板、任务模板

要求：结构清晰、示例丰富、易于理解。
```

### 🎯 **工作流程设计**
```
设计高效的任务管理工作流程：

1. 每日工作流：早上规划、执行过程、晚上复盘
2. 项目管理流：快速捕获、任务整理、进度追踪
3. 番茄钟管理：目标设定、时间追踪、效率分析
4. 数据分析：趋势分析、问题识别、持续改进

提供详细的操作步骤和时间安排。
```

## 🐛 **问题解决 Prompt**

### 🔍 **调试问题**
```
解决任务管理系统中的问题：

问题描述：[具体问题]
期望行为：[期望的正确行为]
实际行为：[当前错误行为]
环境信息：Obsidian版本、Dataview版本、操作系统

请分析问题原因并提供解决方案，包括：
1. 问题根因分析
2. 修复代码
3. 测试验证方法
4. 预防措施
```

### ⚡ **性能优化**
```
优化任务管理系统性能：

当前问题：
- 任务数量多时加载缓慢
- 频繁刷新导致卡顿
- 内存占用过高

优化目标：
- 减少计算复杂度
- 实现增量更新
- 优化数据结构
- 添加缓存机制

请提供性能优化方案。
```

## 🚀 **功能扩展 Prompt**

### 📈 **数据分析功能**
```
为任务管理系统添加数据分析功能：

1. 时间趋势分析：
   - 每日/周/月任务完成趋势
   - 番茄钟效率变化曲线
   - 项目进度时间线

2. 效率分析：
   - 最高效时间段识别
   - 任务类型效率对比
   - 预估准确度分析

3. 可视化图表：
   - 使用Chart.js或类似库
   - 支持多种图表类型
   - 交互式数据展示

请提供实现方案。
```

### 🔔 **提醒通知系统**
```
添加智能提醒功能：

1. 任务提醒：
   - 截止日期临近提醒
   - 逾期任务警告
   - 进行中任务过多提醒

2. 目标提醒：
   - 番茄钟目标进度提醒
   - 每日目标达成提醒
   - 周/月目标总结

3. 习惯提醒：
   - 定时复盘提醒
   - 任务整理提醒
   - 数据备份提醒

请设计提醒系统架构。
```

## 🎨 **主题定制 Prompt**

### 🌈 **视觉主题**
```
为任务管理系统设计多套视觉主题：

1. 主题风格：
   - 简约现代风格
   - 暗黑模式
   - 彩色活泼风格
   - 商务专业风格

2. 定制元素：
   - 颜色搭配方案
   - 图标设计
   - 字体选择
   - 动画效果

3. 响应式设计：
   - 桌面端优化
   - 移动端适配
   - 平板端体验

请提供CSS样式和配置方案。
```

## 🔄 **集成扩展 Prompt**

### 🔗 **第三方集成**
```
实现任务管理系统与第三方工具集成：

1. 日历集成：
   - Google Calendar同步
   - Outlook日历导入
   - 任务时间可视化

2. 时间追踪：
   - Toggl集成
   - RescueTime数据
   - 番茄钟计时器

3. 项目管理：
   - Notion同步
   - Trello导入
   - GitHub Issues集成

请提供集成方案和API调用示例。
```

## 📚 **学习资源 Prompt**

### 🎓 **教程创建**
```
创建Obsidian任务管理系统学习教程：

1. 新手入门：
   - 基础概念介绍
   - 环境搭建指南
   - 第一个任务创建

2. 进阶技巧：
   - 高级功能使用
   - 自定义配置
   - 工作流优化

3. 专家级：
   - 代码定制
   - 插件开发
   - 系统集成

包含视频脚本、图文教程、实践练习。
```

---

## 💡 **使用建议**

### 🎯 **Prompt 使用技巧**
1. **具体化需求**：详细描述功能要求和期望效果
2. **提供上下文**：说明现有系统状态和限制条件
3. **分步骤实现**：复杂功能分解为多个小步骤
4. **包含示例**：提供具体的输入输出示例
5. **测试验证**：要求提供测试方法和验证步骤

### 🔄 **迭代优化**
1. **反馈循环**：根据实际使用效果调整Prompt
2. **版本管理**：记录Prompt的演进历史
3. **最佳实践**：总结成功的Prompt模式
4. **知识积累**：建立Prompt知识库

### 📊 **效果评估**
1. **功能完整性**：生成的代码是否满足需求
2. **代码质量**：性能、可维护性、可扩展性
3. **用户体验**：界面友好性、操作便捷性
4. **文档质量**：说明清晰度、示例丰富度

---

*这些Prompt可以根据具体需求进行调整和组合使用，帮助快速开发和优化任务管理系统。*
