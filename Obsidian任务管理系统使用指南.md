# Obsidian 任务管理系统使用指南

## 🚀 **快速开始（5分钟上手）**

### 第一步：环境准备
1. 确保已安装 **Dataview** 插件
2. 创建文件夹结构：
   ```
   ├── 0_Bullet Journal/Daily Notes/
   ├── 6_Project Notes/
   ```
3. 将 `任务仪表盘-简化版.md` 放在库根目录

### 第二步：创建第一个任务
在任意文件中添加：
```markdown
- [ ] 学习任务管理 📅 2025-05-25 🍅2 ⏫ #重要
```

### 第三步：查看仪表盘
打开 `任务仪表盘-简化版.md`，应该能看到任务出现在对应的时间分类中。

## 📅 **每日工作流程**

### 🌅 **早上规划（5分钟）**

#### 1. 设置番茄钟目标
在今日 Daily Log 头部添加：
```yaml
---
tomato_goal: 8
---
```

#### 2. 查看待办任务
打开任务仪表盘，重点关注：
- 🔴 **逾期**：需要立即处理
- 🟢 **今天**：今日必须完成
- 🟡 **进行中**：昨天未完成的任务

#### 3. 规划新任务
在 Daily Log 的"🚀 快速任务捕获"区域添加：
```markdown
- [ ] 完成项目报告 [[工作项目]] 📅 2025-05-25 🍅3 ⏫ #重要
- [ ] 学习新技术 [[学习计划]] 📅 2025-05-25 🍅2 🔼
```

### 🔄 **执行过程**

#### 1. 标记进行中
开始任务时添加进行中标记：
```markdown
- [ ] 完成项目报告 🔄 [[工作项目]] 📅 2025-05-25 🍅3 ⏫ #重要
```

#### 2. 专注执行
- 使用番茄钟技术专注工作
- 避免同时标记过多任务为"进行中"（建议3-5个）

#### 3. 更新实际时间
完成后更新番茄钟：
```markdown
- [x] 完成项目报告 [[工作项目]] 📅 2025-05-25 🍅3→4 ⏫ #重要
```

### 🌙 **晚上复盘（10分钟）**

#### 1. 查看完成情况
打开任务仪表盘，查看：
- 番茄钟达成率
- 各分类任务完成情况

#### 2. 记录复盘
在 Daily Log 中填写：
```markdown
## 🍅 今日番茄钟复盘
- 目标：🍅 8
- 实际：🍅 6
- 达成率：75%
- 分析：下午被会议打断，明天调整为7个目标

### 📊 时间分布
- 上午：🍅🍅🍅 (3个，效率高)
- 下午：🍅🍅 (2个，被打断)
- 晚上：🍅 (1个，状态一般)

### 💡 改进建议
- 上午安排重要任务
- 下午避免安排会议
- 提前准备明天的任务
```

#### 3. 清理进行中标记
移除未完成任务的🔄标记，避免积累过多。

## 📁 **项目管理流程**

### 🚀 **快速捕获阶段**

#### 在日记中快速记录
```markdown
## 🚀 快速任务捕获
- [ ] 设计新界面 [[UI设计项目]] 📅 2025-05-26 🍅4 ⏫ #重要
- [ ] 用户调研 [[UI设计项目]] 📅 2025-05-27 🍅2 🔼
- [ ] 原型制作 [[UI设计项目]] 📅 2025-05-28 🍅3 🔼
```

### ⚙️ **任务整理阶段**

#### 1. 创建项目文件
在 `6_Project Notes/` 创建 `UI设计项目.md`

#### 2. 迁移并细化任务
```markdown
# UI设计项目

## 🎯 项目目标
设计用户友好的新界面

## 📋 任务列表

### 🔥 高优先级
- [ ] 设计新界面 📅 2025-05-26 🍅4 ⏫ #重要
  - [ ] @设计新界面 竞品分析 📅 2025-05-26 🍅1 🔼
  - [ ] @设计新界面 草图绘制 📅 2025-05-26 🍅2 🔼
  - [ ] @设计新界面 高保真设计 📅 2025-05-26 🍅1 🔼

### 💪 中优先级
- [ ] 用户调研 📅 2025-05-27 🍅2 🔼
- [ ] 原型制作 📅 2025-05-28 🍅3 🔼
```

#### 3. 移除双链标记
将 `[[UI设计项目]]` 标记从任务中删除。

### 📊 **项目追踪**

在任务仪表盘的"📁 项目任务分布"中可以看到：
- 🎯 已规划：项目文件中的任务数
- 📝 待整理：日记中还未迁移的任务（点击可查看详情）
- ✅ 已完成：完成的任务数
- 📈 完成率：项目进度

## 🎯 **主任务与子任务管理**

### 📝 **创建层级结构**
```markdown
- [ ] 开发新功能 🍅8 📅 2025-05-30 ⏫ #重要
  - [ ] @开发新功能 需求分析 🍅2 📅 2025-05-26 🔼
  - [ ] @开发新功能 技术选型 🍅1 📅 2025-05-27 🔼
  - [ ] @开发新功能 代码实现 🍅4 📅 2025-05-29 🔼
  - [ ] @开发新功能 测试验证 🍅1 📅 2025-05-30 🔽
```

### 🔍 **查看关系**
在"🎯 主任务与子任务"模块中可以看到：
- 主任务名称
- 关联的子任务数量
- 所在文件位置

## 🔍 **快速筛选功能**

### 🔥 **筛选按钮**
- **🔥 重要任务**：显示所有 `#重要` 任务
- **⏫ 高优先级**：显示所有 `⏫` 任务
- **🍅 大任务(3+)**：显示番茄钟≥3的任务
- **📅 无日期**：显示没有日期的任务

### 📍 **快速跳转**
点击任务可以直接跳转到原文件位置。

## 💡 **高级技巧**

### 🎯 **番茄钟预估技巧**
- **简单任务**：🍅1（25分钟）
- **中等任务**：🍅2-3（50-75分钟）
- **复杂任务**：🍅4+（100分钟以上，建议拆分）

### 📊 **数据分析**
- 每周回顾番茄钟达成率趋势
- 分析哪类任务容易预估不准
- 找到个人最高效的时间段

### 🔄 **状态管理**
- **新任务**：不添加状态标记
- **开始执行**：添加🔄
- **暂停中断**：移除🔄
- **完成任务**：勾选✅

### 📅 **时间管理**
- **今天**：必须完成的任务
- **明天**：需要今天准备的任务
- **本周其他**：本周计划任务
- **未来**：下周及以后的任务

## ⚠️ **注意事项**

### ❌ **常见错误**
1. 日期格式错误：必须是 `📅 YYYY-MM-DD`
2. 进行中任务过多：建议控制在3-5个
3. 忘记更新实际番茄钟数
4. 项目文件不在正确文件夹中

### ✅ **最佳实践**
1. 每天固定时间查看仪表盘
2. 及时整理待整理任务
3. 定期清理进行中标记
4. 坚持记录番茄钟复盘

## 🆘 **故障排除**

### 问题：任务不显示
**解决方案**：
1. 检查日期格式
2. 确认文件在正确位置
3. 刷新页面

### 问题：番茄钟目标不生效
**解决方案**：
1. 检查 YAML 格式
2. 确认文件名包含日期
3. 重新打开仪表盘

### 问题：项目统计不准确
**解决方案**：
1. 确认项目文件在 `6_Project Notes/`
2. 检查双链格式 `[[项目名]]`
3. 刷新页面

---

## 🎉 **恭喜！**

现在你已经掌握了完整的任务管理系统使用方法。记住：
- **坚持使用**是关键
- **持续优化**工作流程
- **享受高效**的工作体验

开始你的高效任务管理之旅吧！🚀
