# Obsidian 任务管理系统完整文档

## 📋 **系统概述**

这是一个基于 Obsidian + Dataview 的智能任务管理系统，支持：
- 📅 时间分类管理（今天、明天、本周、未来、逾期）
- 🍅 番茄钟目标设定与追踪
- 📁 项目任务分布统计
- 🎯 主任务与子任务关系管理
- 🔄 进行中任务标记
- 📊 可视化仪表盘

## 🏗️ **系统架构**

### 📂 **文件结构**
```
├── 任务仪表盘-简化版.md          # 主仪表盘文件
├── 0_Bullet Journal/
│   └── Daily Notes/              # 日记文件夹
│       └── YYYY-MM-DD 周X XX.md  # 日记文件
├── 6_Project Notes/              # 项目文件夹
│   └── 项目名.md                 # 项目文件
└── 5_BuJo - Daily Log.md         # 日记模板
```

### 🔧 **核心组件**

1. **任务统计卡片**：7个彩色卡片显示任务分布
2. **番茄钟看板**：4个卡片 + 2个完成率卡片
3. **项目任务分布**：智能识别项目文件和待整理任务
4. **主任务与子任务**：显示任务层级关系
5. **快速筛选**：按条件筛选任务

## 📝 **任务格式规范**

### 🏷️ **标准格式**
```markdown
- [ ] 任务名称 📅 2025-05-25 🍅3 ⏫ [[项目名]] #重要
- [ ] @主任务名 子任务描述 📅 2025-05-25 🍅1 🔼
```

### 🔤 **符号说明**
- `📅 YYYY-MM-DD`：截止日期
- `🍅数字`：番茄钟数量（预估→实际）
- `⏫🔼🔽`：优先级（高中低）
- `[[项目名]]`：项目双链（临时捕获用）
- `#重要/#不重要`：重要性标记
- `@主任务名`：子任务标记
- `🔄` 或 `进行中`：进行中状态

### 🔄 **任务状态流转**
```
📝 新建 → 🚀 进行中(🔄) → ⏸️ 暂停 → ✅ 完成
```

## 🍅 **番茄钟目标管理**

### 📋 **Daily Log 设置**
在日记文件头部添加：
```yaml
---
tomato_goal: 8
---
```

### 📊 **复盘模板**
```markdown
## 🍅 今日番茄钟复盘
- 目标：🍅 8
- 实际：🍅 6
- 达成率：75%
- 分析：下午被会议打断

### 📊 时间分布
- 上午：🍅🍅🍅 (3个)
- 下午：🍅🍅 (2个)
- 晚上：🍅 (1个)

### 💡 改进建议
- 调整会议时间避免打断专注时间
```

### 🔄 **工作流程**
1. **早上规划**：设置 `tomato_goal`，给任务标记预估🍅
2. **执行过程**：专注完成任务
3. **完成后**：更新实际番茄钟 `🍅3→5`
4. **晚上复盘**：查看达成率，记录分析

## 📁 **项目管理工作流**

### 🚀 **快速捕获阶段**
在日记中快速记录：
```markdown
- [ ] 学习新技术 [[AI项目]] 📅 2025-05-25 🍅3 ⏫ #重要
```

### ⚙️ **任务整理阶段**
1. 将任务迁移到 `6_Project Notes/AI项目.md`
2. 去掉双链标记 `[[AI项目]]`
3. 细化为主任务和子任务

### 📊 **项目追踪**
系统自动统计：
- 🎯 已规划：项目文件中的任务
- 📝 待整理：日记中带 `[[项目名]]` 的任务
- ✅ 已完成：完成的任务数量
- 📈 完成率：自动计算

## 🎯 **主任务与子任务**

### 📝 **格式规范**
```markdown
- [ ] 开发新功能 🍅5 📅 2025-05-25 ⏫ #重要
  - [ ] @开发新功能 需求分析 🍅1 📅 2025-05-25 🔼
  - [ ] @开发新功能 代码实现 🍅3 📅 2025-05-25 🔼
  - [ ] @开发新功能 测试验证 🍅1 📅 2025-05-25 🔽
```

### 🔍 **显示条件**
主任务显示条件（满足其一）：
- 有相关子任务
- 包含番茄钟标记🍅

## ⚙️ **技术实现要点**

### 📅 **时间计算**
- 使用中国标准：周一开始，周日结束
- 自动计算本周范围
- 支持跨天任务处理

### 🔍 **任务筛选逻辑**
```javascript
// 今天任务
dueDate === today && !isInProgress

// 明天任务  
dueDate === tomorrowStr && !isInProgress

// 本周其他任务
dueDate >= startDate && dueDate <= endDate && 
dueDate !== today && dueDate !== tomorrowStr && !isInProgress

// 未来任务
dueDate > endDate && !isInProgress

// 逾期任务
dueDate < today && !isInProgress
```

### 📊 **番茄钟统计**
```javascript
// 读取Daily Log目标
const todayDailyLog = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.includes(today))
    .first();
let dailyGoal = todayDailyLog?.tomato_goal || 0;

// 计算达成率
const todayRate = dailyGoal > 0 ? 
    Math.round((todayCompleted / dailyGoal) * 100) : 
    (todayPlanned > 0 ? Math.round((todayCompleted / todayPlanned) * 100) : 0);
```

### 🎨 **界面交互**
- 点击卡片查看详细任务列表
- 可点击按钮跳转到原文件
- 待整理任务可点击查看来源日记
- 支持任务完成状态切换

## 🚀 **部署步骤**

### 1️⃣ **环境准备**
- 安装 Obsidian
- 安装 Dataview 插件
- 创建文件夹结构

### 2️⃣ **文件部署**
- 复制 `任务仪表盘-简化版.md` 到根目录
- 设置 Daily Log 模板
- 创建项目文件夹

### 3️⃣ **配置测试**
- 在日记中添加 `tomato_goal` 属性
- 创建测试任务
- 验证仪表盘功能

## 📈 **最佳实践**

### 🎯 **目标设定**
- 新手：6-8个番茄钟/天
- 熟练：8-12个番茄钟/天
- 根据个人情况调整

### 📝 **任务管理**
- 同时进行中任务不超过3-5个
- 每日开始先查看"今天"和"逾期"
- 定期清理"进行中"标记
- 使用番茄钟估算任务时间

### 🔄 **工作流程**
- 快速捕获 → 定期整理 → 专注执行 → 复盘改进

## 🐛 **常见问题**

### Q: 任务不显示在对应分类中？
A: 检查日期格式是否为 `📅 YYYY-MM-DD`，确认没有进行中标记

### Q: 番茄钟目标不生效？
A: 确认 Daily Log 文件名包含今日日期，YAML 属性格式正确

### Q: 项目任务统计不准确？
A: 检查项目文件是否在 `6_Project Notes/` 文件夹中

### Q: 子任务关联不上主任务？
A: 确认子任务格式为 `@主任务名 描述`，主任务名要匹配

## 🔄 **版本更新日志**

### v1.0 (2025-05-24)
- ✅ 基础任务分类功能
- ✅ 番茄钟看板
- ✅ 项目任务分布
- ✅ 中国标准周计算
- ✅ 番茄钟目标管理
- ✅ 待整理任务追踪

---

*本文档持续更新中，如有问题请及时反馈*
