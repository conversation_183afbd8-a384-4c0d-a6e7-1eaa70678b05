<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合伙人告知书 - Obsidian系统模板</title>
    <style>
        :root {
            --bg-color: #0f0f0f;
            --card-bg: #1a1a1a;
            --card-hover: #252525;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --accent-blue: #3a86ff;
            --accent-purple: #8338ec;
            --accent-green: #06d6a0;
            --accent-yellow: #ffbe0b;
            --accent-red: #ef476f;
            --border-radius: 12px;
            --spacing: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-primary);
            font-family: 'Segoe UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .title {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px rgba(58, 134, 255, 0.3);
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-auto-rows: minmax(100px, auto);
            gap: var(--spacing);
            margin-bottom: var(--spacing);
        }

        .bento-item {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .bento-item:hover {
            background-color: var(--card-hover);
            transform: translateY(-5px);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
        }

        .bento-item.intro {
            grid-column: span 12;
            grid-row: span 1;
        }

        .bento-item.system {
            grid-column: span 4;
            grid-row: span 2;
        }

        .bento-item.knowledge {
            grid-column: span 12;
            grid-row: span 2;
        }

        .bento-item.pain {
            grid-column: span 6;
            grid-row: span 2;
        }

        .bento-item.reference {
            grid-column: span 6;
            grid-row: span 2;
        }

        .bento-item.audience {
            grid-column: span 12;
            grid-row: span 1;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            border-radius: 3px;
        }

        .tag {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .system-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .system-desc {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .price {
            font-size: 1.5rem;
            font-weight: 700;
            margin-top: auto;
        }

        .status {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            font-size: 0.8rem;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .divider {
            height: 1px;
            width: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), transparent);
            margin: 1rem 0;
        }

        .pain-point {
            margin-bottom: 1rem;
            padding-left: 1rem;
            border-left: 2px solid var(--accent-red);
        }

        /* Color themes */
        .blue {
            border-top: 3px solid var(--accent-blue);
        }
        .blue .tag, .blue .status {
            color: var(--accent-blue);
        }

        .purple {
            border-top: 3px solid var(--accent-purple);
        }
        .purple .tag, .purple .status {
            color: var(--accent-purple);
        }

        .green {
            border-top: 3px solid var(--accent-green);
        }
        .green .tag, .green .status {
            color: var(--accent-green);
        }

        .yellow {
            border-top: 3px solid var(--accent-yellow);
        }
        .yellow .tag, .yellow .status {
            color: var(--accent-yellow);
        }

        .red {
            border-top: 3px solid var(--accent-red);
        }
        .red .tag, .red .status {
            color: var(--accent-red);
        }

        @media (max-width: 992px) {
            .bento-item.system {
                grid-column: span 6;
            }
        }

        @media (max-width: 768px) {
            .bento-item.system {
                grid-column: span 12;
            }
            .bento-item.pain, .bento-item.reference {
                grid-column: span 12;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">合伙人告知书</h1>
            <div class="subtitle">OBSIDIAN SYSTEM TEMPLATE PROJECT</div>
        </div>

        <div class="bento-grid">
            <div class="bento-item intro blue">
                <h2 class="section-title">项目介绍</h2>
                <p>小红书开店卖Obsidian系统模板，卖我搭建好的系统</p>
            </div>

            <div class="bento-item system blue">
                <div class="tag">基础系统</div>
                <div class="status">收尾阶段</div>
                <div class="icon">✏️</div>
                <h3 class="system-title">睡眠日志系统</h3>
                <p class="system-desc">引流用</p>
                <div class="divider"></div>
                <div class="price">¥9.9</div>
            </div>

            <div class="bento-item system purple">
                <div class="tag">基础系统</div>
                <div class="status">收尾阶段</div>
                <div class="icon">⚡</div>
                <h3 class="system-title">精力管理系统</h3>
                <p class="system-desc">高效管理个人精力</p>
                <div class="divider"></div>
                <div class="price">¥9.9</div>
            </div>

            <div class="bento-item system green">
                <div class="tag">基础系统</div>
                <div class="status">收尾阶段</div>
                <div class="icon">⏱️</div>
                <h3 class="system-title">时间管理系统</h3>
                <p class="system-desc">优化时间分配与利用</p>
                <div class="divider"></div>
                <div class="price">¥9.9</div>
            </div>

            <div class="bento-item system yellow">
                <div class="tag">基础系统</div>
                <div class="status">收尾阶段</div>
                <div class="icon">✅</div>
                <h3 class="system-title">任务管理系统</h3>
                <p class="system-desc">高效任务跟踪与完成</p>
                <div class="divider"></div>
                <div class="price">¥9.9</div>
            </div>

            <div class="bento-item system red">
                <div class="tag">进阶系统</div>
                <div class="status">收尾阶段</div>
                <div class="icon">📊</div>
                <h3 class="system-title">项目管理系统</h3>
                <p class="system-desc">全面项目规划与执行</p>
                <div class="divider"></div>
                <div class="price">¥36.9</div>
            </div>

            <div class="bento-item knowledge blue">
                <div class="tag">综合系统</div>
                <div class="status">规划中</div>
                <div class="icon">🧠</div>
                <h3 class="system-title">知识管理系统</h3>
                <p class="system-desc">包含以上系统，还有其他零碎系统</p>
                <div class="divider"></div>
                <div class="price">¥79.9</div>
            </div>

            <div class="bento-item audience">
                <p style="color: var(--text-secondary); text-align: center;">有写笔记的受众人群：这需要你去市场调查下</p>
            </div>

            <div class="bento-item pain red">
                <h2 class="section-title">痛点</h2>
                <div class="pain-point">
                    <p>我都愿意付费200以内买这样的模板或者请人设计这样的模板</p>
                </div>
                <div class="pain-point">
                    <p>我找了市面上所有的模板，当然有些标价很贵的没试过，看图片介绍就大概知道了，没有适合我的</p>
                </div>
                <div class="pain-point">
                    <p>这个项目是解决我本身的痛点，顺便卖给同样有需求的人</p>
                </div>
            </div>

            <div class="bento-item reference green">
                <h2 class="section-title">参考</h2>
                <p>以下是我参考的博主的店的链接</p>
            </div>
        </div>
    </div>
</body>
</html>
