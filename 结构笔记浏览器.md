---
created: 2025-05-25T16:40
updated: 2025-05-25T16:40
tags:
  - type/dashboard
  - structure/browser
---

# 🏗️ 结构笔记浏览器

> 🗂️ 知识体系架构，构建思维框架

## 🚀 快速操作

```dataviewjs
// 创建快速操作面板
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #FFFBF0 0%, #FFF8F0 100%);
    border-radius: 16px;
    border: 2px solid rgba(232, 213, 165, 0.3);
`;

const quickActions = [
    {
        title: '新建结构笔记',
        icon: '➕',
        desc: '创建知识框架',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates/Structure Note Template', '', false);
            }
        },
        color: '#E8D5A5'
    },
    {
        title: 'AI最佳实践',
        icon: '🤖',
        desc: 'AI使用指南',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('5_Structures/系统/AI/AI最佳实践工作流', '', false);
            }
        },
        color: '#D0C4E8'
    },
    {
        title: '系统架构',
        icon: '🏛️',
        desc: '查看系统结构',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('5_Structures/系统', '', false);
            }
        },
        color: '#A5D6E8'
    },
    {
        title: '结构模板',
        icon: '📋',
        desc: '查看结构模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates', '', false);
            }
        },
        color: '#E8B4A0'
    }
];

let actionsHTML = '';
quickActions.forEach((action, index) => {
    actionsHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 12px; padding: 18px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action-btn" data-index="${index}">
            <div style="font-size: 2em; margin-bottom: 8px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickActionsContainer.innerHTML = actionsHTML;

// 添加点击事件
setTimeout(() => {
    const actionBtns = quickActionsContainer.querySelectorAll('.quick-action-btn');
    actionBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            quickActions[index].action();
        });

        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 🏗️ 所有结构笔记

```dataview
TABLE WITHOUT ID
    file.link AS "🗂️ 结构标题",
    choice(subject, subject, "📝 未分类") AS "🏷️ 学科",
    choice(type, type, "🏗️ 结构") AS "📚 类型",
    choice(importance = "核心", "🔴 核心", choice(importance = "重要", "🟡 重要", "⚪ 一般")) AS "⭐ 重要性",
    file.mtime AS "📅 修改时间"
FROM "5_Structures"
WHERE file.name != "README"
SORT file.mtime DESC
```

## 📁 按文件夹分类

```dataviewjs
// 获取5_Structures下的所有文件夹和文件
const allStructures = dv.pages('"5_Structures"').where(p => p.file.name !== "README");

// 按文件夹路径分组
const folderGroups = {};
allStructures.forEach(p => {
    const pathParts = p.file.path.split('/');
    if (pathParts.length > 2) {
        const folder = pathParts.slice(1, -1).join('/'); // 去掉第一个"5_Structures"和最后的文件名
        if (!folderGroups[folder]) {
            folderGroups[folder] = [];
        }
        folderGroups[folder].push(p);
    } else {
        // 根目录文件
        if (!folderGroups['根目录']) {
            folderGroups['根目录'] = [];
        }
        folderGroups['根目录'].push(p);
    }
});

// 创建文件夹展示容器
const folderContainer = document.createElement('div');
folderContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
`;

Object.entries(folderGroups).forEach(([folder, files]) => {
    const folderDiv = document.createElement('div');
    folderDiv.style.cssText = `
        background: rgba(255,255,255,0.8);
        border-radius: 16px;
        padding: 20px;
        border: 2px solid rgba(232, 213, 165, 0.3);
        box-shadow: 0 4px 16px rgba(139, 115, 85, 0.1);
    `;

    let filesHTML = '';
    files.forEach(file => {
        const importance = file.importance === "核心" ? "🔴" : file.importance === "重要" ? "🟡" : "⚪";
        filesHTML += `
            <div style="margin: 8px 0; padding: 8px; background: rgba(248,246,243,0.5); 
                        border-radius: 8px; cursor: pointer; transition: all 0.2s ease;"
                 class="structure-file" data-link="${file.file.name}">
                <div style="font-weight: 600; color: #8B7355; font-size: 0.95em;">
                    ${importance} ${file.file.name}
                </div>
                <div style="font-size: 0.8em; color: #A0896B; margin-top: 4px;">
                    ${file.subject || "未分类"} | ${file.file.mtime.toString().split(' ')[0]}
                </div>
            </div>
        `;
    });

    folderDiv.innerHTML = `
        <div style="display: flex; align-items: center; margin-bottom: 15px; padding-bottom: 10px; 
                    border-bottom: 2px solid rgba(232, 213, 165, 0.3);">
            <span style="font-size: 1.5em; margin-right: 10px;">📁</span>
            <h3 style="margin: 0; color: #E8D5A5; font-weight: 600; font-size: 1.1em;">
                ${folder}
            </h3>
            <span style="margin-left: auto; background: rgba(232, 213, 165, 0.3); 
                         padding: 4px 8px; border-radius: 8px; font-size: 0.8em; color: #8B7355;">
                ${files.length} 个文件
            </span>
        </div>
        ${filesHTML}
    `;

    folderContainer.appendChild(folderDiv);
});

// 添加点击事件
setTimeout(() => {
    const structureFiles = folderContainer.querySelectorAll('.structure-file');
    structureFiles.forEach(file => {
        file.addEventListener('click', function() {
            const link = this.getAttribute('data-link');
            if (app && app.workspace) {
                app.workspace.openLinkText(`5_Structures/${link}`, '', false);
            }
        });

        file.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(232, 213, 165, 0.2)';
            this.style.transform = 'translateX(8px)';
        });

        file.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(248,246,243,0.5)';
            this.style.transform = 'translateX(0)';
        });
    });
}, 100);

this.container.appendChild(folderContainer);
```

## 📊 结构笔记统计

```dataviewjs
// 获取所有结构笔记
const allStructures = dv.pages('"5_Structures"').where(p => p.file.name !== "README");

// 统计数据
const totalStructures = allStructures.length;
const coreStructures = allStructures.where(p => p.importance === "核心").length;
const importantStructures = allStructures.where(p => p.importance === "重要").length;
const generalStructures = allStructures.where(p => !p.importance || p.importance === "一般").length;

// 创建统计容器
const statsContainer = document.createElement('div');
statsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #FFFBF0 0%, #FFF8F0 100%);
    border-radius: 16px;
    border: 2px solid rgba(232, 213, 165, 0.2);
`;

const stats = [
    { label: '总结构', value: totalStructures, icon: '🏗️', color: '#8B7355' },
    { label: '核心', value: coreStructures, icon: '🔴', color: '#F44336' },
    { label: '重要', value: importantStructures, icon: '🟡', color: '#FF9800' },
    { label: '一般', value: generalStructures, icon: '⚪', color: '#9E9E9E' }
];

let statsHTML = '';
stats.forEach(stat => {
    statsHTML += `
        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); 
                    border-radius: 12px; border: 1px solid ${stat.color}20;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${stat.icon}</div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${stat.color}; margin-bottom: 4px;">
                ${stat.value}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500;">
                ${stat.label}
            </div>
        </div>
    `;
});

statsContainer.innerHTML = statsHTML;
this.container.appendChild(statsContainer);
```

---

<div style="text-align: center; margin-top: 30px; padding: 15px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 12px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600; margin-bottom: 6px;">
        🏗️ 结构化思维小贴士
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        结构笔记是知识的骨架，帮助您建立清晰的思维框架和知识体系
    </div>
</div>
