# Daily Log 番茄钟目标使用指南

## 📝 **Daily Log 文件头部设置**

在每日笔记的开头添加属性：

```yaml
---
tomato_goal: 10
---
```

## 🍅 **今日番茄钟复盘模板**

在Daily Log的适当位置添加：

```markdown
## 🍅 今日番茄钟复盘
- 目标：🍅 10
- 实际：🍅 8 
- 达成率：80%
- 分析：下午被会议打断，明天调整为8个目标

### 📊 时间分布
- 上午：🍅🍅🍅🍅 (4个)
- 下午：🍅🍅 (2个，被会议打断)
- 晚上：🍅🍅 (2个)

### 💡 改进建议
- 下午安排会议时间，避免打断专注时间
- 明天目标调整为8个番茄钟
- 尝试上午安排更多重要任务
```

## 🎯 **使用效果**

设置了`tomato_goal`属性后，任务仪表盘会：

1. **🎯 今日目标**卡片：显示你设定的目标数量
2. **🔥 今日完成**卡片：显示实际完成的数量
3. **目标达成率**：显示 `实际完成/目标 × 100%`
4. **详细信息**：显示 `8/10 🍅` 这样的进度

## 📅 **每日工作流程**

### 🌅 **早上规划**
1. 在Daily Log头部设置 `tomato_goal: 10`
2. 给任务标记预估番茄钟 `🍅3`
3. 查看任务仪表盘，确认今日目标

### 🔄 **执行过程**
1. 专注完成任务
2. 完成后更新实际番茄钟 `🍅3→5` 或 `🍅5`

### 🌙 **晚上复盘**
1. 查看番茄钟仪表盘的目标达成率
2. 在Daily Log中记录复盘内容
3. 分析未达成原因，调整明天目标

## 💡 **最佳实践建议**

### 🎯 **目标设定**
- 新手：每日6-8个番茄钟
- 熟练：每日8-12个番茄钟
- 根据个人情况和工作强度调整

### 📈 **持续改进**
- 记录每日达成率
- 分析未达成的原因
- 逐步提高预估准确性
- 找到最适合自己的目标数量

### 🔍 **数据分析**
- 周末回顾一周的达成率趋势
- 识别高效和低效的时间段
- 调整工作节奏和目标设定

## 🚀 **进阶技巧**

### 📊 **分时段目标**
```yaml
---
tomato_goal: 10
morning_goal: 4
afternoon_goal: 4
evening_goal: 2
---
```

### 🏷️ **项目目标**
```markdown
## 🍅 项目番茄钟分配
- 📁 项目A：🍅🍅🍅🍅 (4个)
- 📁 项目B：🍅🍅🍅 (3个)
- 📁 学习：🍅🍅 (2个)
- 📁 其他：🍅 (1个)
```

这样你就能更精细地管理时间分配了！🎉
