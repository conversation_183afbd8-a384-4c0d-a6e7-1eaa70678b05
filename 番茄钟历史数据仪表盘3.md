---
created: 2025-05-25T17:11
updated: 2025-05-25T17:11
---
# 🍅 番茄钟历史数据仪表盘

## 📊 **本周趋势分析**

```dataviewjs
// 数据处理辅助函数
function parseNumber(value) {
    if (value === null || value === undefined || value === '') return 0;
    if (typeof value === 'string') {
        const parsed = parseInt(value.replace(/[^\d]/g, ''));
        return isNaN(parsed) ? 0 : parsed;
    }
    return typeof value === 'number' ? value : 0;
}

// 获取本周数据
const now = new Date();
const dayOfWeek = now.getDay();
const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
const startOfWeek = new Date(now);
startOfWeek.setDate(now.getDate() - (adjustedDayOfWeek - 1));

const weekData = [];
const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

for (let i = 0; i < 7; i++) {
    const checkDate = new Date(startOfWeek);
    checkDate.setDate(startOfWeek.getDate() + i);
    const checkDateStr = checkDate.toISOString().split('T')[0];

    const dayLog = dv.pages('"0_Bullet Journal/Daily Notes"')
        .where(p => p.file.name.includes(checkDateStr))
        .first();

    // 安全地解析数据
    const goal = parseNumber(dayLog?.tomato_goal);
    const actual = parseNumber(dayLog?.tomato_actual);
    const rate = goal > 0 ? Math.round((actual / goal) * 100) : 0;

    weekData.push({
        date: checkDateStr,
        dayName: weekDays[i],
        goal: goal,
        actual: actual,
        rate: rate,
        isToday: checkDateStr === now.toISOString().split('T')[0],
        hasData: dayLog && (goal > 0 || actual > 0)
    });
}

// 显示本周趋势表格
const container = this.container;
container.innerHTML = '';

let tableHTML = `
    <h3 style="margin: 0 0 15px 0; color: #333;">📈 本周番茄钟趋势</h3>
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse; font-size: 0.9em;">
            <thead>
                <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">星期</th>
                    <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">日期</th>
                    <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">目标</th>
                    <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">完成</th>
                    <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">达成率</th>
                    <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">状态</th>
                </tr>
            </thead>
            <tbody>
`;

weekData.forEach(day => {
    const rate = day.rate;
    const status = day.hasData ?
        (rate >= 100 ? '🎉超额' :
         rate >= 80 ? '👍良好' :
         rate > 0 ? '💪努力' : '📝记录') : '⭕无记录';

    const rateColor = day.hasData ?
        (rate >= 100 ? '#51cf66' :
         rate >= 80 ? '#ffd43b' : '#ff6b6b') : '#999';

    const dayName = day.isToday ? `<strong>${day.dayName}</strong>` : day.dayName;
    const goalDisplay = day.hasData ? `🎯${day.goal}` : '🎯-';
    const actualDisplay = day.hasData ? `🍅${day.actual}` : '🍅-';
    const rateDisplay = day.hasData ? `${rate}%` : '-';

    tableHTML += `
        <tr style="border-bottom: 1px solid #dee2e6;">
            <td style="padding: 8px; font-weight: 500;">${dayName}</td>
            <td style="padding: 8px; text-align: center;">${day.date.slice(5)}</td>
            <td style="padding: 8px; text-align: center;">${goalDisplay}</td>
            <td style="padding: 8px; text-align: center;">${actualDisplay}</td>
            <td style="padding: 8px; text-align: center; color: ${rateColor}; font-weight: bold;">${rateDisplay}</td>
            <td style="padding: 8px; text-align: center;">${status}</td>
        </tr>
    `;
});

tableHTML += `
            </tbody>
        </table>
    </div>
`;

container.innerHTML = tableHTML;
```

## 📈 **月度统计与分析**

```dataviewjs
// 数据处理辅助函数
function parseNumber(value) {
    if (value === null || value === undefined || value === '') return 0;
    if (typeof value === 'string') {
        const parsed = parseInt(value.replace(/[^\d]/g, ''));
        return isNaN(parsed) ? 0 : parsed;
    }
    return typeof value === 'number' ? value : 0;
}

// 获取最近30天的数据
const today = new Date();
const monthData = [];
let totalGoal = 0;
let totalActual = 0;
let activeDays = 0;
let recordedDays = 0; // 有记录的天数（包括目标为0的天数）

for (let i = 29; i >= 0; i--) {
    const checkDate = new Date(today);
    checkDate.setDate(today.getDate() - i);
    const checkDateStr = checkDate.toISOString().split('T')[0];

    const dayLog = dv.pages('"0_Bullet Journal/Daily Notes"')
        .where(p => p.file.name.includes(checkDateStr))
        .first();

    if (dayLog) {
        // 使用安全的数据解析函数
        const goal = parseNumber(dayLog.tomato_goal);
        const actual = parseNumber(dayLog.tomato_actual);
        const rate = goal > 0 ? Math.round((actual / goal) * 100) : 0;

        // 只要有日记文件就算有记录
        recordedDays++;

        // 只有设置了目标或有实际完成数据才算活跃天数
        if (goal > 0 || actual > 0) {
            monthData.push({
                date: checkDateStr,
                goal: goal,
                actual: actual,
                rate: rate
            });

            totalGoal += goal;
            totalActual += actual;
            activeDays++;
        }
    }
}

// 计算统计数据
const avgGoal = activeDays > 0 ? Math.round(totalGoal / activeDays) : 0;
const avgActual = activeDays > 0 ? Math.round(totalActual / activeDays) : 0;
const overallRate = totalGoal > 0 ? Math.round((totalActual / totalGoal) * 100) : 0;

// 1. 显示月度统计
dv.header(3, "📊 最近30天统计");

const statsData = [
    ["📝 记录天数", `${recordedDays} 天`],
    ["📅 活跃天数", `${activeDays} 天`],
    ["🎯 总目标", `${totalGoal} 🍅`],
    ["✅ 总完成", `${totalActual} 🍅`],
    ["📊 平均目标", `${avgGoal} 🍅/天`],
    ["🔥 平均完成", `${avgActual} 🍅/天`],
    ["📈 总体达成率", `${overallRate}%`]
];

dv.table(["指标", "数值"], statsData);

// 2. 效率分析
const performanceData = monthData.filter(d => d.goal > 0);
const highPerformance = performanceData.filter(d => d.rate >= 90);
const mediumPerformance = performanceData.filter(d => d.rate >= 70 && d.rate < 90);
const lowPerformance = performanceData.filter(d => d.rate < 70);

dv.header(3, "🎯 效率分析报告");

const efficiencyData = [
    ["🌟 高效天数 (≥90%)", `${highPerformance.length} 天`],
    ["👍 中效天数 (70-89%)", `${mediumPerformance.length} 天`],
    ["💪 低效天数 (<70%)", `${lowPerformance.length} 天`]
];

dv.table(["效率等级", "天数"], efficiencyData);

// 最佳表现日期
if (highPerformance.length > 0) {
    const bestDays = highPerformance.slice(-5).map(d => d.date.slice(5)).join(', ');
    dv.paragraph(`🌟 **最佳表现日期**：${bestDays}`);
}

// 改进建议
let suggestions = [];
if (lowPerformance.length > highPerformance.length) {
    suggestions.push('考虑降低每日目标，设定更现实的期望');
}
if (avgActual < avgGoal * 0.8) {
    suggestions.push('分析任务被打断的原因，优化工作环境');
}
if (activeDays < 20) {
    suggestions.push('提高记录频率，养成每日复盘的习惯');
}

if (suggestions.length > 0) {
    dv.header(4, "💡 改进建议");
    suggestions.forEach(suggestion => {
        dv.paragraph(`- ${suggestion}`);
    });
}

// 3. 详细记录表
const tableContainer = this.container.createDiv();
tableContainer.style.cssText = `
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-top: 20px;
`;

if (monthData.length > 0) {
    let detailTableHTML = `
        <h3 style="margin: 0 0 15px 0; color: #333;">📋 最近记录详情</h3>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse; font-size: 0.9em;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">日期</th>
                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">目标</th>
                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">完成</th>
                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">达成率</th>
                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">状态</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // 显示最近10天的记录
    monthData.slice(-10).forEach(day => {
        const rateColor = day.rate >= 100 ? '#51cf66' :
                         day.rate >= 80 ? '#ffd43b' : '#ff6b6b';
        const status = day.rate >= 100 ? '🎉超额' :
                      day.rate >= 80 ? '👍良好' : '💪努力';

        detailTableHTML += `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 8px; font-weight: 500;">${day.date}</td>
                <td style="padding: 8px; text-align: center;">🎯${day.goal}</td>
                <td style="padding: 8px; text-align: center;">🍅${day.actual}</td>
                <td style="padding: 8px; text-align: center; color: ${rateColor}; font-weight: bold;">${day.rate}%</td>
                <td style="padding: 8px; text-align: center;">${status}</td>
            </tr>
        `;
    });

    detailTableHTML += `
                </tbody>
            </table>
        </div>
    `;

    tableContainer.innerHTML = detailTableHTML;
} else {
    tableContainer.innerHTML = `
        <h3 style="margin: 0 0 15px 0; color: #333;">📋 最近记录详情</h3>
        <div style="text-align: center; padding: 40px; color: #666;">
            <div style="font-size: 3em; margin-bottom: 15px;">📝</div>
            <p>暂无历史记录</p>
            <p style="font-size: 0.9em; color: #999;">请在Daily Log中设置番茄钟目标和实际完成数据</p>
        </div>
    `;
}

// 4. 数据状态提示
if (activeDays === 0) {
    dv.header(4, "🌱 开始您的番茄钟之旅");
    dv.paragraph("欢迎开始使用番茄钟技术！请在Daily Notes的YAML属性中添加：");
    dv.paragraph("- `tomato_goal: 8` （当日目标）");
    dv.paragraph("- `tomato_actual: 6` （实际完成）");
} else if (activeDays < 7) {
    dv.header(4, "📈 数据积累中");
    dv.paragraph(`很好！您已经记录了 ${activeDays} 天的番茄钟数据。继续保持，一周后将有更丰富的趋势分析。`);
} else {
    dv.header(4, "🎉 数据充足");
    dv.paragraph(`太棒了！您已经积累了 ${activeDays} 天的番茄钟数据，可以进行有意义的趋势分析了。`);
}
```

---

## 💡 **使用说明**

### 📊 **数据来源**
- 读取 `0_Bullet Journal/Daily Notes/` 中的所有日记文件
- 基于 YAML 属性：`tomato_goal`、`tomato_actual`、`tomato_rate`

### 🔧 **数据设置**
在每日日记文件的YAML front matter中添加以下属性：
```yaml
tomato_goal: 8        # 当日番茄钟目标数量
tomato_actual: 6      # 实际完成的番茄钟数量
tomato_rate: 75       # 达成率（可选，系统会自动计算）
```

### 🔄 **数据更新**
- 每次打开页面自动刷新数据
- 建议每日在 Daily Log 中更新实际完成数据
- 系统会自动处理数据类型不一致的问题

### 📈 **分析维度**
- **本周趋势**：7天详细对比，显示无数据的天数
- **月度统计**：30天整体表现，区分记录天数和活跃天数
- **效率分析**：高中低效天数分布
- **详细记录**：最近10天明细

### 🎯 **改进建议**
系统会根据你的数据自动生成个性化改进建议，帮助优化番茄钟使用效果。

### 📊 **数据说明**
- **记录天数**：有Daily Notes文件的天数（包括没有番茄钟数据的日期）
- **活跃天数**：实际记录了番茄钟数据的天数
- **历史数据**：以前没有记录番茄钟是正常的，系统只分析有数据的时间段
- **数据积累**：随着使用时间增长，分析结果会越来越准确
