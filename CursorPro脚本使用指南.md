# CursorPro脚本使用指南

## 脚本介绍

本文档介绍了两个用于获取和验证Cursor Pro推荐码的Python脚本：
1. **CursorPro脚本.py**：从Perplexity AI获取新的Cursor推荐码
2. **a.py**：验证已有Cursor推荐链接的有效性

## 脚本对比

| 特性 | CursorPro脚本.py | a.py |
|------|-----------------|------|
| 主要功能 | 获取新推荐码 | 验证已有推荐码 |
| 输入 | 无需输入文件 | 需要"Cursor 再来1.5k个.txt"输入文件 |
| 输出 | 控制台打印推荐码 | 保存有效链接到"有效.txt" |
| 处理方式 | 单线程 | 多线程(最多50个) |
| 复杂度 | 简单 | 复杂 |

## 环境准备

1. 安装Python（如未安装）
   - 访问[Python官网](https://www.python.org/downloads/)下载安装
   - 安装时勾选"Add Python to PATH"

2. 安装必要的依赖库
   ```
   pip install requests
   ```

## 使用CursorPro脚本.py

### 步骤详解

1. **打开命令行工具**
   - 按`Win+R`，输入`powershell`或`cmd`并按回车
   - 或在开始菜单搜索"PowerShell"/"命令提示符"

2. **导航到脚本所在目录**
   ```
   cd "C:\脚本路径"
   ```
   ⚠️ 注意：
   - `cd`与路径之间需要有空格
   - 路径包含中文或特殊字符时需要使用引号

3. **运行脚本**
   ```
   python "CursorPro脚本.py"
   ```

4. **获取推荐码**
   - 脚本会自动运行并尝试获取推荐码
   - 成功时会在控制台显示`Code: XXXXX`
   - 脚本默认运行1000次，每次间隔0.5秒(约8-9分钟)
   - 可按`Ctrl+C`随时终止

5. **使用推荐码**
   - 复制获得的推荐码
   - 访问[Cursor官网](https://cursor.com/)
   - 在适当位置输入推荐码激活Pro版本

## 使用a.py

### 准备工作

1. **创建输入文件**
   - 在脚本同目录下创建"Cursor 再来1.5k个.txt"文本文件
   - 在文件中粘贴要验证的Cursor推荐链接（每行一个或多个）

### 步骤详解

1. **打开命令行工具**（同上）

2. **导航到脚本所在目录**（同上）

3. **运行脚本**
   ```
   python a.py
   ```
   ⚠️ 注意：
   - 如文件名只有英文字母，可以不加引号
   - 确保输入文件已准备好

4. **查看结果**
   - 脚本会显示处理进度："进度: x/总数, 有效链接: y, 剩余: z"
   - 处理完成后，有效链接保存到"有效.txt"
   - 控制台会显示执行时间和有效链接数量

## 常见问题与解决方法

### 1. 找不到文件
```
Python can't open file 'xxx': [Errno 2] No such file or directory
```
**解决方法**：
- 确认文件名包含扩展名(.py)
- 使用`dir`命令确认文件是否在当前目录
- 检查路径和文件名拼写是否正确

### 2. 命令不被识别
```
'xxx' is not recognized as a name of a cmdlet, function...
```
**解决方法**：
- 确保命令格式正确，特别是空格的使用
- 命令示例：`cd "路径"` 而非 `cd"路径"`

### 3. 模块导入错误
```
ModuleNotFoundError: No module named 'xxx'
```
**解决方法**：
- 安装缺失的库：`pip install xxx`
- 对于本脚本，确保已安装requests：`pip install requests`

### 4. 脚本长时间无输出
**解决方法**：
- CursorPro脚本.py可能需要多次尝试才能找到有效推荐码
- 网站可能已更新或添加防护措施
- 可按`Ctrl+C`终止并修改脚本减少尝试次数

## 注意事项

1. **路径与文件名**
   - 包含中文、空格或特殊字符的路径/文件名需用引号包围
   - 简单英文字母数字组合可不用引号

2. **脚本选择**
   - 获取新推荐码：使用CursorPro脚本.py
   - 验证已有推荐码：使用a.py

3. **执行环境**
   - 两个脚本均可在PowerShell或CMD中运行
   - 命令语法在不同终端中基本相同

4. **脚本有效性**
   - 网站结构更新可能导致脚本失效
   - 如长时间无结果，可能需要更新脚本或寻找其他方法 