# 邮箱验证码接码查询系统

这是一个简单的邮箱验证码接码查询系统，用于查询域名注册等服务发送到特定邮箱的验证码。

## 功能特点

- 邮箱验证码查询
- 验证码管理（添加新验证码）
- 简洁美观的界面
- 响应式设计，适配移动设备

## 系统组件

- `email_verification_code_query.html`: 前端查询页面
- `email_verification_code_admin.html`: 管理员添加验证码页面
- `email_verification_code_api.py`: 后端API服务
- `verification_codes.db`: SQLite数据库（自动创建）

## 安装和使用

### 环境要求

- Python 3.6+
- Flask
- Flask-CORS

### 安装依赖

```bash
pip install flask flask-cors
```

### 运行服务

1. 启动后端API服务：

```bash
python email_verification_code_api.py
```

2. 在浏览器中打开前端页面：

```
email_verification_code_query.html
```

### 使用说明

#### 查询验证码

1. 在查询页面输入邮箱地址
2. 点击"查询"按钮
3. 系统将显示与该邮箱关联的最新验证码（如果存在）

#### 管理验证码

1. 在浏览器中打开管理页面：`email_verification_code_admin.html`
2. 输入管理密码（默认为：admin123）
3. 在管理面板中填写邮箱、验证码和服务名称
4. 点击"添加验证码"按钮

## 安全说明

- 本系统仅用于演示目的，实际应用中应加强安全措施
- 管理页面应使用更安全的身份验证机制
- 生产环境中应使用HTTPS保护数据传输
- 验证码应加密存储

## 自定义配置

可以修改以下配置：

- `email_verification_code_api.py` 中的 `DB_PATH` 变量可以更改数据库文件路径
- 前端页面中的API地址可以根据实际部署情况进行调整
- 管理页面中的默认密码应该更改为更安全的密码

## 示例数据

系统初始化时会自动创建两条测试数据：

- 邮箱：<EMAIL>，验证码：123456
- 邮箱：<EMAIL>，验证码：654321

可以使用这些测试数据来验证系统功能。 