# 🤖 Obsidian项目仪表盘 - AI助手Prompt模板

> 与AI助手高效沟通的专业Prompt模板集合

## 📋 目录

- [基础问题模板](#-基础问题模板)
- [功能开发模板](#-功能开发模板)
- [故障排除模板](#-故障排除模板)
- [定制化需求模板](#-定制化需求模板)
- [性能优化模板](#-性能优化模板)
- [集成扩展模板](#-集成扩展模板)

## 🔧 基础问题模板

### 环境配置问题

```
我在配置Obsidian项目仪表盘时遇到问题，请帮我分析：

**环境信息：**
- Obsidian版本：[如：v1.4.16]
- Dataview版本：[如：v0.5.64]
- 操作系统：[如：Windows 11 / macOS 13.0 / Ubuntu 22.04]
- 其他相关插件：[如：Templater, Calendar等]

**问题描述：**
[详细描述遇到的问题，如：仪表盘显示空白、插件无法启用等]

**已尝试的解决方案：**
[列出已经尝试过的方法]

**期望结果：**
[描述希望达到的效果]

请提供详细的解决步骤和注意事项。
```

### 使用方法咨询

```
我想了解Obsidian项目仪表盘的具体使用方法：

**具体需求：**
[如：如何创建新项目、如何设置项目状态、如何使用筛选功能等]

**当前情况：**
[描述当前的使用状态和遇到的困惑]

**使用场景：**
[说明您的具体使用场景，如：个人项目管理、团队协作、学习计划等]

请提供详细的操作步骤和最佳实践建议。
```

## 🚀 功能开发模板

### 新功能请求

```
我想为Obsidian项目仪表盘添加新功能：

**功能名称：**
[功能的简短名称，如：项目甘特图、时间统计、团队视图等]

**功能描述：**
[详细描述功能的作用和行为]
- 主要功能：[核心功能说明]
- 交互方式：[用户如何操作]
- 数据来源：[从哪里获取数据]
- 显示效果：[如何展示给用户]

**使用场景：**
[说明在什么情况下会使用这个功能]

**设计要求：**
- 视觉风格：[保持治愈系奶茶风 / 其他特殊要求]
- 响应式设计：[是否需要适配不同屏幕尺寸]
- 性能要求：[对加载速度、数据量的要求]

**参考示例：**
[如果有类似的功能或设计，请提供参考链接或描述]

**技术偏好：**
[如：纯JavaScript实现、使用特定图表库、集成特定插件等]

请帮我设计并实现这个功能，包括完整的代码和使用说明。
```

### 代码优化请求

```
我想优化现有的项目仪表盘代码：

**优化目标：**
[如：提高性能、改善用户体验、增强可维护性、减少代码重复等]

**当前代码：**
```javascript
[粘贴需要优化的代码片段]
```

**遇到的问题：**
[如：加载缓慢、内存占用高、代码难以维护、功能不稳定等]

**性能数据：**
[如果有的话，提供当前的性能数据]

**优化要求：**
- 兼容性：[需要保持的兼容性要求]
- 功能完整性：[不能影响的现有功能]
- 代码风格：[遵循的编码规范]

请提供优化后的代码和改进说明。
```

## 🔍 故障排除模板

### 错误诊断

```
我的项目仪表盘出现错误，需要诊断和修复：

**错误现象：**
[详细描述错误的表现，如：页面崩溃、功能失效、显示异常等]

**错误信息：**
[如果有错误提示，请完整复制]
```
[错误信息内容]
```

**复现步骤：**
1. [第一步操作]
2. [第二步操作]
3. [出现错误的步骤]

**环境信息：**
- Obsidian版本：[版本号]
- 相关插件版本：[插件版本信息]
- 浏览器（如果相关）：[浏览器版本]

**相关代码：**
```javascript
[如果怀疑是特定代码导致的问题，请粘贴相关代码]
```

**最近的更改：**
[列出最近对仪表盘或相关文件的修改]

请帮我分析错误原因并提供修复方案。
```

### 兼容性问题

```
我遇到了兼容性问题：

**问题描述：**
[如：在某个版本的Obsidian中无法正常工作、与其他插件冲突等]

**环境对比：**
- 工作正常的环境：[详细信息]
- 出现问题的环境：[详细信息]

**具体表现：**
[详细描述兼容性问题的具体表现]

**冲突插件（如果有）：**
[列出可能冲突的插件]

请提供兼容性解决方案。
```

## 🎨 定制化需求模板

### 主题定制

```
我想定制项目仪表盘的主题：

**设计风格：**
[如：Morandi风格、深色主题、简约风格、商务风格等]

**色彩偏好：**
- 主色调：[如：#FF6B6B、蓝色系、绿色系等]
- 辅助色：[配色方案]
- 强调色：[用于突出重要信息]

**具体要求：**
- 背景样式：[渐变、纯色、图案等]
- 按钮样式：[圆角、方形、扁平、立体等]
- 字体样式：[字体族、大小、粗细等]
- 动画效果：[保留、简化、增强等]

**参考设计：**
[如果有参考的设计或网站，请提供链接或描述]

**适用场景：**
[如：个人使用、团队展示、客户演示等]

请帮我创建这个主题的完整CSS代码。
```

### 布局调整

```
我想调整项目仪表盘的布局：

**当前布局问题：**
[描述现有布局的不足之处]

**期望布局：**
[详细描述希望的布局方式]
- 组件排列：[如：上下排列改为左右排列]
- 尺寸比例：[各部分的大小比例]
- 响应式要求：[不同屏幕尺寸的适配]

**特殊要求：**
[如：固定某些组件位置、添加侧边栏、调整卡片大小等]

**使用设备：**
[主要使用的设备类型：桌面、平板、手机等]

请提供布局调整的代码和说明。
```

## ⚡ 性能优化模板

### 性能问题分析

```
我的项目仪表盘存在性能问题：

**性能表现：**
- 加载时间：[如：首次加载需要5秒]
- 响应延迟：[如：点击按钮后2秒才响应]
- 内存使用：[如果能测量的话]
- CPU占用：[如果能观察到的话]

**数据规模：**
- 项目数量：[如：100个项目]
- 文件大小：[如：每个项目文件平均大小]
- 查询复杂度：[如：多层嵌套查询]

**使用环境：**
- 设备配置：[CPU、内存等]
- Obsidian库大小：[文件数量、总大小]

**期望目标：**
[如：加载时间控制在2秒内、支持500个项目等]

请分析性能瓶颈并提供优化方案。
```

### 大数据量处理

```
我需要处理大量项目数据：

**数据规模：**
- 项目数量：[具体数字]
- 预期增长：[未来可能的数据量]
- 查询频率：[用户操作频率]

**当前问题：**
[如：加载缓慢、界面卡顿、内存不足等]

**性能要求：**
- 加载时间：[期望的加载时间]
- 响应速度：[期望的操作响应时间]
- 内存使用：[内存使用限制]

**功能需求：**
[哪些功能必须保留，哪些可以简化]

请提供大数据量处理的解决方案。
```

## 🔗 集成扩展模板

### 插件集成

```
我想将项目仪表盘与其他Obsidian插件集成：

**目标插件：**
[如：Calendar、Kanban、Charts、Templater等]

**集成目标：**
[描述希望实现的集成效果]
- 数据共享：[如何在插件间共享数据]
- 功能联动：[如何实现功能的相互调用]
- 界面整合：[如何在界面上整合显示]

**具体需求：**
[详细描述集成的具体需求和期望效果]

**技术限制：**
[已知的技术限制或约束条件]

请提供集成方案和实现代码。
```

### 外部工具集成

```
我想将项目仪表盘与外部工具集成：

**目标工具：**
[如：GitHub、Notion、Trello、Google Calendar等]

**集成方式：**
[如：API调用、数据导入导出、Webhook等]

**数据流向：**
[描述数据如何在系统间流动]

**同步需求：**
[如：实时同步、定时同步、手动同步等]

**安全要求：**
[对数据安全和隐私的要求]

请提供集成方案和实现指导。
```

## 💡 使用技巧

### 提问技巧

1. **具体明确** - 提供具体的问题描述和期望结果
2. **信息完整** - 包含必要的环境信息和错误信息
3. **逐步描述** - 按照操作步骤详细描述问题
4. **提供代码** - 粘贴相关的代码片段
5. **说明场景** - 描述具体的使用场景和需求

### 沟通建议

1. **保持耐心** - 复杂问题可能需要多轮沟通
2. **及时反馈** - 尝试建议后及时反馈结果
3. **详细测试** - 按照建议进行完整测试
4. **记录过程** - 记录解决过程以备后用

## 📞 获取更多帮助

如果这些模板无法满足您的需求，可以：

1. **查看完整文档** - `项目仪表盘完整文档.md`
2. **参考快速指南** - `快速开始指南.md`
3. **访问社区论坛** - Obsidian官方论坛
4. **提交GitHub Issue** - 在项目仓库提交问题

---

## 📝 实用示例

### 示例1：添加项目优先级筛选

```
我想为项目仪表盘添加优先级筛选功能：

**功能名称：**
项目优先级筛选器

**功能描述：**
- 主要功能：在快速筛选区域添加优先级筛选按钮（高、中、低优先级）
- 交互方式：点击按钮显示对应优先级的项目
- 数据来源：项目文件的priority字段
- 显示效果：与现有筛选按钮保持一致的治愈系风格

**使用场景：**
当项目较多时，需要快速查看高优先级项目进行重点关注

**设计要求：**
- 视觉风格：保持治愈系奶茶风格
- 按钮颜色：高优先级用红色系，中优先级用黄色系，低优先级用绿色系
- 响应式设计：需要适配不同屏幕尺寸

**技术偏好：**
使用现有的筛选框架，添加新的筛选条件

请帮我实现这个功能。
```

### 示例2：性能优化请求

```
我的项目仪表盘加载缓慢，需要优化：

**性能表现：**
- 加载时间：首次加载需要8秒
- 响应延迟：点击筛选按钮后3秒才显示结果
- 项目数量：当前有150个项目

**当前代码：**
```javascript
// 每次都重新查询所有项目
const projects = dv.pages('"6_Project Notes"')
    .where(p => p.tags && p.tags.includes("type/project"));

// 每个项目都重新计算复杂的时间信息
projects.forEach(project => {
    // 复杂的日期计算...
});
```

**遇到的问题：**
- 每次筛选都重新查询和计算所有数据
- 大量DOM操作导致界面卡顿
- 没有缓存机制

**优化要求：**
- 兼容性：保持与当前Dataview版本的兼容
- 功能完整性：不能影响现有的筛选和显示功能
- 目标性能：加载时间控制在3秒内

请提供性能优化方案。
```

### 示例3：主题定制请求

```
我想创建一个深色主题版本：

**设计风格：**
现代深色主题，保持治愈系的温暖感

**色彩偏好：**
- 主色调：深灰色 (#2D2D2D)
- 辅助色：暖灰色 (#3A3A3A)
- 强调色：温暖的橙色 (#FF8C42)

**具体要求：**
- 背景样式：深色渐变背景
- 按钮样式：保持圆角设计，调整为深色适配
- 字体样式：浅色字体，保持良好对比度
- 动画效果：保留现有的悬浮动画

**适用场景：**
夜间使用，减少眼部疲劳

请帮我创建深色主题的CSS代码。
```

## 🎯 高级Prompt技巧

### 1. 分步骤请求

```
我想分步骤实现一个复杂功能：

**第一步：** 请先帮我设计数据结构
[描述需要的数据结构]

**第二步：** 基于数据结构实现基础查询
[描述查询需求]

**第三步：** 添加UI界面
[描述界面需求]

**第四步：** 实现交互功能
[描述交互需求]

请先完成第一步，我确认后再进行下一步。
```

### 2. 对比分析请求

```
请帮我分析两种实现方案的优劣：

**方案A：**
[描述方案A的实现方式]

**方案B：**
[描述方案B的实现方式]

**对比维度：**
- 性能表现
- 代码复杂度
- 维护难度
- 扩展性
- 用户体验

**我的使用场景：**
[描述具体的使用场景和约束条件]

请提供详细的对比分析和推荐方案。
```

### 3. 渐进式改进请求

```
我想渐进式改进现有功能：

**当前版本：**
[描述当前功能的实现]

**改进目标：**
[描述希望达到的最终效果]

**改进路径：**
请帮我设计一个渐进式的改进路径，每个版本都能独立工作，逐步接近最终目标。

**约束条件：**
- 每次改进不能影响现有功能
- 改进过程中用户可以正常使用
- 每个版本的改进幅度适中

请提供详细的改进计划和每个版本的实现方案。
```

## 📚 学习型Prompt

### 代码学习请求

```
我想深入理解项目仪表盘的实现原理：

**学习目标：**
[如：理解DataviewJS的工作机制、掌握动态DOM操作、学习事件处理等]

**当前水平：**
[描述您当前的技术水平]

**重点关注：**
[您最想了解的技术点]

**学习方式偏好：**
[如：通过注释详细解释、提供简化示例、对比不同实现方式等]

请帮我深入理解相关技术，并提供学习建议。
```

### 最佳实践咨询

```
我想了解项目仪表盘开发的最佳实践：

**关注领域：**
[如：代码组织、性能优化、用户体验、可维护性等]

**具体问题：**
[列出您关心的具体问题]

**应用场景：**
[描述您的应用场景]

请分享相关的最佳实践和经验建议。
```

## 🔄 迭代优化Prompt

### 功能迭代

```
基于用户反馈，我想迭代优化现有功能：

**用户反馈：**
[列出收到的用户反馈]

**当前实现：**
[描述当前的实现方式]

**改进方向：**
[基于反馈确定的改进方向]

**优先级：**
[按重要性排序的改进项目]

请帮我制定迭代优化方案。
```

### A/B测试设计

```
我想设计A/B测试来验证改进效果：

**测试目标：**
[要验证的假设或改进效果]

**测试方案：**
- 方案A：[当前实现]
- 方案B：[改进方案]

**评估指标：**
[如何衡量改进效果]

**测试环境：**
[测试的环境和条件]

请帮我设计详细的A/B测试方案。
```

## 📞 获取更多帮助

如果这些模板无法满足您的需求，可以：

1. **查看完整文档** - `项目仪表盘完整文档.md`
2. **参考快速指南** - `快速开始指南.md`
3. **访问社区论坛** - Obsidian官方论坛
4. **提交GitHub Issue** - 在项目仓库提交问题

---

## 📝 实用示例

### 示例1：添加项目优先级筛选

```
我想为项目仪表盘添加优先级筛选功能：

**功能名称：**
项目优先级筛选器

**功能描述：**
- 主要功能：在快速筛选区域添加优先级筛选按钮（高、中、低优先级）
- 交互方式：点击按钮显示对应优先级的项目
- 数据来源：项目文件的priority字段
- 显示效果：与现有筛选按钮保持一致的治愈系风格

**使用场景：**
当项目较多时，需要快速查看高优先级项目进行重点关注

**设计要求：**
- 视觉风格：保持治愈系奶茶风格
- 按钮颜色：高优先级用红色系，中优先级用黄色系，低优先级用绿色系
- 响应式设计：需要适配不同屏幕尺寸

**技术偏好：**
使用现有的筛选框架，添加新的筛选条件

请帮我实现这个功能。
```

### 示例2：性能优化请求

```
我的项目仪表盘加载缓慢，需要优化：

**性能表现：**
- 加载时间：首次加载需要8秒
- 响应延迟：点击筛选按钮后3秒才显示结果
- 项目数量：当前有150个项目

**当前代码：**
```javascript
// 每次都重新查询所有项目
const projects = dv.pages('"6_Project Notes"')
    .where(p => p.tags && p.tags.includes("type/project"));

// 每个项目都重新计算复杂的时间信息
projects.forEach(project => {
    // 复杂的日期计算...
});
```

**遇到的问题：**
- 每次筛选都重新查询和计算所有数据
- 大量DOM操作导致界面卡顿
- 没有缓存机制

**优化要求：**
- 兼容性：保持与当前Dataview版本的兼容
- 功能完整性：不能影响现有的筛选和显示功能
- 目标性能：加载时间控制在3秒内

请提供性能优化方案。
```

### 示例3：主题定制请求

```
我想创建一个深色主题版本：

**设计风格：**
现代深色主题，保持治愈系的温暖感

**色彩偏好：**
- 主色调：深灰色 (#2D2D2D)
- 辅助色：暖灰色 (#3A3A3A)
- 强调色：温暖的橙色 (#FF8C42)

**具体要求：**
- 背景样式：深色渐变背景
- 按钮样式：保持圆角设计，调整为深色适配
- 字体样式：浅色字体，保持良好对比度
- 动画效果：保留现有的悬浮动画

**适用场景：**
夜间使用，减少眼部疲劳

请帮我创建深色主题的CSS代码。
```

## 🎯 高级Prompt技巧

### 1. 分步骤请求

```
我想分步骤实现一个复杂功能：

**第一步：** 请先帮我设计数据结构
[描述需要的数据结构]

**第二步：** 基于数据结构实现基础查询
[描述查询需求]

**第三步：** 添加UI界面
[描述界面需求]

**第四步：** 实现交互功能
[描述交互需求]

请先完成第一步，我确认后再进行下一步。
```

### 2. 对比分析请求

```
请帮我分析两种实现方案的优劣：

**方案A：**
[描述方案A的实现方式]

**方案B：**
[描述方案B的实现方式]

**对比维度：**
- 性能表现
- 代码复杂度
- 维护难度
- 扩展性
- 用户体验

**我的使用场景：**
[描述具体的使用场景和约束条件]

请提供详细的对比分析和推荐方案。
```

### 3. 渐进式改进请求

```
我想渐进式改进现有功能：

**当前版本：**
[描述当前功能的实现]

**改进目标：**
[描述希望达到的最终效果]

**改进路径：**
请帮我设计一个渐进式的改进路径，每个版本都能独立工作，逐步接近最终目标。

**约束条件：**
- 每次改进不能影响现有功能
- 改进过程中用户可以正常使用
- 每个版本的改进幅度适中

请提供详细的改进计划和每个版本的实现方案。
```

## 📚 学习型Prompt

### 代码学习请求

```
我想深入理解项目仪表盘的实现原理：

**学习目标：**
[如：理解DataviewJS的工作机制、掌握动态DOM操作、学习事件处理等]

**当前水平：**
[描述您当前的技术水平]

**重点关注：**
[您最想了解的技术点]

**学习方式偏好：**
[如：通过注释详细解释、提供简化示例、对比不同实现方式等]

请帮我深入理解相关技术，并提供学习建议。
```

### 最佳实践咨询

```
我想了解项目仪表盘开发的最佳实践：

**关注领域：**
[如：代码组织、性能优化、用户体验、可维护性等]

**具体问题：**
[列出您关心的具体问题]

**应用场景：**
[描述您的应用场景]

请分享相关的最佳实践和经验建议。
```

## 🔄 迭代优化Prompt

### 功能迭代

```
基于用户反馈，我想迭代优化现有功能：

**用户反馈：**
[列出收到的用户反馈]

**当前实现：**
[描述当前的实现方式]

**改进方向：**
[基于反馈确定的改进方向]

**优先级：**
[按重要性排序的改进项目]

请帮我制定迭代优化方案。
```

### A/B测试设计

```
我想设计A/B测试来验证改进效果：

**测试目标：**
[要验证的假设或改进效果]

**测试方案：**
- 方案A：[当前实现]
- 方案B：[改进方案]

**评估指标：**
[如何衡量改进效果]

**测试环境：**
[测试的环境和条件]

请帮我设计详细的A/B测试方案。
```

## 📞 获取更多帮助

如果这些模板无法满足您的需求，可以：

1. **查看完整文档** - `项目仪表盘完整文档.md`
2. **参考快速指南** - `快速开始指南.md`
3. **访问社区论坛** - Obsidian官方论坛
4. **提交GitHub Issue** - 在项目仓库提交问题

---

🤖 **愿这些模板帮助您与AI助手更高效地沟通！**

*记住：好的Prompt是成功沟通的一半，详细、具体、有条理的描述能帮助AI更好地理解您的需求。*
