---
created: 2025-05-25T16:00
updated: 2025-05-25T16:00
tags:
  - type/dashboard
  - project/browser
---

# 📁 项目笔记浏览器

> 🎯 浏览和管理所有项目文件，快速访问项目资源

## 🚀 快速操作

```dataviewjs
// 创建快速操作面板
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F0F8FF 0%, #F8F5FF 100%);
    border-radius: 16px;
    border: 2px solid rgba(165, 214, 232, 0.3);
`;

const quickActions = [
    {
        title: '新建项目',
        icon: '➕',
        desc: '创建新项目',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates/Project Template', '', false);
            }
        },
        color: '#A5D6E8'
    },
    {
        title: '项目仪表盘',
        icon: '📊',
        desc: '查看项目总览',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('项目仪表盘', '', false);
            }
        },
        color: '#B8D6B8'
    },
    {
        title: '活跃项目',
        icon: '🔥',
        desc: '进行中的项目',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('项目仪表盘', '', false);
            }
        },
        color: '#E8D5A5'
    },
    {
        title: '项目模板',
        icon: '📋',
        desc: '查看项目模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates', '', false);
            }
        },
        color: '#E8B4A0'
    }
];

let actionsHTML = '';
quickActions.forEach((action, index) => {
    actionsHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 12px; padding: 18px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action-btn" data-index="${index}">
            <div style="font-size: 2em; margin-bottom: 8px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickActionsContainer.innerHTML = actionsHTML;

// 添加点击事件
setTimeout(() => {
    const actionBtns = quickActionsContainer.querySelectorAll('.quick-action-btn');
    actionBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            quickActions[index].action();
        });

        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 📋 所有项目文件

```dataview
TABLE WITHOUT ID
    file.link AS "📁 项目名称",
    choice(Status = "active", "🟢 进行中", 
           choice(Status = "completed", "✅ 已完成", 
                  choice(Status = "cancelled", "❌ 已取消", 
                         choice(Status = "risk", "🔴 风险", "⚪ 未设置")))) AS "状态",
    choice(Area, Area, "📝 未分类") AS "领域",
    choice(progress, progress + "%", "0%") AS "进度",
    file.mtime AS "📅 修改时间"
FROM "6_Project Notes"
WHERE file.name != "README"
SORT file.mtime DESC
```

## 🎯 按状态分类

### 🟢 进行中的项目

```dataview
TABLE WITHOUT ID
    file.link AS "项目",
    choice(Goal, Goal, "未设置目标") AS "目标",
    choice(progress, progress + "%", "0%") AS "进度",
    file.mtime AS "更新时间"
FROM "6_Project Notes"
WHERE Status = "active"
SORT file.mtime DESC
LIMIT 10
```

### ✅ 已完成的项目

```dataview
TABLE WITHOUT ID
    file.link AS "项目",
    choice(Goal, Goal, "未设置目标") AS "目标",
    choice(end_date, end_date, "未设置") AS "完成日期"
FROM "6_Project Notes"
WHERE Status = "completed"
SORT file.mtime DESC
LIMIT 5
```

### 🔴 风险项目

```dataview
TABLE WITHOUT ID
    file.link AS "项目",
    choice(Goal, Goal, "未设置目标") AS "目标",
    choice(progress, progress + "%", "0%") AS "进度",
    choice(end_date, end_date, "未设置") AS "截止日期"
FROM "6_Project Notes"
WHERE Status = "risk"
SORT file.mtime DESC
```

## 📊 项目统计

```dataviewjs
// 获取所有项目文件
const allProjects = dv.pages('"6_Project Notes"').where(p => p.file.name !== "README");

// 统计数据
const totalProjects = allProjects.length;
const activeProjects = allProjects.where(p => p.Status === "active").length;
const completedProjects = allProjects.where(p => p.Status === "completed").length;
const riskProjects = allProjects.where(p => p.Status === "risk").length;
const cancelledProjects = allProjects.where(p => p.Status === "cancelled").length;

// 创建统计容器
const statsContainer = document.createElement('div');
statsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #FFF8F5 0%, #F8FFF8 100%);
    border-radius: 16px;
    border: 2px solid rgba(232, 180, 160, 0.2);
`;

const stats = [
    { label: '总项目', value: totalProjects, icon: '📁', color: '#8B7355' },
    { label: '进行中', value: activeProjects, icon: '🟢', color: '#4CAF50' },
    { label: '已完成', value: completedProjects, icon: '✅', color: '#2196F3' },
    { label: '风险', value: riskProjects, icon: '🔴', color: '#F44336' },
    { label: '已取消', value: cancelledProjects, icon: '❌', color: '#9E9E9E' }
];

let statsHTML = '';
stats.forEach(stat => {
    statsHTML += `
        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); 
                    border-radius: 12px; border: 1px solid ${stat.color}20;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${stat.icon}</div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${stat.color}; margin-bottom: 4px;">
                ${stat.value}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500;">
                ${stat.label}
            </div>
        </div>
    `;
});

statsContainer.innerHTML = statsHTML;
this.container.appendChild(statsContainer);
```

## 🔍 按领域分类

```dataview
TABLE WITHOUT ID
    Area AS "🏷️ 领域",
    length(rows.file.link) AS "📊 项目数量",
    join(rows.file.link, ", ") AS "📁 项目列表"
FROM "6_Project Notes"
WHERE Area AND file.name != "README"
GROUP BY Area
SORT length(rows.file.link) DESC
```

---

<div style="text-align: center; margin-top: 30px; padding: 15px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 12px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600; margin-bottom: 6px;">
        🎯 项目管理小贴士
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        定期回顾项目进度，及时调整计划，保持项目健康状态
    </div>
</div>
