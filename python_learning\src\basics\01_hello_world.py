#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Python基础语法示例
包含变量、数据类型、运算符等基础概念
"""

def main():
    # 1. 变量和基本数据类型
    name = "Python学习者"  # 字符串
    age = 25              # 整数
    height = 1.75         # 浮点数
    is_student = True     # 布尔值
    
    # 2. 打印变量
    print("=== 基本数据类型示例 ===")
    print(f"姓名: {name}")
    print(f"年龄: {age}")
    print(f"身高: {height}米")
    print(f"是否学生: {is_student}")
    
    # 3. 基本运算
    print("\n=== 基本运算示例 ===")
    a = 10
    b = 3
    print(f"{a} + {b} = {a + b}")  # 加法
    print(f"{a} - {b} = {a - b}")  # 减法
    print(f"{a} * {b} = {a * b}")  # 乘法
    print(f"{a} / {b} = {a / b}")  # 除法
    print(f"{a} // {b} = {a // b}")  # 整除
    print(f"{a} % {b} = {a % b}")  # 取余
    print(f"{a} ** {b} = {a ** b}")  # 幂运算
    
    # 4. 字符串操作
    print("\n=== 字符串操作示例 ===")
    greeting = "Hello, Python!"
    print(f"原始字符串: {greeting}")
    print(f"大写: {greeting.upper()}")
    print(f"小写: {greeting.lower()}")
    print(f"长度: {len(greeting)}")
    print(f"切片[0:5]: {greeting[0:5]}")
    
    # 5. 条件语句
    print("\n=== 条件语句示例 ===")
    score = 85
    if score >= 90:
        grade = "优秀"
    elif score >= 80:
        grade = "良好"
    elif score >= 60:
        grade = "及格"
    else:
        grade = "不及格"
    print(f"分数: {score}, 等级: {grade}")
    
    # 6. 循环语句
    print("\n=== 循环语句示例 ===")
    print("for循环:")
    for i in range(1, 4):
        print(f"  第{i}次循环")
    
    print("\nwhile循环:")
    count = 1
    while count <= 3:
        print(f"  第{count}次循环")
        count += 1

if __name__ == "__main__":
    main() 