---
tags:
  - type/dashboard
  - dashboard/task
created: 2025-05-16T17:00
updated: 2025-05-24T16:21
---

# 📋 任务仪表盘 - 简化版

> 简洁实用的任务管理系统

## 📊 任务看板

```dataviewjs
// 使用固定的当前时间确保一致性
const baseTime = new Date();

// 获取所有任务
const allTasks = dv.pages().file.tasks;
const today = baseTime.toISOString().split('T')[0];
const tomorrow = new Date(baseTime);
tomorrow.setDate(baseTime.getDate() + 1);
const tomorrowStr = tomorrow.toISOString().split('T')[0];

// 计算本周范围（中国标准：周一开始，周日结束）
const dayOfWeek = baseTime.getDay();
// 将周日(0)转换为7，这样周一就是1，周日是7
const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
const startOfWeek = new Date(baseTime);
startOfWeek.setDate(baseTime.getDate() - (adjustedDayOfWeek - 1));
const endOfWeek = new Date(startOfWeek);
endOfWeek.setDate(startOfWeek.getDate() + 6);
const startDate = startOfWeek.toISOString().split('T')[0];
const endDate = endOfWeek.toISOString().split('T')[0];

// 统计各类任务
let stats = {
    overdue: 0,      // 逾期
    inProgress: 0,   // 进行中
    waiting: 0,      // 待办
    today: 0,        // 今天
    tomorrow: 0,     // 明天
    thisWeek: 0,     // 一周内
    future: 0,       // 未来
    completed: 0     // 已完成
};

// 调试信息 - 在界面上显示
let debugInfo = `
📅 调试信息：
- 今天: ${today}
- 明天: ${tomorrowStr}
- 本周开始: ${startDate}
- 本周结束: ${endDate}
- 任务详情:
`;

let taskCount = 0;

allTasks.forEach(task => {
    if (task.completed) {
        stats.completed++;
        return;
    }

    // 首先检查是否有进行中标记 - 进行中任务优先级最高
    const isInProgress = task.text.includes('🔄') ||
        task.text.includes('进行中');

    if (isInProgress) {
        stats.inProgress++;
        return; // 进行中任务不再按日期分类
    }

    // 支持多种日期格式：📅 2025-05-25, due: 2025-05-25, [due:: 2025-05-25]
    const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);

    if (!dueDateMatch) {
        stats.waiting++;
        return;
    }

    const dueDate = dueDateMatch[1];

    // 记录任务详情
    taskCount++;
    if (taskCount <= 10) { // 只显示前10个任务避免太长
        debugInfo += `\n  ${taskCount}. ${task.text.substring(0, 30)}... 日期:${dueDate} 进行中:${isInProgress}`;
    }

    if (dueDate < today && !isInProgress) {
        stats.overdue++;
        if (taskCount <= 10) debugInfo += ` -> 逾期`;
    } else if (dueDate === today && !isInProgress) {
        stats.today++;
        if (taskCount <= 10) debugInfo += ` -> 今天`;
    } else if (dueDate === tomorrowStr && !isInProgress) {
        stats.tomorrow++;
        if (taskCount <= 10) debugInfo += ` -> 明天`;
    } else if (dueDate >= startDate && dueDate <= endDate && dueDate !== today && dueDate !== tomorrowStr && !isInProgress) {
        stats.thisWeek++;
        if (taskCount <= 10) debugInfo += ` -> 本周其他`;
    } else if (!isInProgress) {
        stats.future++;
        if (taskCount <= 10) debugInfo += ` -> 未来`;
    }
});

debugInfo += `\n\n📊 最终统计: 逾期:${stats.overdue} 进行中:${stats.inProgress} 待办:${stats.waiting} 今天:${stats.today} 明天:${stats.tomorrow} 本周其他:${stats.thisWeek} 未来:${stats.future} 已完成:${stats.completed}`;

// 创建看板样式
const container = this.container;
container.innerHTML = '';



// 添加标题
const title = document.createElement('div');
title.style.cssText = `
    text-align: center;
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
`;
title.textContent = '任务清单';
container.appendChild(title);

// 创建卡片容器
const cardContainer = document.createElement('div');
cardContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
`;

// 定义卡片数据
const cards = [
    { label: '逾期', count: stats.overdue, color: '#ff6b6b', bgColor: '#ffe0e0' },
    { label: '进行中', count: stats.inProgress, color: '#4ecdc4', bgColor: '#e0f7f5' },
    { label: '待办', count: stats.waiting, color: '#ffa726', bgColor: '#fff3e0' },
    { label: '今天', count: stats.today, color: '#42a5f5', bgColor: '#e3f2fd' },
    { label: '明天', count: stats.tomorrow, color: '#ab47bc', bgColor: '#f3e5f5' },
    { label: '本周其他', count: stats.thisWeek, color: '#66bb6a', bgColor: '#e8f5e8' },
    { label: '未来', count: stats.future, color: '#78909c', bgColor: '#f5f5f5' },
    { label: '已完成', count: stats.completed, color: '#26a69a', bgColor: '#e0f2f1' }
];

// 创建卡片
cards.forEach((card, index) => {
    const cardElement = document.createElement('div');
    cardElement.style.cssText = `
        background: ${card.bgColor};
        border-radius: 12px;
        padding: 15px 10px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
        cursor: pointer;
        border: 2px solid transparent;
    `;

    cardElement.innerHTML = `
        <div style="font-size: 2em; font-weight: bold; color: ${card.color}; margin-bottom: 5px;">
            ${card.count}
        </div>
        <div style="font-size: 0.9em; color: #666; font-weight: 500;">
            ${card.label}
        </div>
    `;

    // 添加悬停效果
    cardElement.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        this.style.borderColor = card.color;
    });

    cardElement.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        this.style.borderColor = 'transparent';
    });

    // 添加点击事件
    cardElement.addEventListener('click', function() {
        showTasksByCategory(card.label, card.color);
    });

    cardContainer.appendChild(cardElement);
});

container.appendChild(cardContainer);

// 添加总计信息
const totalTasks = stats.overdue + stats.inProgress + stats.waiting + stats.today +
                  stats.tomorrow + stats.thisWeek + stats.future;
const totalInfo = document.createElement('div');
totalInfo.style.cssText = `
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
`;
totalInfo.innerHTML = `
    <div style="font-size: 1.1em; color: #333;">
        <strong>📋 总计: ${totalTasks + stats.completed} 个任务</strong>
        <span style="margin-left: 20px; color: #666;">
            未完成: ${totalTasks} | 已完成: ${stats.completed}
        </span>
    </div>
`;
container.appendChild(totalInfo);

// 创建任务详情显示区域
const taskDetailContainer = document.createElement('div');
taskDetailContainer.id = 'task-detail-container';
taskDetailContainer.style.cssText = `
    margin-top: 20px;
    display: none;
`;
container.appendChild(taskDetailContainer);

// 显示指定类别的任务
function showTasksByCategory(category, color) {
    const detailContainer = document.getElementById('task-detail-container');
    let filteredTasks = [];

    // 重新获取最新的任务数据，确保与下面列表一致
    const currentAllTasks = dv.pages().file.tasks;

    // 使用固定的当前时间确保一致性
    const baseTime = new Date();
    const today = baseTime.toISOString().split('T')[0];
    const tomorrow = new Date(baseTime);
    tomorrow.setDate(baseTime.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    // 计算本周范围（中国标准：周一开始，周日结束）
    const dayOfWeek = baseTime.getDay();
    // 将周日(0)转换为7，这样周一就是1，周日是7
    const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
    const startOfWeek = new Date(baseTime);
    startOfWeek.setDate(baseTime.getDate() - (adjustedDayOfWeek - 1));
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    const startDate = startOfWeek.toISOString().split('T')[0];
    const endDate = endOfWeek.toISOString().split('T')[0];

    // 根据类别筛选任务
    switch(category) {
        case '逾期':
            filteredTasks = currentAllTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                if (!dueDateMatch) return false;

                // 排除进行中任务
                const isInProgress = task.text.includes('🔄') ||
                    task.text.includes('进行中');

                return dueDateMatch[1] < today && !isInProgress;
            });
            break;
        case '进行中':
            filteredTasks = currentAllTasks.filter(task =>
                !task.completed && (
                    task.text.includes('🔄') ||
                    task.text.includes('进行中')
                )
            );
            break;
        case '待办':
            filteredTasks = currentAllTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);

                // 排除进行中任务
                const isInProgress = task.text.includes('🔄') ||
                    task.text.includes('进行中');

                return !dueDateMatch && !isInProgress;
            });
            break;
        case '今天':
            filteredTasks = currentAllTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                return dueDateMatch && dueDateMatch[1] === today;
            });
            break;
        case '明天':
            filteredTasks = currentAllTasks.filter(task => {
                if (task.completed) return false;

                // 排除进行中任务
                const isInProgress = task.text.includes('🔄') ||
                    task.text.includes('进行中');

                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                return dueDateMatch && dueDateMatch[1] === tomorrowStr && !isInProgress;
            });
            break;
        case '本周其他':
            filteredTasks = currentAllTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                if (!dueDateMatch) return false;
                const dueDate = dueDateMatch[1];

                // 排除进行中任务
                const isInProgress = task.text.includes('🔄') ||
                    task.text.includes('进行中');

                return dueDate >= startDate && dueDate <= endDate && dueDate !== today && dueDate !== tomorrowStr && !isInProgress;
            });
            break;
        case '未来':
            filteredTasks = currentAllTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                if (!dueDateMatch) return false;
                const dueDate = dueDateMatch[1];

                // 排除进行中任务
                const isInProgress = task.text.includes('🔄') ||
                    task.text.includes('进行中');

                // 未来任务：超出本周范围且不是今天和明天的任务
                return dueDate > endDate && dueDate !== today && dueDate !== tomorrowStr && !isInProgress;
            });
            break;
        case '已完成':
            filteredTasks = currentAllTasks.filter(task => task.completed);
            break;
    }

    // 生成任务列表HTML
    if (filteredTasks.length > 0) {
        let html = `
            <div style="background: ${color}15; border: 2px solid ${color}; border-radius: 12px; padding: 20px; margin-top: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; color: ${color};">📋 ${category} (${filteredTasks.length} 个)</h3>
                    <button onclick="document.getElementById('task-detail-container').style.display='none'"
                            style="background: ${color}; color: white; border: none; border-radius: 6px; padding: 5px 10px; cursor: pointer;">
                        ✕ 关闭
                    </button>
                </div>
                <div style="max-height: 400px; overflow-y: auto;">
        `;

        filteredTasks.forEach(task => {
            const cleanText = task.text
                .replace(/🍅\d+/g, '')
                .replace(/⏫|🔼|🔽/g, '')
                .replace(/(?:📅\s*|due:\s*|\[due::\s*)\d{4}-\d{2}-\d{2}(?:\])?/g, '')
                .replace(/#project\/[^\s#]+/g, '')
                .replace(/#重要|#不重要/g, '')
                .replace(/🔄/g, '')
                .trim();

            const priority = task.text.includes('⏫') ? '⏫' :
                           task.text.includes('🔼') ? '🔼' :
                           task.text.includes('🔽') ? '🔽' : '';

            const tomatoMatch = task.text.match(/🍅(\d+)/);
            const tomato = tomatoMatch ? `🍅${tomatoMatch[1]}` : '';

            const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
            const dueDate = dueDateMatch ? `📅 ${dueDateMatch[1]}` : '';

            const projectMatch = task.text.match(/#project\/([^\s#]+)/);
            const project = projectMatch ? `#${projectMatch[1]}` : '';

            const important = task.text.includes('#重要') ? '⭐' : '';

            // 生成唯一ID用于操作
            const taskId = `task-${Math.random().toString(36).substr(2, 9)}`;

            html += `
                <div id="${taskId}" style="background: white; border-radius: 8px; padding: 15px; margin-bottom: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: all 0.2s ease;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1; cursor: pointer;" class="task-content" data-path="${task.path}" data-text="${cleanText}">
                            <div style="font-weight: 500; margin-bottom: 5px; color: #333;">
                                ${task.completed ? '✅' : '⬜'} ${cleanText}
                            </div>
                            <div style="font-size: 0.9em; color: #666;">
                                <span style="background: #f0f0f0; padding: 2px 6px; border-radius: 4px; margin-right: 5px;">
                                    📁 ${task.path}
                                </span>
                                <span style="color: #007bff; font-size: 0.8em;">
                                    👆 点击跳转到原位置
                                </span>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px;">
                            <div style="display: flex; gap: 5px; margin-bottom: 5px;">
                                <button class="jump-btn" data-path="${task.path}" data-text="${cleanText}"
                                        style="background: #007bff; color: white; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer; font-size: 0.8em;">
                                    📍 跳转
                                </button>
                            </div>
                            <div style="display: flex; flex-wrap: wrap; gap: 3px; justify-content: flex-end;">
                                ${priority ? `<span style="font-size: 1.2em;">${priority}</span>` : ''}
                                ${important ? `<span style="font-size: 1.1em;">${important}</span>` : ''}
                                ${tomato ? `<span style="background: #fff3e0; color: #f57c00; padding: 2px 6px; border-radius: 4px; font-size: 0.9em;">${tomato}</span>` : ''}
                                ${dueDate ? `<span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 4px; font-size: 0.9em;">${dueDate}</span>` : ''}
                                ${project ? `<span style="background: #f3e5f5; color: #7b1fa2; padding: 2px 6px; border-radius: 4px; font-size: 0.9em;">${project}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        detailContainer.innerHTML = html;
        detailContainer.style.display = 'block';

        // 添加事件委托
        setTimeout(() => {
            // 跳转按钮事件
            const jumpBtns = detailContainer.querySelectorAll('.jump-btn');
            jumpBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const path = this.getAttribute('data-path');
                    const text = this.getAttribute('data-text');
                    openTaskLocation(path, text);
                });
            });



            // 任务内容点击事件
            const taskContents = detailContainer.querySelectorAll('.task-content');
            taskContents.forEach(content => {
                content.addEventListener('click', function() {
                    const path = this.getAttribute('data-path');
                    const text = this.getAttribute('data-text');
                    openTaskLocation(path, text);
                });
            });
        }, 100);

        // 滚动到详情区域
        detailContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
        detailContainer.innerHTML = `
            <div style="background: #f8f9fa; border: 2px solid #dee2e6; border-radius: 12px; padding: 20px; margin-top: 15px; text-align: center;">
                <h3 style="color: #666;">📋 ${category}</h3>
                <p style="color: #999;">暂无相关任务</p>
                <button onclick="document.getElementById('task-detail-container').style.display='none'"
                        style="background: #6c757d; color: white; border: none; border-radius: 6px; padding: 8px 16px; cursor: pointer;">
                    关闭
                </button>
            </div>
        `;
        detailContainer.style.display = 'block';
    }
}

// 定义全局函数
function openTaskLocation(filePath, taskText) {
    try {
        console.log('尝试打开文件:', filePath);
        // 使用 Obsidian 的内部 API 打开文件
        if (app && app.workspace) {
            app.workspace.openLinkText(filePath, '', false);
            console.log('文件打开成功');
        } else {
            alert(`请手动打开文件: ${filePath}`);
        }
    } catch (error) {
        console.error('打开文件失败:', error);
        alert(`无法打开文件: ${filePath}\n\n请手动在文件管理器中找到该文件`);
    }
}



function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 10000;
        font-weight: bold;
    `;
    successDiv.textContent = message;
    document.body.appendChild(successDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
}
```

## 🔥 今日任务

```dataviewjs
// 使用固定的当前时间确保一致性
const baseTime = new Date();
const today = baseTime.toISOString().split('T')[0];
const todayTasks = dv.pages().file.tasks
    .where(t => {
        if (t.completed) return false;
        const dueDateMatch = t.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
        return dueDateMatch && dueDateMatch[1] === today;
    })
    .sort(t => {
        if (t.text.includes('⏫')) return 0;
        if (t.text.includes('🔼')) return 1;
        if (t.text.includes('🔽')) return 3;
        return 2;
    });

if (todayTasks.length > 0) {
    dv.taskList(todayTasks);
} else {
    dv.paragraph("🎉 今日暂无任务安排");
}
```

## 📅 明天任务

```dataviewjs
// 使用固定的当前时间确保一致性
const baseTime = new Date();
const tomorrow = new Date(baseTime);
tomorrow.setDate(baseTime.getDate() + 1);
const tomorrowStr = tomorrow.toISOString().split('T')[0];

const tomorrowTasks = dv.pages().file.tasks
    .where(t => {
        if (t.completed) return false;
        const dueDateMatch = t.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch || dueDateMatch[1] !== tomorrowStr) return false;

        // 排除进行中任务
        const isInProgress = t.text.includes('🔄') ||
            t.text.includes('进行中');

        return !isInProgress;
    })
    .sort(t => {
        if (t.text.includes('⏫')) return 0;
        if (t.text.includes('🔼')) return 1;
        if (t.text.includes('🔽')) return 3;
        return 2;
    });

if (tomorrowTasks.length > 0) {
    dv.taskList(tomorrowTasks);
} else {
    dv.paragraph("📅 明天暂无任务安排");
}
```

## ⏰ 本周任务

```dataviewjs
// 使用固定的当前时间确保一致性
const baseTime = new Date();

// 计算本周日期范围（中国标准：周一开始，周日结束）
const dayOfWeek = baseTime.getDay();
// 将周日(0)转换为7，这样周一就是1，周日是7
const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
const startOfWeek = new Date(baseTime);
startOfWeek.setDate(baseTime.getDate() - (adjustedDayOfWeek - 1));
const endOfWeek = new Date(startOfWeek);
endOfWeek.setDate(startOfWeek.getDate() + 6);

const startDate = startOfWeek.toISOString().split('T')[0];
const endDate = endOfWeek.toISOString().split('T')[0];

// 临时调试信息
dv.paragraph(`🔍 下面本周任务调试: 今天=${baseTime.toISOString().split('T')[0]}, 本周=${startDate}到${endDate}`);

const today = baseTime.toISOString().split('T')[0];
const tomorrow = new Date(baseTime);
tomorrow.setDate(baseTime.getDate() + 1);
const tomorrowStr = tomorrow.toISOString().split('T')[0];

const weekTasks = dv.pages().file.tasks
    .where(t => {
        if (t.completed) return false;
        const dueDateMatch = t.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch) return false;
        const dueDate = dueDateMatch[1];

        // 排除进行中任务
        const isInProgress = t.text.includes('🔄') ||
            t.text.includes('进行中');

        return dueDate >= startDate && dueDate <= endDate && dueDate !== today && dueDate !== tomorrowStr && !isInProgress;
    })
    .sort(t => {
        const dueDateMatch = t.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
        return dueDateMatch ? dueDateMatch[1] : '9999-99-99';
    });

if (weekTasks.length > 0) {
    dv.taskList(weekTasks);
} else {
    dv.paragraph("📅 本周暂无任务安排");
}
```

## 🚨 逾期任务

```dataviewjs
// 使用固定的当前时间确保一致性
const baseTime = new Date();
const today = baseTime.toISOString().split('T')[0];
const overdueTasks = dv.pages().file.tasks
    .where(t => {
        if (t.completed) return false;
        const dueDateMatch = t.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch) return false;

        // 排除进行中任务
        const isInProgress = t.text.includes('🔄') ||
            t.text.includes('进行中');

        return dueDateMatch[1] < today && !isInProgress;
    })
    .sort(t => {
        const dueDateMatch = t.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
        return dueDateMatch ? dueDateMatch[1] : '9999-99-99';
    });

if (overdueTasks.length > 0) {
    dv.taskList(overdueTasks);
} else {
    dv.paragraph("✅ 暂无逾期任务");
}
```

## 🍅 番茄钟看板

```dataviewjs
// 使用固定的当前时间确保一致性
const baseTime = new Date();

// 获取所有任务
const allTasks = dv.pages().file.tasks;
const today = baseTime.toISOString().split('T')[0];

// 获取今日Daily Log的番茄钟数据
const todayDailyLog = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.includes(today))
    .first();

let dailyGoal = 0;
let dailyActual = 0;
let dailyRate = 0;
if (todayDailyLog) {
    dailyGoal = todayDailyLog.tomato_goal || 0;
    dailyActual = todayDailyLog.tomato_actual || 0;
    dailyRate = todayDailyLog.tomato_rate || 0;
}

// 获取昨日数据用于对比
const yesterday = new Date(baseTime);
yesterday.setDate(baseTime.getDate() - 1);
const yesterdayStr = yesterday.toISOString().split('T')[0];
const yesterdayDailyLog = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.includes(yesterdayStr))
    .first();

let yesterdayActual = 0;
let yesterdayGoal = 0;
if (yesterdayDailyLog) {
    yesterdayActual = yesterdayDailyLog.tomato_actual || 0;
    yesterdayGoal = yesterdayDailyLog.tomato_goal || 0;
}

// 获取本周数据
const dayOfWeek = baseTime.getDay();
const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
const startOfWeek = new Date(baseTime);
startOfWeek.setDate(baseTime.getDate() - (adjustedDayOfWeek - 1));

let weeklyGoal = 0;
let weeklyActual = 0;
let weeklyDays = 0;

for (let i = 0; i < adjustedDayOfWeek; i++) {
    const checkDate = new Date(startOfWeek);
    checkDate.setDate(startOfWeek.getDate() + i);
    const checkDateStr = checkDate.toISOString().split('T')[0];

    const dayLog = dv.pages('"0_Bullet Journal/Daily Notes"')
        .where(p => p.file.name.includes(checkDateStr))
        .first();

    if (dayLog) {
        weeklyGoal += dayLog.tomato_goal || 0;
        weeklyActual += dayLog.tomato_actual || 0;
        if (dayLog.tomato_goal > 0) weeklyDays++;
    }
}

// 统计番茄钟
let totalPlanned = 0;
let totalCompleted = 0;
let todayPlanned = 0;
let todayCompleted = 0;

allTasks.forEach(task => {
    const tomatoMatch = task.text.match(/🍅(\d+)/);
    if (tomatoMatch) {
        const count = parseInt(tomatoMatch[1]);
        totalPlanned += count;

        if (task.completed) {
            totalCompleted += count;
        }

        // 今日任务
        if (task.text.includes(`📅 ${today}`)) {
            todayPlanned += count;
            if (task.completed) {
                todayCompleted += count;
            }
        }
    }
});

// 创建番茄钟看板
const container = this.container;
container.innerHTML = '';

// 创建番茄钟卡片容器
const tomatoContainer = document.createElement('div');
tomatoContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    border-radius: 15px;
    margin-bottom: 20px;
`;

// 番茄钟数据 - 优先使用Daily Log记录的实际数据
const actualTodayCompleted = dailyActual > 0 ? dailyActual : todayCompleted;

const tomatoData = [
    {
        label: '今日目标',
        count: dailyGoal,
        icon: '🎯',
        color: '#9c88ff',
        bgColor: '#f8f7ff',
        subtitle: dailyGoal > 0 ? '已设定' : '未设定'
    },
    {
        label: '今日完成',
        count: actualTodayCompleted,
        icon: '🔥',
        color: '#ff8c42',
        bgColor: '#fff8f0',
        subtitle: dailyActual > 0 ? '已记录' : '实时统计'
    },
    {
        label: '昨日完成',
        count: yesterdayActual,
        icon: '📊',
        color: '#74c0fc',
        bgColor: '#f0f8ff',
        subtitle: `目标${yesterdayGoal}`
    },
    {
        label: '本周累计',
        count: weeklyActual,
        icon: '📈',
        color: '#51cf66',
        bgColor: '#f3fff3',
        subtitle: `${weeklyDays}天记录`
    }
];

// 创建番茄钟卡片
tomatoData.forEach(item => {
    const card = document.createElement('div');
    card.style.cssText = `
        background: ${item.bgColor};
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    `;

    card.innerHTML = `
        <div style="font-size: 2.5em; margin-bottom: 10px;">
            ${item.icon}
        </div>
        <div style="font-size: 2em; font-weight: bold; color: ${item.color}; margin-bottom: 5px;">
            ${item.count}
        </div>
        <div style="font-size: 1em; color: #666; font-weight: 500;">
            ${item.label}
        </div>
        <div style="font-size: 0.8em; color: #999; margin-top: 5px;">
            ${item.subtitle}
        </div>
    `;

    // 添加悬停效果
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px) scale(1.02)';
        this.style.boxShadow = '0 8px 20px rgba(0,0,0,0.15)';
        this.style.borderColor = item.color;
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
        this.style.borderColor = 'transparent';
    });

    tomatoContainer.appendChild(card);
});

container.appendChild(tomatoContainer);

// 添加完成率信息
const todayRate = dailyGoal > 0 ?
    Math.round((actualTodayCompleted / dailyGoal) * 100) : 0;

const weeklyRate = weeklyGoal > 0 ?
    Math.round((weeklyActual / weeklyGoal) * 100) : 0;

const yesterdayRate = yesterdayGoal > 0 ?
    Math.round((yesterdayActual / yesterdayGoal) * 100) : 0;

const rateInfo = document.createElement('div');
rateInfo.style.cssText = `
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    margin-top: 15px;
`;

// 今日达成率
const todayCard = document.createElement('div');
todayCard.style.cssText = `
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #333;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

const todayRateDetail = dailyGoal > 0 ?
    `${actualTodayCompleted}/${dailyGoal} 🍅` :
    '未设定目标';

todayCard.innerHTML = `
    <div style="font-size: 1.8em; font-weight: bold; margin-bottom: 5px;">
        ${todayRate}%
    </div>
    <div style="font-size: 1em; opacity: 0.8;">
        今日达成率
    </div>
    <div style="font-size: 0.8em; opacity: 0.6; margin-top: 5px;">
        ${todayRateDetail}
    </div>
`;

// 昨日对比
const yesterdayCard = document.createElement('div');
yesterdayCard.style.cssText = `
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

const compareIcon = actualTodayCompleted >= yesterdayActual ? '📈' : '📉';
const compareText = actualTodayCompleted >= yesterdayActual ? '超越昨日' : '低于昨日';

yesterdayCard.innerHTML = `
    <div style="font-size: 1.8em; font-weight: bold; margin-bottom: 5px;">
        ${compareIcon}
    </div>
    <div style="font-size: 1em; opacity: 0.8;">
        ${compareText}
    </div>
    <div style="font-size: 0.8em; opacity: 0.6; margin-top: 5px;">
        昨日${yesterdayActual}🍅
    </div>
`;

// 本周进度
const weeklyCard = document.createElement('div');
weeklyCard.style.cssText = `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

weeklyCard.innerHTML = `
    <div style="font-size: 1.8em; font-weight: bold; margin-bottom: 5px;">
        ${weeklyRate}%
    </div>
    <div style="font-size: 1em; opacity: 0.9;">
        本周进度
    </div>
    <div style="font-size: 0.8em; opacity: 0.7; margin-top: 5px;">
        ${weeklyActual}/${weeklyGoal} 🍅
    </div>
`;

rateInfo.appendChild(todayCard);
rateInfo.appendChild(yesterdayCard);
rateInfo.appendChild(weeklyCard);
container.appendChild(rateInfo);
```

## 🎯 主任务与子任务

```dataviewjs
// 获取所有主任务（不以@开头的任务）
const allTasks = dv.pages().file.tasks.where(t => {
    if (t.completed) return false;

    // 排除进行中任务
    const isInProgress = t.text.includes('🔄') ||
        t.text.includes('进行中');

    return !isInProgress;
});
const mainTasks = allTasks.where(t => !t.text.trim().startsWith('@'));
const subTasks = allTasks.where(t => t.text.trim().startsWith('@'));

// 建立主任务与子任务的关系
const taskRelations = [];

mainTasks.forEach(mainTask => {
    let cleanMainTaskName = mainTask.text
        .replace(/🍅\d+/g, '')
        .replace(/⏫|🔼|🔽/g, '')
        .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
        .replace(/#project\/[^\s#]+/g, '')
        .replace(/#重要|#不重要/g, '')
        .trim();

    // 计算相关子任务数量
    const relatedSubTasks = subTasks.filter(subTask => {
        const subTaskText = subTask.text.trim();
        const atMatch = subTaskText.match(/^@([^@]+?)\s/);
        if (atMatch) {
            const mainTaskName = atMatch[1].trim();
            return cleanMainTaskName.includes(mainTaskName) || mainTaskName.includes(cleanMainTaskName);
        }
        return false;
    });

    if (relatedSubTasks.length > 0 || mainTask.text.includes('🍅')) {
        taskRelations.push([
            cleanMainTaskName,
            relatedSubTasks.length,
            `[[${mainTask.path}]]`
        ]);
    }
});

if (taskRelations.length > 0) {
    // 按子任务数量排序
    taskRelations.sort((a, b) => b[1] - a[1]);
    dv.table(["主任务", "子任务数", "文件"], taskRelations);
} else {
    dv.paragraph("📝 暂无主任务");
}
```

## 📁 项目任务分布

```dataviewjs
// 获取所有任务
const allTasks = dv.pages().file.tasks;

// 1. 获取项目文件夹中的项目
const projectFiles = dv.pages('"6_Project Notes"').where(p => p.file.name !== "README");
const projectStats = {};

// 2. 统计每个项目文件中的任务
projectFiles.forEach(projectFile => {
    const projectName = projectFile.file.name.replace('.md', '');

    if (!projectStats[projectName]) {
        projectStats[projectName] = {
            planned: 0,        // 已规划任务（项目文件中）
            pending: 0,        // 待整理任务（日记中带[[项目名]]）
            completed: 0,      // 已完成任务
            important: 0,      // 重要任务
            tomatoes: 0        // 番茄钟总数
        };
    }

    // 统计项目文件中的任务
    const projectTasks = projectFile.file.tasks;
    projectTasks.forEach(task => {
        const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');

        if (task.completed) {
            projectStats[projectName].completed++;
        } else if (!isInProgress) {
            projectStats[projectName].planned++;
        }

        if (task.text.includes('#重要')) {
            projectStats[projectName].important++;
        }

        const tomatoMatch = task.text.match(/🍅(\d+)/);
        if (tomatoMatch) {
            projectStats[projectName].tomatoes += parseInt(tomatoMatch[1]);
        }
    });
});

// 3. 统计日记中待整理的项目任务
const dailyNotes = dv.pages('"0_Bullet Journal/Daily Notes"');
dailyNotes.forEach(note => {
    const noteTasks = note.file.tasks;
    noteTasks.forEach(task => {
        // 查找任务中的项目双链
        const projectMatches = task.text.match(/\[\[([^\]]+)\]\]/g);
        if (projectMatches) {
            projectMatches.forEach(match => {
                const projectName = match.replace(/\[\[|\]\]/g, '');

                // 如果这个项目在项目文件夹中存在
                if (projectStats[projectName]) {
                    const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');

                    if (!task.completed && !isInProgress) {
                        projectStats[projectName].pending++;
                    }

                    if (task.text.includes('#重要')) {
                        projectStats[projectName].important++;
                    }

                    const tomatoMatch = task.text.match(/🍅(\d+)/);
                    if (tomatoMatch) {
                        projectStats[projectName].tomatoes += parseInt(tomatoMatch[1]);
                    }
                }
            });
        }
    });
});

// 4. 收集待整理任务的详细信息
const pendingTaskDetails = {};

dailyNotes.forEach(note => {
    const noteTasks = note.file.tasks;
    noteTasks.forEach(task => {
        const projectMatches = task.text.match(/\[\[([^\]]+)\]\]/g);
        if (projectMatches) {
            projectMatches.forEach(match => {
                const projectName = match.replace(/\[\[|\]\]/g, '');

                if (projectStats[projectName]) {
                    const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');

                    if (!task.completed && !isInProgress) {
                        if (!pendingTaskDetails[projectName]) {
                            pendingTaskDetails[projectName] = [];
                        }
                        pendingTaskDetails[projectName].push({
                            text: task.text,
                            file: note.file.name,
                            path: note.file.path
                        });
                    }
                }
            });
        }
    });
});

// 5. 生成显示数据
const container = this.container;
container.innerHTML = '';

// 创建表格容器
const tableContainer = document.createElement('div');
tableContainer.style.cssText = `
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-bottom: 20px;
`;

const projectData = Object.entries(projectStats)
    .filter(([name, stats]) => stats.planned > 0 || stats.pending > 0 || stats.completed > 0)
    .sort((a, b) => (b[1].planned + b[1].pending) - (a[1].planned + a[1].pending));

if (projectData.length > 0) {
    // 创建表格标题
    let tableHTML = `
        <table style="width: 100%; border-collapse: collapse; font-size: 0.9em;">
            <thead>
                <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">项目</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">已规划</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">待整理</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">已完成</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">完成率</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">重要</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">番茄钟</th>
                </tr>
            </thead>
            <tbody>
    `;

    projectData.forEach(([project, stats]) => {
        const total = stats.planned + stats.pending;
        const completionRate = stats.completed > 0 ?
            Math.round((stats.completed / (stats.completed + total)) * 100) : 0;

        const pendingCell = stats.pending > 0 ?
            `<button onclick="showPendingTasks('${project}')"
                     style="background: #ffc107; color: #212529; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer; font-weight: bold;">
                📝 ${stats.pending}
             </button>` :
            `📝 0`;

        tableHTML += `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px; font-weight: 500;">📁 ${project}</td>
                <td style="padding: 12px; text-align: center;">🎯 ${stats.planned}</td>
                <td style="padding: 12px; text-align: center;">${pendingCell}</td>
                <td style="padding: 12px; text-align: center;">✅ ${stats.completed}</td>
                <td style="padding: 12px; text-align: center;">📈 ${completionRate}%</td>
                <td style="padding: 12px; text-align: center;">${stats.important > 0 ? `⭐ ${stats.important}` : ''}</td>
                <td style="padding: 12px; text-align: center;">${stats.tomatoes > 0 ? `🍅 ${stats.tomatoes}` : ''}</td>
            </tr>
        `;
    });

    tableHTML += `
            </tbody>
        </table>
    `;

    tableContainer.innerHTML = tableHTML;
    container.appendChild(tableContainer);

    // 创建详情显示区域
    const detailContainer = document.createElement('div');
    detailContainer.id = 'pending-detail-container';
    detailContainer.style.display = 'none';
    container.appendChild(detailContainer);

    // 添加显示待整理任务的函数
    window.showPendingTasks = function(projectName) {
        const details = pendingTaskDetails[projectName];
        if (!details || details.length === 0) return;

        const detailContainer = document.getElementById('pending-detail-container');

        let html = `
            <div style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 12px; padding: 20px; margin-top: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; color: #856404;">📝 ${projectName} - 待整理任务 (${details.length} 个)</h3>
                    <button onclick="document.getElementById('pending-detail-container').style.display='none'"
                            style="background: #ffc107; color: #212529; border: none; border-radius: 6px; padding: 5px 10px; cursor: pointer;">
                        ✕ 关闭
                    </button>
                </div>
                <div style="max-height: 400px; overflow-y: auto;">
        `;

        details.forEach(task => {
            const cleanText = task.text
                .replace(/\[\[[^\]]+\]\]/g, '')
                .replace(/🍅\d+/g, '')
                .replace(/⏫|🔼|🔽/g, '')
                .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
                .replace(/#\w+/g, '')
                .trim();

            html += `
                <div style="background: white; border-radius: 8px; padding: 12px; margin-bottom: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 3px solid #ffc107;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1;">
                            <div style="font-weight: 500; margin-bottom: 3px; color: #333;">
                                ⬜ ${cleanText}
                            </div>
                            <div style="font-size: 0.8em; color: #666;">
                                📁 ${task.file}
                                <span style="color: #ffc107; margin-left: 10px;">👆 需要迁移到项目文件</span>
                            </div>
                        </div>
                        <button onclick="app.workspace.openLinkText('${task.path}', '', false)"
                                style="background: #007bff; color: white; border: none; border-radius: 4px; padding: 3px 6px; cursor: pointer; font-size: 0.7em;">
                            📍 跳转
                        </button>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        detailContainer.innerHTML = html;
        detailContainer.style.display = 'block';
    };

} else {
    tableContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">📝 暂无项目任务</p>';
    container.appendChild(tableContainer);
}
```

## 🔍 快速筛选

```dataviewjs
// 创建筛选按钮
const container = this.container;

const buttonStyle = "margin: 5px; padding: 8px 12px; border: none; border-radius: 4px; cursor: pointer; color: white; font-size: 0.9em;";

const buttons = [
    { text: "🔥 重要任务", filter: "#重要", color: "#dc3545" },
    { text: "⏫ 高优先级", filter: "⏫", color: "#fd7e14" },
    { text: "🍅 大任务(3+)", filter: "tomato", color: "#28a745" },
    { text: "📅 无日期", filter: "nodate", color: "#6c757d" }
];

buttons.forEach(btn => {
    const button = document.createElement('button');
    button.textContent = btn.text;
    button.style.cssText = buttonStyle + `background-color: ${btn.color};`;

    button.onclick = function() {
        showFilteredTasks(btn.filter, btn.text);
    };

    container.appendChild(button);
});

// 结果显示区域
const resultDiv = document.createElement('div');
resultDiv.id = 'filter-results';
resultDiv.style.marginTop = '15px';
container.appendChild(resultDiv);

// 筛选函数
function showFilteredTasks(filter, title) {
    const allTasks = dv.pages().file.tasks.where(t => {
        if (t.completed) return false;

        // 排除进行中任务
        const isInProgress = t.text.includes('🔄') ||
            t.text.includes('进行中');

        return !isInProgress;
    }).array();
    let filteredTasks = [];

    switch(filter) {
        case "#重要":
            filteredTasks = allTasks.filter(t => t.text.includes('#重要'));
            break;
        case "⏫":
            filteredTasks = allTasks.filter(t => t.text.includes('⏫'));
            break;
        case "tomato":
            filteredTasks = allTasks.filter(t => {
                const match = t.text.match(/🍅(\d+)/);
                return match && parseInt(match[1]) >= 3;
            });
            break;
        case "nodate":
            filteredTasks = allTasks.filter(t => !t.text.match(/📅\s*\d{4}-\d{2}-\d{2}/));
            break;
    }

    const resultDiv = document.getElementById('filter-results');
    if (filteredTasks.length > 0) {
        let html = `
            <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); margin-top: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h4 style="margin: 0; color: #333;">${title} (${filteredTasks.length} 个)</h4>
                    <button onclick="document.getElementById('filter-results').innerHTML=''"
                            style="background: #dc3545; color: white; border: none; border-radius: 6px; padding: 5px 10px; cursor: pointer; font-size: 0.8em;">
                        ✕ 关闭
                    </button>
                </div>
                <div style="max-height: 400px; overflow-y: auto; padding-right: 5px; scrollbar-width: thin; scrollbar-color: #ccc #f1f1f1;">
                <style>
                    .filter-scroll-container::-webkit-scrollbar {
                        width: 8px;
                    }
                    .filter-scroll-container::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 4px;
                    }
                    .filter-scroll-container::-webkit-scrollbar-thumb {
                        background: #ccc;
                        border-radius: 4px;
                    }
                    .filter-scroll-container::-webkit-scrollbar-thumb:hover {
                        background: #999;
                    }
                </style>
                <div class="filter-scroll-container" style="max-height: 400px; overflow-y: auto; padding-right: 5px;">
        `;

        filteredTasks.forEach(task => {
            const cleanText = task.text.replace(/🍅\d+|⏫|🔼|🔽|📅\s*\d{4}-\d{2}-\d{2}|#\w+/g, '').trim();
            const taskId = `filter-task-${Math.random().toString(36).substr(2, 9)}`;

            // 提取任务属性
            const priority = task.text.includes('⏫') ? '⏫' :
                           task.text.includes('🔼') ? '🔼' :
                           task.text.includes('🔽') ? '🔽' : '';
            const tomatoMatch = task.text.match(/🍅(\d+)/);
            const tomato = tomatoMatch ? `🍅${tomatoMatch[1]}` : '';
            const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
            const dueDate = dueDateMatch ? `📅 ${dueDateMatch[1]}` : '';
            const projectMatch = task.text.match(/#project\/([^\s#]+)/);
            const project = projectMatch ? `#${projectMatch[1]}` : '';
            const important = task.text.includes('#重要') ? '⭐' : '';

            html += `
                <div id="${taskId}" style="background: white; border-radius: 8px; padding: 12px; margin-bottom: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 3px solid #007bff;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1; cursor: pointer;" class="filter-task-content" data-path="${task.path}" data-text="${cleanText}">
                            <div style="font-weight: 500; margin-bottom: 3px; color: #333;">
                                ${task.completed ? '✅' : '⬜'} ${cleanText}
                            </div>
                            <div style="font-size: 0.8em; color: #666;">
                                📁 ${task.path}
                                <span style="color: #007bff; margin-left: 10px;">👆 点击跳转</span>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 3px;">
                            <button class="filter-jump-btn" data-path="${task.path}" data-text="${cleanText}"
                                    style="background: #007bff; color: white; border: none; border-radius: 4px; padding: 3px 6px; cursor: pointer; font-size: 0.7em;">
                                📍 跳转
                            </button>
                            <div style="display: flex; flex-wrap: wrap; gap: 2px; justify-content: flex-end;">
                                ${priority ? `<span style="font-size: 1em;">${priority}</span>` : ''}
                                ${important ? `<span style="font-size: 1em;">${important}</span>` : ''}
                                ${tomato ? `<span style="background: #fff3e0; color: #f57c00; padding: 1px 4px; border-radius: 3px; font-size: 0.7em;">${tomato}</span>` : ''}
                                ${dueDate ? `<span style="background: #e3f2fd; color: #1976d2; padding: 1px 4px; border-radius: 3px; font-size: 0.7em;">${dueDate}</span>` : ''}
                                ${project ? `<span style="background: #f3e5f5; color: #7b1fa2; padding: 1px 4px; border-radius: 3px; font-size: 0.7em;">${project}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `</div></div></div>`; // 关闭内层滚动div、外层滚动div和容器div
        resultDiv.innerHTML = html;

        // 添加事件监听
        setTimeout(() => {
            // 定义本地跳转函数
            function openTaskLocationLocal(filePath, taskText) {
                try {
                    console.log('尝试打开文件:', filePath);
                    // 使用 Obsidian 的内部 API 打开文件
                    if (app && app.workspace) {
                        app.workspace.openLinkText(filePath, '', false);
                        console.log('文件打开成功');
                    } else {
                        alert(`请手动打开文件: ${filePath}`);
                    }
                } catch (error) {
                    console.error('打开文件失败:', error);
                    alert(`无法打开文件: ${filePath}\n\n请手动在文件管理器中找到该文件`);
                }
            }

            // 跳转按钮事件
            const jumpBtns = resultDiv.querySelectorAll('.filter-jump-btn');
            jumpBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const path = this.getAttribute('data-path');
                    const text = this.getAttribute('data-text');
                    openTaskLocationLocal(path, text);
                });
            });

            // 任务内容点击事件
            const taskContents = resultDiv.querySelectorAll('.filter-task-content');
            taskContents.forEach(content => {
                content.addEventListener('click', function() {
                    const path = this.getAttribute('data-path');
                    const text = this.getAttribute('data-text');
                    openTaskLocationLocal(path, text);
                });
            });
        }, 100);
    } else {
        resultDiv.innerHTML = `
            <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); margin-top: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h4 style="margin: 0; color: #333;">${title} (0 个)</h4>
                    <button onclick="document.getElementById('filter-results').innerHTML=''"
                            style="background: #dc3545; color: white; border: none; border-radius: 6px; padding: 5px 10px; cursor: pointer; font-size: 0.8em;">
                        ✕ 关闭
                    </button>
                </div>
                <div style="text-align: center; padding: 20px; color: #666;">
                    <div style="font-size: 2em; margin-bottom: 10px;">📝</div>
                    <p>未找到 ${title}</p>
                </div>
            </div>
        `;
    }
}
```

---

## 📝 任务格式规范

### 标准格式
```markdown
- [ ] 任务名称 📅 2025-05-20 🍅3 ⏫ #project/工作 #重要
- [ ] @主任务名 子任务描述 📅 2025-05-18 🍅1 🔼 #project/工作
```

### 符号说明
- `📅` 截止日期
- `🍅` 番茄钟数量
- `⏫🔼🔽` 优先级（高到低）
- `#project/` 项目标签
- `#重要/#不重要` 重要性
- `@主任务名` 子任务标记

### 进行中任务标记
支持以下两种方式标记进行中的任务：
- `🔄` 循环箭头（推荐）
- `进行中` 中文文字

### 使用建议

#### 🎯 **如何标记进行中的任务**
1. **方法一：添加符号（推荐）**
   ```markdown
   - [ ] 学习AI课程 🔄 📅 2025-05-20 🍅3 ⏫ #project/学习
   ```

2. **方法二：添加文字**
   ```markdown
   - [ ] 健身锻炼 进行中 📅 2025-05-17 🍅1 #project/健康
   ```

#### 📋 **任务状态管理建议**
- **📝 新任务** → 不添加任何状态标记
- **🚀 开始执行** → 添加进行中标记（如 🔄）
- **⏸️ 暂停中断** → 移除进行中标记，保持未完成状态
- **✅ 完成任务** → 勾选任务为已完成

#### 🔍 **快速查看技巧**
1. **点击"进行中"卡片** → 查看所有正在进行的任务
2. **点击"今天"卡片** → 查看今日计划任务
3. **点击"明天"卡片** → 查看明天的任务（需要今天准备）
4. **点击"本周其他"卡片** → 查看本周除今明两天外的任务
5. **点击"逾期"卡片** → 查看需要紧急处理的任务
6. **使用筛选按钮** → 快速筛选重要任务、高优先级任务等

#### 📅 **时间分类说明**
- **今天**：今日必须完成的任务
- **明天**：明天的任务（建议今天做准备）
- **本周其他**：本周除今明两天外的任务
- **未来**：下周及以后的任务

#### 💡 **最佳实践**
- 同时只标记 3-5 个任务为"进行中"，避免分散注意力
- 每天开始工作时，先查看"今天"和"逾期"任务
- 定期清理"进行中"标记，避免标记过多任务
- 使用番茄钟🍅来估算任务时间，提高时间管理效率
- **推荐使用 🔄 标记进行中任务**，简洁明了且易于识别
