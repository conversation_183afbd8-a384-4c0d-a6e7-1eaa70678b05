# Daily Log 番茄钟增强模板

## 📝 **YAML 属性配置**

在每日笔记的头部添加以下属性：

```yaml
---
# 基础信息
tags:
  - type/structure
  - structure/bujo/daily
template_type: BuJo Daily
created: 2025-05-25
updated: 2025-05-25T10:20

# 番茄钟管理
tomato_goal: 8          # 今日目标番茄钟数
tomato_actual: 0        # 实际完成番茄钟数
tomato_rate: 0          # 达成率（自动计算或手动填写）
tomato_morning: 0       # 上午完成数量
tomato_afternoon: 0     # 下午完成数量
tomato_evening: 0       # 晚上完成数量

# 其他属性...
---
```

## 🍅 **番茄钟复盘区域模板**

```markdown
## 🍅 今日番茄钟复盘
```dataviewjs
// 读取当前页面的番茄钟数据
const page = dv.current();
const goal = page.tomato_goal || 0;
const actual = page.tomato_actual || 0;
const rate = goal > 0 ? Math.round((actual / goal) * 100) : 0;
const morning = page.tomato_morning || 0;
const afternoon = page.tomato_afternoon || 0;
const evening = page.tomato_evening || 0;

// 显示复盘信息
dv.paragraph(`- **目标**：🍅 ${goal}`);
dv.paragraph(`- **实际**：🍅 ${actual}`);
dv.paragraph(`- **达成率**：${rate}%`);

// 时间分布
dv.header(4, "📊 时间分布");
dv.paragraph(`- 上午：${'🍅'.repeat(morning)} (${morning}个)`);
dv.paragraph(`- 下午：${'🍅'.repeat(afternoon)} (${afternoon}个)`);
dv.paragraph(`- 晚上：${'🍅'.repeat(evening)} (${evening}个)`);

// 状态提示
if (actual === 0) {
    dv.paragraph("⚠️ **提醒**：请在页面属性中更新 `tomato_actual` 数据");
} else if (rate >= 100) {
    dv.paragraph("🎉 **太棒了**：超额完成目标！");
} else if (rate >= 80) {
    dv.paragraph("👍 **不错**：接近目标，继续保持！");
} else {
    dv.paragraph("💪 **加油**：明天可以调整目标或优化时间安排");
}
```

### 📝 分析记录
- **效率最高时段**：
- **被打断次数**：
- **主要完成任务**：
- **遇到的困难**：

### 💡 改进建议
- **时间安排优化**：
- **环境改善**：
- **工具使用**：

### 🔄 明日调整
- **目标调整**：
- **时间安排**：
- **重点任务**：
```

## 📊 **使用工作流程**

### 🌅 **早上规划（5分钟）**

1. **设置目标**：
   ```yaml
   tomato_goal: 8
   ```

2. **查看仪表盘**：
   - 打开 `任务仪表盘-简化版.md`
   - 查看今日目标卡片
   - 对比昨日表现

3. **规划任务**：
   - 给任务标记预估番茄钟
   - 安排时间分布

### 🔄 **执行过程**

1. **开始任务**：
   ```markdown
   - [ ] 重要任务 🔄 📅 2025-05-25 🍅3 ⏫ #重要
   ```

2. **完成任务**：
   ```markdown
   - [x] 重要任务 📅 2025-05-25 🍅3→4 ⏫ #重要
   ```

3. **实时更新**：
   - 在属性中更新分时段数据
   - 观察仪表盘变化

### 🌙 **晚上复盘（10分钟）**

1. **更新数据**：
   ```yaml
   tomato_actual: 6
   tomato_rate: 75
   tomato_morning: 3
   tomato_afternoon: 2
   tomato_evening: 1
   ```

2. **查看分析**：
   - 主仪表盘：今日达成率、昨日对比
   - 历史仪表盘：趋势分析、效率报告

3. **记录复盘**：
   - 填写分析记录
   - 制定改进建议
   - 规划明日调整

## 🎯 **数据管理最佳实践**

### 📝 **属性填写建议**

1. **tomato_goal**：
   - 早上设定，基于任务量和可用时间
   - 新手建议6-8个，熟练后8-12个

2. **tomato_actual**：
   - 晚上复盘时填写
   - 基于实际完成的任务番茄钟总数

3. **分时段数据**：
   - 可选填写，用于分析最佳时段
   - 帮助优化时间安排

### 📊 **数据一致性**

确保数据一致性：
```
tomato_actual = tomato_morning + tomato_afternoon + tomato_evening
tomato_rate = (tomato_actual / tomato_goal) × 100
```

### 🔄 **历史数据维护**

1. **定期检查**：每周检查数据完整性
2. **批量更新**：使用查找替换功能批量修正
3. **备份数据**：定期备份重要的历史数据

## 📈 **进阶功能**

### 🎨 **个性化配置**

可以根据个人需求添加更多属性：

```yaml
# 扩展属性
tomato_focus_score: 8    # 专注度评分(1-10)
tomato_energy_level: 7   # 精力水平(1-10)
tomato_interruptions: 2  # 被打断次数
tomato_best_time: "上午" # 最佳时段
```

### 📊 **自定义分析**

在历史仪表盘中可以添加：
- 专注度趋势分析
- 精力水平与效率关联
- 打断因素统计
- 最佳时段识别

### 🔗 **系统集成**

可以与其他系统集成：
- 日历应用同步
- 时间追踪工具
- 项目管理平台

## 🎉 **预期效果**

使用这套增强系统后，你将获得：

1. **完整的历史数据**：永久保存每日番茄钟记录
2. **趋势分析能力**：识别效率模式和改进空间
3. **智能对比功能**：与昨日、本周、历史数据对比
4. **个性化建议**：基于数据的改进建议
5. **可视化展示**：直观的图表和统计信息

---

*这套增强模板将让你的番茄钟管理更加科学和高效！*
