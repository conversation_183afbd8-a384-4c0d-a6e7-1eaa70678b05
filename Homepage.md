---
created: 2025-05-25T15:30
updated: 2025-05-25T15:30
tags:
  - type/homepage
  - dashboard/main
  - system/navigation
---

# 🏠 知识管理系统主页

> 🌟 欢迎回到您的数字花园，让每一天都充满成长与收获

## 🌅 今日概览

```dataviewjs
// 获取当前时间信息
const now = new Date();
const today = now.toISOString().split('T')[0];
const hour = now.getHours();
const dayOfWeek = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][now.getDay()];
const weekNumber = Math.ceil((now - new Date(now.getFullYear(), 0, 1)) / (7 * 24 * 60 * 60 * 1000));

// 问候语
let greeting = '';
if (hour < 6) greeting = '🌙 深夜好';
else if (hour < 12) greeting = '🌅 早上好';
else if (hour < 14) greeting = '☀️ 中午好';
else if (hour < 18) greeting = '🌤️ 下午好';
else if (hour < 22) greeting = '🌆 晚上好';
else greeting = '🌙 夜深了';

// 获取今日日记
const todayDailyLog = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.includes(today))
    .first();

let tomatoGoal = 0;
let tomatoActual = 0;
if (todayDailyLog) {
    tomatoGoal = todayDailyLog.tomato_goal || 0;
    tomatoActual = todayDailyLog.tomato_actual || 0;
}

// 获取今日任务
const allTasks = dv.pages().file.tasks;
const todayTasks = allTasks.filter(task => {
    if (task.completed) return false;
    const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
    return dueDateMatch && dueDateMatch[1] === today;
});

const inProgressTasks = allTasks.filter(task =>
    !task.completed && (task.text.includes('🔄') || task.text.includes('进行中'))
);

// 创建主页容器
const container = this.container;
container.innerHTML = '';

// 主页样式
const mainStyle = `
    background: linear-gradient(135deg, #F8F6F3 0%, #FFF9F5 50%, #F5F2EF 100%);
    border-radius: 24px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 12px 40px rgba(139, 115, 85, 0.12);
    border: 2px solid rgba(232, 180, 160, 0.15);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
`;

// 创建主容器
const mainContainer = document.createElement('div');
mainContainer.style.cssText = mainStyle;

// 欢迎标题
const welcomeSection = document.createElement('div');
welcomeSection.style.cssText = `
    text-align: center;
    margin-bottom: 35px;
    padding: 25px;
    background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%);
    border-radius: 20px;
    border: 1px solid rgba(232, 180, 160, 0.2);
`;

welcomeSection.innerHTML = `
    <div style="font-size: 2.2em; font-weight: 700; color: #8B7355; margin-bottom: 12px;">
        ${greeting}
    </div>
    <div style="font-size: 1.3em; color: #A0896B; margin-bottom: 15px;">
        📅 ${today} ${dayOfWeek} 第${weekNumber}周
    </div>
    <div style="font-size: 1em; color: #B8A082; line-height: 1.6;">
        🌸 今日任务: ${todayTasks.length} 个 | 🔄 进行中: ${inProgressTasks.length} 个 | 🍅 番茄钟: ${tomatoActual}/${tomatoGoal}
    </div>
`;

mainContainer.appendChild(welcomeSection);
container.appendChild(mainContainer);
```

## 🚀 快速导航

```dataviewjs
// 导航数据
const navigationItems = [
    {
        title: '任务管理',
        icon: '📋',
        color: '#E8B4A0',
        bgColor: '#FFF8F5',
        items: [
            { name: '任务仪表盘-治愈系', link: '任务仪表盘-治愈系奶茶风', desc: '温馨的任务管理' },
            { name: '任务仪表盘-莫兰迪', link: '任务仪表盘-莫兰迪主题', desc: '简约的任务视图' },
            { name: '极简任务管理', link: '极简任务管理系统', desc: '轻量级任务管理' },
            { name: '番茄钟历史', link: '番茄钟历史数据仪表盘', desc: '专注时间统计' }
        ]
    },
    {
        title: '项目管理',
        icon: '🎯',
        color: '#A5D6E8',
        bgColor: '#F0F8FF',
        items: [
            { name: '项目仪表盘', link: '项目仪表盘', desc: '项目进度总览' },
            { name: '项目笔记浏览器', link: '项目笔记浏览器', desc: '浏览项目文件' },
            { name: '目标管理', link: '3_Permanent notes/3_0_Goal目标', desc: '长期目标规划' }
        ]
    },
    {
        title: '知识管理',
        icon: '📚',
        color: '#B8D6B8',
        bgColor: '#F8FFF8',
        items: [
            { name: '知识库概览', link: '知识库概览仪表板', desc: '知识库统计分析' },
            { name: '文献笔记浏览器', link: '文献笔记浏览器', desc: '学习资料收集' },
            { name: '永久笔记浏览器', link: '永久笔记浏览器', desc: '核心知识沉淀' },
            { name: '结构笔记浏览器', link: '结构笔记浏览器', desc: '知识体系架构' }
        ]
    },
    {
        title: 'AI & 工具',
        icon: '🤖',
        color: '#D0C4E8',
        bgColor: '#F8F5FF',
        items: [
            { name: 'AI最佳实践', link: '5_Structures/系统/AI/AI最佳实践工作流', desc: 'AI使用指南' },
            { name: 'Prompt合集', link: 'Assets/☁️常用Prompt合集', desc: '提示词库' },
            { name: '工具库', link: 'Assets/工具库', desc: '常用工具集合' },
            { name: '资料库', link: '资料库浏览器', desc: '学习资料管理' }
        ]
    },
    {
        title: '精力管理',
        icon: '⚡',
        color: '#E8D5A5',
        bgColor: '#FFFBF0',
        items: [
            { name: '精力仪表盘', link: '6_Project Notes/精力/精力仪表盘', desc: '健康状态监控' },
            { name: '精力记录查询', link: '6_Project Notes/精力/精力记录查询', desc: '历史记录查看' },
            { name: '睡眠日志统计', link: '0_Bullet Journal/睡眠日志/睡眠日志统计图表', desc: '睡眠质量分析' }
        ]
    },
    {
        title: '日志系统',
        icon: '📝',
        color: '#E8A5A5',
        bgColor: '#FFF5F5',
        items: [
            { name: '今日日记', link: '日志浏览器', desc: '今天的记录' },
            { name: '周记浏览器', link: '周记浏览器', desc: '每周总结' },
            { name: '月记浏览器', link: '月记浏览器', desc: '月度反思' },
            { name: '回顾仪表板', link: '回顾仪表板', desc: '回顾统计' }
        ]
    }
];

// 创建导航容器
const navContainer = document.createElement('div');
navContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    margin: 25px 0;
`;

navigationItems.forEach(section => {
    const sectionDiv = document.createElement('div');
    sectionDiv.style.cssText = `
        background: ${section.bgColor};
        border-radius: 18px;
        padding: 22px;
        border: 2px solid ${section.color}30;
        box-shadow: 0 6px 24px rgba(139, 115, 85, 0.08);
        transition: all 0.3s ease;
    `;

    let itemsHTML = '';
    section.items.forEach(item => {
        itemsHTML += `
            <div style="margin: 12px 0; padding: 12px; background: rgba(255,255,255,0.6);
                        border-radius: 12px; transition: all 0.2s ease; cursor: pointer;"
                 class="nav-item" data-link="${item.link}">
                <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px;">
                    ${item.name}
                </div>
                <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                    ${item.desc}
                </div>
            </div>
        `;
    });

    sectionDiv.innerHTML = `
        <div style="display: flex; align-items: center; margin-bottom: 18px;">
            <span style="font-size: 1.8em; margin-right: 12px;">${section.icon}</span>
            <h3 style="margin: 0; color: ${section.color}; font-weight: 600; font-size: 1.2em;">
                ${section.title}
            </h3>
        </div>
        ${itemsHTML}
    `;

    // 添加悬停效果
    sectionDiv.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px)';
        this.style.boxShadow = '0 12px 32px rgba(139, 115, 85, 0.15)';
    });

    sectionDiv.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 6px 24px rgba(139, 115, 85, 0.08)';
    });

    navContainer.appendChild(sectionDiv);
});

// 添加点击事件
setTimeout(() => {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const link = this.getAttribute('data-link');
            if (link && app && app.workspace) {
                try {
                    app.workspace.openLinkText(link, '', false);
                } catch (error) {
                    console.log('无法打开文件:', link);
                    // 可以在这里添加用户友好的提示
                }
            }
        });

        // 添加悬停效果
        item.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(255,255,255,0.9)';
            this.style.transform = 'translateX(8px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(255,255,255,0.6)';
            this.style.transform = 'translateX(0)';
        });
    });
}, 100);

this.container.appendChild(navContainer);
```

## 📊 今日聚焦

```dataviewjs
// 创建今日聚焦面板
const focusContainer = document.createElement('div');
focusContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 25px 0;
`;

// 获取数据
const today = new Date().toISOString().split('T')[0];
const allTasks = dv.pages().file.tasks;

// 今日任务统计
const todayTasks = allTasks.filter(task => {
    if (task.completed) return false;
    const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
    return dueDateMatch && dueDateMatch[1] === today;
});

const urgentTasks = allTasks.filter(task =>
    !task.completed && task.text.includes('⏫')
);

const inProgressTasks = allTasks.filter(task =>
    !task.completed && (task.text.includes('🔄') || task.text.includes('进行中'))
);

// 获取今日番茄钟数据
const todayDailyLog = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.includes(today))
    .first();

let tomatoGoal = 0;
let tomatoActual = 0;
if (todayDailyLog) {
    tomatoGoal = todayDailyLog.tomato_goal || 0;
    tomatoActual = todayDailyLog.tomato_actual || 0;
}

// 获取活跃项目
const activeProjects = dv.pages('"6_Project Notes"')
    .where(p => p.Status === 'active' || p.status === '进行中')
    .limit(5);

// 创建聚焦卡片
const focusCards = [
    {
        title: '今日任务',
        icon: '🎯',
        count: todayTasks.length,
        color: '#E8B4A0',
        bgColor: '#FFF8F5',
        action: '查看详情',
        link: '任务仪表盘-治愈系奶茶风'
    },
    {
        title: '紧急任务',
        icon: '🚨',
        count: urgentTasks.length,
        color: '#E8A5A5',
        bgColor: '#FFF5F5',
        action: '立即处理',
        link: '任务仪表盘-治愈系奶茶风'
    },
    {
        title: '进行中',
        icon: '🔄',
        count: inProgressTasks.length,
        color: '#A5D6E8',
        bgColor: '#F0F8FF',
        action: '继续工作',
        link: '任务仪表盘-治愈系奶茶风'
    },
    {
        title: '番茄钟',
        icon: '🍅',
        count: `${tomatoActual}/${tomatoGoal}`,
        color: '#E8D5A5',
        bgColor: '#FFFBF0',
        action: '专注时间',
        link: '番茄钟历史数据仪表盘'
    }
];

focusCards.forEach(card => {
    const cardElement = document.createElement('div');
    cardElement.style.cssText = `
        background: ${card.bgColor};
        border-radius: 18px;
        padding: 24px;
        border: 2px solid ${card.color}30;
        box-shadow: 0 6px 24px rgba(139, 115, 85, 0.08);
        transition: all 0.3s ease;
        cursor: pointer;
        text-align: center;
    `;

    cardElement.innerHTML = `
        <div style="font-size: 2.5em; margin-bottom: 12px;">${card.icon}</div>
        <div style="font-size: 2em; font-weight: 700; color: ${card.color}; margin-bottom: 8px;">
            ${card.count}
        </div>
        <div style="font-size: 1.1em; font-weight: 600; color: #8B7355; margin-bottom: 12px;">
            ${card.title}
        </div>
        <button style="background: ${card.color}; color: white; border: none; border-radius: 12px;
                       padding: 8px 16px; cursor: pointer; font-weight: 500; font-size: 0.9em;
                       box-shadow: 0 4px 12px ${card.color}40; transition: all 0.2s ease;">
            ${card.action}
        </button>
    `;

    // 添加交互效果
    cardElement.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-6px) scale(1.02)';
        this.style.boxShadow = '0 12px 32px rgba(139, 115, 85, 0.15)';
    });

    cardElement.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 6px 24px rgba(139, 115, 85, 0.08)';
    });

    cardElement.addEventListener('click', function() {
        if (app && app.workspace) {
            app.workspace.openLinkText(card.link, '', false);
        }
    });

    focusContainer.appendChild(cardElement);
});

this.container.appendChild(focusContainer);
```

## 🌟 知识库概览

```dataviewjs
// 创建知识库概览容器
const overviewContainer = document.createElement('div');
overviewContainer.style.cssText = `
    background: linear-gradient(135deg, #F8F6F3 0%, #FFF9F5 100%);
    border-radius: 20px;
    padding: 25px;
    margin: 25px 0;
    border: 2px solid rgba(232, 180, 160, 0.15);
    box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1);
`;

// 获取统计数据
const allPages = dv.pages('-"Templates"');
const totalPages = allPages.length;

// 按文件夹统计
const folderStats = [
    { name: '日志', path: '0_Bullet Journal', icon: '📅', color: '#E8B4A0' },
    { name: '文献', path: '2_Literature notes', icon: '📚', color: '#A5D6E8' },
    { name: '永久', path: '3_Permanent notes', icon: '📘', color: '#B8D6B8' },
    { name: '结构', path: '5_Structures', icon: '🏗️', color: '#E8D5A5' },
    { name: '项目', path: '6_Project Notes', icon: '📋', color: '#D0C4E8' }
];

let statsHTML = `
    <div style="text-align: center; margin-bottom: 25px;">
        <h3 style="color: #8B7355; font-weight: 600; font-size: 1.3em; margin-bottom: 8px;">
            📊 知识库统计
        </h3>
        <div style="font-size: 1.1em; color: #A0896B;">
            总计 <strong style="color: #D4A574;">${totalPages}</strong> 个文档
        </div>
    </div>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
`;

folderStats.forEach(folder => {
    const count = dv.pages(`"${folder.path}"`).length;
    const percentage = totalPages > 0 ? Math.round((count / totalPages) * 100) : 0;

    statsHTML += `
        <div style="background: rgba(255,255,255,0.7); border-radius: 14px; padding: 18px; text-align: center;
                    border: 1px solid ${folder.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="folder-stat" data-path="${folder.path}">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${folder.icon}</div>
            <div style="font-size: 1.5em; font-weight: 700; color: ${folder.color}; margin-bottom: 4px;">
                ${count}
            </div>
            <div style="font-size: 0.85em; color: #8B7355; font-weight: 500;">
                ${folder.name}
            </div>
            <div style="font-size: 0.75em; color: #A0896B; margin-top: 4px;">
                ${percentage}%
            </div>
        </div>
    `;
});

statsHTML += '</div>';

overviewContainer.innerHTML = statsHTML;

// 添加点击事件
setTimeout(() => {
    const folderStats = overviewContainer.querySelectorAll('.folder-stat');
    folderStats.forEach(stat => {
        stat.addEventListener('click', function() {
            const path = this.getAttribute('data-path');
            if (app && app.workspace) {
                app.workspace.openLinkText(path, '', false);
            }
        });

        stat.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        stat.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(overviewContainer);
```

## 🎯 活跃项目

```dataviewjs
// 创建项目状态容器
const projectContainer = document.createElement('div');
projectContainer.style.cssText = `
    background: linear-gradient(135deg, #F0F8FF 0%, #F8F5FF 100%);
    border-radius: 20px;
    padding: 25px;
    margin: 25px 0;
    border: 2px solid rgba(165, 214, 232, 0.3);
    box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1);
`;

// 获取项目数据
const allProjects = dv.pages('"6_Project Notes"');
const activeProjects = allProjects.where(p => p.Status === 'active' || p.status === '进行中');
const completedProjects = allProjects.where(p => p.Status === 'completed' || p.status === '已完成');
const riskProjects = allProjects.where(p => p.Status === 'risk' || p.status === '风险');

let projectHTML = `
    <div style="text-align: center; margin-bottom: 25px;">
        <h3 style="color: #A5D6E8; font-weight: 600; font-size: 1.3em; margin-bottom: 8px;">
            🎯 项目状态总览
        </h3>
        <div style="display: flex; justify-content: center; gap: 20px; margin-top: 15px;">
            <span style="background: rgba(165, 214, 232, 0.2); padding: 6px 12px; border-radius: 12px; font-size: 0.9em;">
                🟢 进行中: ${activeProjects.length}
            </span>
            <span style="background: rgba(168, 200, 168, 0.2); padding: 6px 12px; border-radius: 12px; font-size: 0.9em;">
                ✅ 已完成: ${completedProjects.length}
            </span>
            <span style="background: rgba(232, 165, 165, 0.2); padding: 6px 12px; border-radius: 12px; font-size: 0.9em;">
                🔴 风险: ${riskProjects.length}
            </span>
        </div>
    </div>
`;

if (activeProjects.length > 0) {
    projectHTML += `<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">`;

    activeProjects.limit(6).forEach(project => {
        const updateDate = project.updated ? new Date(project.updated).toLocaleDateString('zh-CN') : '未知';
        projectHTML += `
            <div style="background: rgba(255,255,255,0.8); border-radius: 14px; padding: 18px;
                        border: 1px solid rgba(165, 214, 232, 0.3); transition: all 0.2s ease; cursor: pointer;"
                 class="project-card" data-link="${project.file.name}">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                    <span style="font-size: 1.2em; margin-right: 8px;">📋</span>
                    <div style="font-weight: 600; color: #8B7355; font-size: 1em;">
                        ${project.file.name}
                    </div>
                </div>
                <div style="font-size: 0.85em; color: #A0896B; margin-bottom: 8px;">
                    📅 更新: ${updateDate}
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="background: rgba(165, 214, 232, 0.3); color: #A5D6E8;
                                 padding: 4px 8px; border-radius: 8px; font-size: 0.8em; font-weight: 500;">
                        🟢 进行中
                    </span>
                    <button style="background: #A5D6E8; color: white; border: none; border-radius: 8px;
                                   padding: 4px 12px; cursor: pointer; font-size: 0.8em; font-weight: 500;">
                        查看
                    </button>
                </div>
            </div>
        `;
    });

    projectHTML += '</div>';
} else {
    projectHTML += `
        <div style="text-align: center; padding: 40px; color: #A0896B;">
            <div style="font-size: 3em; margin-bottom: 15px;">🌸</div>
            <p style="font-size: 1.1em;">暂无活跃项目</p>
            <p style="font-size: 0.9em; opacity: 0.7;">是时候开始新的项目了～</p>
        </div>
    `;
}

projectContainer.innerHTML = projectHTML;

// 添加点击事件
setTimeout(() => {
    const projectCards = projectContainer.querySelectorAll('.project-card');
    projectCards.forEach(card => {
        card.addEventListener('click', function() {
            const link = this.getAttribute('data-link');
            if (app && app.workspace) {
                app.workspace.openLinkText(`6_Project Notes/${link}`, '', false);
            }
        });

        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(projectContainer);
```

## ⚡ 精力状态

```dataviewjs
// 创建精力状态容器
const energyContainer = document.createElement('div');
energyContainer.style.cssText = `
    background: linear-gradient(135deg, #FFFBF0 0%, #FFF8F0 100%);
    border-radius: 20px;
    padding: 25px;
    margin: 25px 0;
    border: 2px solid rgba(232, 213, 165, 0.3);
    box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1);
`;

// 获取最近的精力记录
const today = new Date().toISOString().split('T')[0];
const recentDays = [];
for (let i = 0; i < 7; i++) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    recentDays.push(date.toISOString().split('T')[0]);
}

let totalEnergyRecords = 0;
let hasEnergyData = false;

// 检查是否有精力记录
const energyPages = dv.pages('"0_Bullet Journal/Daily Notes"');
energyPages.forEach(page => {
    if (page.file.name.includes('2025-')) {
        // 检查是否有精力相关的属性或内容
        if (page.tomato_actual || page.tomato_goal || page.file.content?.includes('精力') || page.file.content?.includes('能量')) {
            totalEnergyRecords++;
            hasEnergyData = true;
        }
    }
});

let energyHTML = `
    <div style="text-align: center; margin-bottom: 25px;">
        <h3 style="color: #E8D5A5; font-weight: 600; font-size: 1.3em; margin-bottom: 8px;">
            ⚡ 精力管理中心
        </h3>
        <div style="font-size: 1em; color: #A0896B;">
            已记录 <strong style="color: #D4A574;">${totalEnergyRecords}</strong> 天的精力数据
        </div>
    </div>
`;

// 创建精力管理快捷入口
const energyActions = [
    {
        title: '精力仪表盘',
        icon: '📊',
        desc: '查看精力统计',
        link: '6_Project Notes/精力/精力仪表盘',
        color: '#E8D5A5'
    },
    {
        title: '记录精力',
        icon: '✍️',
        desc: '添加今日记录',
        link: `0_Bullet Journal/Daily Notes/${today}`,
        color: '#B8D6B8'
    },
    {
        title: '睡眠分析',
        icon: '😴',
        desc: '睡眠质量统计',
        link: '0_Bullet Journal/睡眠日志/睡眠日志统计图表',
        color: '#D0C4E8'
    },
    {
        title: '历史查询',
        icon: '🔍',
        desc: '查看历史记录',
        link: '6_Project Notes/精力/精力记录查询',
        color: '#E8B4A0'
    }
];

energyHTML += `<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">`;

energyActions.forEach(action => {
    energyHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 14px; padding: 20px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="energy-action" data-link="${action.link}">
            <div style="font-size: 2em; margin-bottom: 10px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 6px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

energyHTML += '</div>';

energyContainer.innerHTML = energyHTML;

// 添加点击事件
setTimeout(() => {
    const energyActions = energyContainer.querySelectorAll('.energy-action');
    energyActions.forEach(action => {
        action.addEventListener('click', function() {
            const link = this.getAttribute('data-link');
            if (app && app.workspace) {
                app.workspace.openLinkText(link, '', false);
            }
        });

        action.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        action.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(energyContainer);
```

## 🚀 快速操作

```dataviewjs
// 创建快速操作容器
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    background: linear-gradient(135deg, #F8F5FF 0%, #FFF5F8 100%);
    border-radius: 20px;
    padding: 25px;
    margin: 25px 0;
    border: 2px solid rgba(208, 196, 232, 0.3);
    box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1);
`;

const today = new Date().toISOString().split('T')[0];
const todayFormatted = today.replace(/-/g, '-');

// 快速操作数据
const quickActions = [
    {
        title: '新建日记',
        icon: '📝',
        desc: '创建今日日记',
        action: () => {
            if (app && app.workspace) {
                // 创建正确的日期格式：YYYY-MM-DD ddd WW
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const dayOfWeek = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][now.getDay()];
                const weekNumber = Math.ceil((now - new Date(now.getFullYear(), 0, 1)) / (7 * 24 * 60 * 60 * 1000));

                const formattedDate = `${year}-${month}-${day} ${dayOfWeek} ${String(weekNumber).padStart(2, '0')}`;
                const todayFile = `0_Bullet Journal/Daily Notes/${formattedDate}`;
                app.workspace.openLinkText(todayFile, '', true);
            }
        },
        color: '#E8A5A5'
    },
    {
        title: '添加任务',
        icon: '✅',
        desc: '快速添加新任务',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('任务快速输入器', '', false);
            }
        },
        color: '#A5D6E8'
    },
    {
        title: '新建项目',
        icon: '🎯',
        desc: '创建新项目',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates/Project Template', '', false);
            }
        },
        color: '#B8D6B8'
    },
    {
        title: '记录想法',
        icon: '💡',
        desc: '捕捉灵感',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('1_Fleeting notes', '', false);
            }
        },
        color: '#E8D5A5'
    },
    {
        title: '查看模板',
        icon: '📋',
        desc: '所有模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates', '', false);
            }
        },
        color: '#D0C4E8'
    },
    {
        title: '搜索笔记',
        icon: '🔍',
        desc: '全局搜索',
        action: () => {
            if (app && app.commands) {
                app.commands.executeCommandById('global-search:open');
            } else if (app && app.workspace) {
                // 备用方案：打开搜索面板
                app.workspace.openLinkText('', '', false);
            }
        },
        color: '#E8B4A0'
    }
];

let quickHTML = `
    <div style="text-align: center; margin-bottom: 25px;">
        <h3 style="color: #D0C4E8; font-weight: 600; font-size: 1.3em; margin-bottom: 8px;">
            🚀 快速操作
        </h3>
        <div style="font-size: 1em; color: #A0896B;">
            常用功能一键直达
        </div>
    </div>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); gap: 15px;">
`;

quickActions.forEach((action, index) => {
    quickHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 14px; padding: 20px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action" data-index="${index}">
            <div style="font-size: 2.2em; margin-bottom: 10px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 6px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickHTML += '</div>';

quickActionsContainer.innerHTML = quickHTML;

// 添加点击事件
setTimeout(() => {
    const actionElements = quickActionsContainer.querySelectorAll('.quick-action');
    actionElements.forEach((element, index) => {
        element.addEventListener('click', function() {
            quickActions[index].action();
        });

        element.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-6px) scale(1.05)';
            this.style.boxShadow = '0 12px 24px rgba(139, 115, 85, 0.15)';
        });

        element.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 📈 最近更新

```dataview
TABLE WITHOUT ID
    file.link AS "📄 文档",
    choice(importance = "核心", "🔴 核心", choice(importance = "重要", "🟡 重要", "⚪ 一般")) AS "重要性",
    choice(subject, subject, "📝 未分类") AS "学科",
    updated AS "🕒 更新时间"
FROM -"Templates" AND -"Attachment"
WHERE updated
SORT updated DESC
LIMIT 8
```

## 🎨 主题说明

> 💡 **设计理念**: 这个主页采用了温馨治愈的设计风格，与您的任务仪表盘保持一致的视觉体验。

### 🌈 色彩搭配
- **主色调**: 奶茶色系 (#F8F6F3, #FFF9F5)
- **强调色**: 温暖的橙色 (#E8B4A0, #D4A574)
- **辅助色**: 柔和的蓝色、绿色、紫色等

### ✨ 功能特色
1. **智能问候**: 根据时间显示不同的问候语
2. **实时数据**: 动态显示今日任务、番茄钟等数据
3. **快速导航**: 一键跳转到各个功能模块
4. **响应式设计**: 自适应不同屏幕尺寸
5. **交互动效**: 悬停和点击效果提升用户体验

### 🔧 自定义建议
- 可以根据个人喜好调整颜色主题
- 可以添加或删除导航模块
- 可以修改快速操作的功能
- 可以调整数据显示的内容

---

<div style="text-align: center; margin-top: 40px; padding: 20px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 16px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.2em; color: #8B7355; font-weight: 600; margin-bottom: 8px;">
        🌸 愿您的每一天都充满成长与收获 🌸
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        "知识管理不仅是整理信息，更是塑造思维的过程"
    </div>
</div>
