---
tags:
  - type/index
  - index/energy
created: 2025-05-16T10:58
updated: 2025-05-16T10:58
---

# 精力历史记录索引

这个索引文件帮助您快速查找和访问历史精力记录，特别是那些使用旧格式记录的内容。

## 补充剂相关记录

### 维生素D
- [[2025-04-02 周三 14]] (start::12:43) ：【健康】精力 *记录* 中医 调理 **吃维生素D**
- [[2025-04-01 周二 14]] (start::10:42) ：【健康】精力 *记录* 中医 调理 **吃维生素D**
- [[2025-03-31 周一 14]] (start::09:41) ：【健康】精力 *记录* 中医 调理 **吃维生素D**

### 钙片
- [[2025-04-01 周二 14]] (start::18:59) ：【健康】精力 *记录* 中医 调理 **吃维生素D+钙片**
- [[2025-03-31 周一 14]] (start::17:59) ：【健康】精力 *记录* 中医 调理 **吃维生素D+钙片**

## 中医调理记录

### 健胰玉液饮茶
- [[2025-03-31 周一 14]] (start::14:14) ：【健康】精力 *记录* 中医 调理 **喝健胰玉液饮茶**X3

## 习惯记录

### SY记录
- [[2025-04-02 周三 14]] (start::11:03) ：【健康】精力 *记录* 中医 调理 **SY**
- [[2025-04-01 周二 14]] (start::21:59) ：【健康】精力 *记录* 中医 调理 **SY**

## 精力恢复方法

### 能量恢复技巧
- [[2025-04-01 周二 14]] (start::11:18) ：【健康】【精神】*精力* **能量恢复** 光脚踩草地～吸收负离子排走静电，大树下打盹～吸收抗菌物质提高免疫力，溪边发呆～调节脑波频率缓解焦虑。

## 睡眠管理

### 睡眠质量
- 查看睡眠日志统计图表: [[睡眠日志统计图表7]]

## 西医治疗

### 医院检查
- [[2025-03-28 周五 13]] 医院检查维生素D、钙、磷 ✅ 2025-03-28

## 如何使用本索引

1. **查找特定类型记录**：
   - 浏览对应分类下的历史记录
   - 点击日期链接跳转到原始日记

2. **添加新的历史记录**：
   - 当您在日记中发现重要的精力记录时
   - 复制记录内容到本索引的对应分类下
   - 添加日记链接

3. **分析历史趋势**：
   - 查看同一类型记录的历史变化
   - 分析不同方法的效果差异

## 自动索引脚本

以下是一个简单的脚本，可以帮助您自动从日记中提取精力记录并按类型分类：

```dataviewjs
// 获取所有日记文件
const dailyNotes = dv.pages('"0_Bullet Journal/Daily Notes"')
    .sort(p => p.file.name, 'desc');

// 定义类型关键词
const typeKeywords = {
    '补充剂': ['维生素', '钙片', '补充剂', '营养素'],
    '中医': ['中医', '调理', '健胰玉液', '中药'],
    '习惯': ['sy', 'sy频率', '梦遗', '习惯'],
    '恢复': ['恢复', '放松', '冥想', '光脚'],
    '调理': ['调理', '腹泻', '感冒', '症状'],
    '西医': ['西医', '医院', '检查', '西药']
};

// 按类型分类记录
const recordsByType = {};
Object.keys(typeKeywords).forEach(type => {
    recordsByType[type] = [];
});

// 提取记录
dailyNotes.file.lists
    .where(li => li.text.includes("【健康】精力"))
    .forEach(record => {
        const lowerText = record.text.toLowerCase();
        let matched = false;

        // 检查每种类型的关键词
        Object.entries(typeKeywords).forEach(([type, keywords]) => {
            if (!matched && keywords.some(kw => lowerText.includes(kw.toLowerCase()))) {
                recordsByType[type].push({
                    text: record.text,
                    link: record.link
                });
                matched = true;
            }
        });

        // 如果没有匹配任何类型，归为"其他"
        if (!matched) {
            if (!recordsByType['其他']) recordsByType['其他'] = [];
            recordsByType['其他'].push({
                text: record.text,
                link: record.link
            });
        }
    });

// 显示分类结果
Object.entries(recordsByType).forEach(([type, records]) => {
    if (records.length > 0) {
        dv.header(3, type);
        dv.list(records.map(r => `${r.text} (来自: ${r.link})`));
    }
});
```

您可以运行此脚本来自动生成最新的精力记录索引。
