---
tags:
  - type/structure
  - structure/bujo/monthly
start_date: 2025-05-01
end_date: 2025-05-31
template: "[[5_BuJo - Monthly Log]]"
created: 2025-03-31T10:00:00
updated: 2025-05-14T18:54
---
# 📅 月度回顾 MONTHLY REVIEW - 2025年5月

## 📊 月度精华内容

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 创建表格来显示精华内容
const rows = [];

for (const page of pages) {
    // 获取文件内容
    const content = await dv.io.load(page.file.path);

    // 查找包含 #精华 标签的行
    const lines = content.split("\n");
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes("#精华")) {
            // 提取文件名部分
            const fileName = page.file.name.replace(".md", ""); // 移除.md扩展名

            // 提取内容（去除时间戳和标签）
            let highlightContent = lines[i];

            // 移除时间戳部分 (start::XX:XX)
            highlightContent = highlightContent.replace(/\(start::\d+:\d+\)/g, "");

            // 移除 #精华 标签
            highlightContent = highlightContent.replace(/#精华/g, "");

            // 清理多余空格
            highlightContent = highlightContent.trim();

            // 添加到结果数组
            rows.push({
                fileName: fileName,
                content: highlightContent,
                link: page.file.link
            });
        }
    }
}

// 显示结果
if (rows.length > 0) {
    dv.table(["日期", "内容"],
        rows.map(row => [
            `[[${row.fileName}|${row.fileName}]]`,
            row.content
        ])
    );
} else {
    dv.paragraph("本月暂无精华内容");
}
```

## 📈 月度习惯统计

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 定义要跟踪的习惯
const habits = [
  { key: 'stretch', label: '🧘 晨起拉伸' },
  { key: 'journal', label: '📓 晨间日记' },
  { key: 'notes', label: '💻 整理笔记' },
  { key: 'running', label: '🏃 有氧慢跑' },
  { key: 'reading', label: '📖 睡前阅读' }
];

// 初始化统计数据
const habitStats = {};
habits.forEach(habit => {
    habitStats[habit.key] = {
        completed: 0,
        total: pages.length,
        percentage: 0
    };
});

// 统计每个习惯的完成情况
pages.forEach(page => {
    habits.forEach(habit => {
        if (page[habit.key]) {
            habitStats[habit.key].completed++;
        }
    });
});

// 计算完成百分比
habits.forEach(habit => {
    habitStats[habit.key].percentage = Math.round((habitStats[habit.key].completed / habitStats[habit.key].total) * 100);
});

// 显示结果
dv.table(["习惯", "完成次数", "总天数", "完成率"],
    habits.map(habit => [
        habit.label,
        habitStats[habit.key].completed,
        habitStats[habit.key].total,
        `${habitStats[habit.key].percentage}%`
    ])
);
```

## 🏃 运动数据统计

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 初始化运动数据
let totalDistance = 0;
let runningDays = 0;
let runningData = [];

// 收集运动数据
pages.forEach(page => {
    if (page.running && page.Distance_km) {
        runningDays++;
        totalDistance += parseFloat(page.Distance_km);

        // 提取日期用于显示
        const dateStr = page.file.name.split(" ")[0];

        // 存储每天的数据用于表格显示
        runningData.push({
            date: dateStr,
            distance: page.Distance_km,
            pace: page.Pace_per_km || "-",
            heartRate: page.Heart_Rate_BPM || "-"
        });
    }
});

// 显示汇总数据
dv.header(3, "运动汇总");
dv.paragraph(`- **总跑步天数**: ${runningDays}天`);
dv.paragraph(`- **总跑步距离**: ${totalDistance.toFixed(2)}公里`);
dv.paragraph(`- **平均每次距离**: ${runningDays > 0 ? (totalDistance / runningDays).toFixed(2) : 0}公里`);

// 显示每日数据表格
if (runningData.length > 0) {
    dv.header(3, "每日运动数据");
    dv.table(["日期", "距离(km)", "配速(/km)", "心率(BPM)"],
        runningData.map(d => [
            d.date,
            d.distance,
            d.pace,
            d.heartRate
        ])
    );
} else {
    dv.paragraph("本月暂无运动数据");
}
```

## 😴 睡眠数据统计

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 准备数据
let totalSleepTime = 0;
let totalTimeInBed = 0;
let totalSleepQuality = 0;
let daysWithSleepData = 0;

// 处理每一天的数据
for (let page of pages) {
    if (page.sleep_bedtime && page.sleep_fallasleep && page.sleep_wakeup && page.sleep_outofbed) {
        daysWithSleepData++;

        // 转换时间字符串为分钟数
        function timeToMinutes(timeStr) {
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
        }

        // 处理跨天的情况
        function calculateDuration(start, end) {
            let startMin = timeToMinutes(start);
            let endMin = timeToMinutes(end);

            // 如果结束时间小于开始时间，假设跨天
            if (endMin < startMin) {
                endMin += 24 * 60;
            }

            return endMin - startMin;
        }

        // 计算各项指标
        const totalSleepTimeToday = calculateDuration(page.sleep_fallasleep, page.sleep_wakeup);
        const timeInBedToday = calculateDuration(page.sleep_bedtime, page.sleep_outofbed);

        totalSleepTime += totalSleepTimeToday;
        totalTimeInBed += timeInBedToday;
        totalSleepQuality += page.sleep_quality || 3;
    }
}

// 计算平均值
const avgSleepTime = daysWithSleepData ? totalSleepTime / daysWithSleepData : 0;
const avgTimeInBed = daysWithSleepData ? totalTimeInBed / daysWithSleepData : 0;
const avgSleepQuality = daysWithSleepData ? totalSleepQuality / daysWithSleepData : 0;
const avgSleepEfficiency = avgTimeInBed ? Math.round((avgSleepTime / avgTimeInBed) * 100) : 0;

// 显示汇总数据
dv.header(3, "睡眠汇总");
dv.paragraph(`- **记录天数**: ${daysWithSleepData}天`);
dv.paragraph(`- **平均睡眠时长**: ${Math.floor(avgSleepTime/60)}小时${Math.round(avgSleepTime%60)}分钟`);
dv.paragraph(`- **平均卧床时长**: ${Math.floor(avgTimeInBed/60)}小时${Math.round(avgTimeInBed%60)}分钟`);
dv.paragraph(`- **平均睡眠质量**: ${avgSleepQuality.toFixed(1)}/5`);
dv.paragraph(`- **平均睡眠效率**: ${avgSleepEfficiency}%`);
```

## 🍅 番茄钟月度报告

```dataviewjs
// 数据处理辅助函数
function parseNumber(value) {
    if (value === null || value === undefined || value === '') return 0;
    if (typeof value === 'string') {
        const parsed = parseInt(value.replace(/[^\d]/g, ''));
        return isNaN(parsed) ? 0 : parsed;
    }
    return typeof value === 'number' ? value : 0;
}

// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 收集番茄钟数据
const monthData = [];
let totalGoal = 0;
let totalActual = 0;
let activeDays = 0;
let recordedDays = 0;

pages.forEach(page => {
    recordedDays++;

    // 使用安全的数据解析函数
    const goal = parseNumber(page.tomato_goal);
    const actual = parseNumber(page.tomato_actual);

    // 只有设置了目标或有实际完成数据才算活跃天数
    if (goal > 0 || actual > 0) {
        const rate = goal > 0 ? Math.round((actual / goal) * 100) : 0;

        monthData.push({
            date: page.file.name.split(" ")[0],
            goal: goal,
            actual: actual,
            rate: rate
        });

        totalGoal += goal;
        totalActual += actual;
        activeDays++;
    }
});

// 计算统计数据
const avgGoal = activeDays > 0 ? Math.round(totalGoal / activeDays) : 0;
const avgActual = activeDays > 0 ? Math.round(totalActual / activeDays) : 0;
const overallRate = totalGoal > 0 ? Math.round((totalActual / totalGoal) * 100) : 0;

// 效率分析
const performanceData = monthData.filter(d => d.goal > 0);
const highPerformance = performanceData.filter(d => d.rate >= 90);
const mediumPerformance = performanceData.filter(d => d.rate >= 70 && d.rate < 90);
const lowPerformance = performanceData.filter(d => d.rate < 70);

// 显示月度汇总
dv.header(3, "📊 月度汇总");
dv.paragraph(`- **记录天数**: ${recordedDays}天`);
dv.paragraph(`- **活跃天数**: ${activeDays}天`);
dv.paragraph(`- **总目标**: ${totalGoal}🍅`);
dv.paragraph(`- **总完成**: ${totalActual}🍅`);
dv.paragraph(`- **平均目标**: ${avgGoal}🍅/天`);
dv.paragraph(`- **平均完成**: ${avgActual}🍅/天`);
dv.paragraph(`- **总体达成率**: ${overallRate}%`);

// 显示效率分析
dv.header(3, "🎯 效率分析");
dv.paragraph(`- **高效天数** (≥90%): ${highPerformance.length}天`);
dv.paragraph(`- **中效天数** (70-89%): ${mediumPerformance.length}天`);
dv.paragraph(`- **低效天数** (<70%): ${lowPerformance.length}天`);

// 最佳表现日期
if (highPerformance.length > 0) {
    const bestDays = highPerformance.slice(-5).map(d => d.date.slice(5)).join(', ');
    dv.paragraph(`- **最佳表现日期**: ${bestDays}`);
}

// 显示详细记录表格
if (monthData.length > 0) {
    dv.header(3, "📋 详细记录");
    dv.table(["日期", "目标", "完成", "达成率", "状态"],
        monthData.map(day => {
            const statusIcon = day.rate >= 100 ? '🎉' :
                              day.rate >= 80 ? '👍' : '💪';
            const statusText = day.rate >= 100 ? '超额完成' :
                              day.rate >= 80 ? '良好' : '需努力';

            return [
                day.date,
                `🎯 ${day.goal}`,
                `🍅 ${day.actual}`,
                `${day.rate}%`,
                `${statusIcon} ${statusText}`
            ];
        })
    );
} else {
    dv.paragraph("本月暂无番茄钟数据");
}

// 改进建议
dv.header(3, "💡 改进建议");
let suggestions = [];
if (activeDays === 0) {
    suggestions.push('开始记录番茄钟数据，建议从每日6-8个番茄钟开始');
} else {
    if (lowPerformance.length > highPerformance.length) {
        suggestions.push('考虑降低每日目标，设定更现实的期望');
    }
    if (avgActual < avgGoal * 0.8) {
        suggestions.push('分析任务被打断的原因，优化工作环境');
    }
    if (activeDays < recordedDays * 0.7) {
        suggestions.push('提高记录频率，养成每日复盘的习惯');
    }
    if (overallRate >= 90) {
        suggestions.push('表现优秀！可以考虑适当提高目标挑战自己');
    }
}

if (suggestions.length > 0) {
    suggestions.forEach(suggestion => {
        dv.paragraph(`- ${suggestion}`);
    });
} else {
    dv.paragraph("- 继续保持当前的良好状态！");
}
```

## 📚 阅读统计

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    });

// 按书籍分组统计阅读时间
let bookStats = {};
let totalReadingDays = 0;
let totalReadingTime = 0;

pages.forEach(page => {
    if (page.reading && page.Reading_min && page.book) {
        totalReadingDays++;
        totalReadingTime += parseInt(page.Reading_min);

        if (!bookStats[page.book]) {
            bookStats[page.book] = 0;
        }
        bookStats[page.book] += parseInt(page.Reading_min);
    }
});

// 显示汇总数据
dv.header(3, "阅读汇总");
dv.paragraph(`- **阅读天数**: ${totalReadingDays}天`);
dv.paragraph(`- **总阅读时间**: ${totalReadingTime}分钟 (约${(totalReadingTime/60).toFixed(1)}小时)`);
dv.paragraph(`- **平均每天阅读**: ${totalReadingDays > 0 ? Math.round(totalReadingTime/totalReadingDays) : 0}分钟`);

// 显示每本书的阅读时间
if (Object.keys(bookStats).length > 0) {
    dv.header(3, "书籍阅读时间");
    dv.table(["书名", "阅读时间(分钟)"],
        Object.entries(bookStats).map(([book, time]) => [
            book,
            time
        ]).sort((a, b) => b[1] - a[1]) // 按阅读时间降序排序
    );
} else {
    dv.paragraph("本月暂无阅读数据");
}
```

## 🍚 精神食粮 Spiritual Nourishment

### 🤔 每日斯多葛 Stoic
```dataview
LIST without id Stoic
FROM "0_Bullet Journal/Daily Notes"
WHERE date(split(file.name, " ")[0]) >= date(this.start_date)
    AND date(split(file.name, " ")[0]) <= date(this.end_date)
    AND Stoic
```

### 📖 书籍 Book
```dataview
LIST without id "《" + book + "》" + " 本月共计📖时长" + sum(rows.Reading_min) + "min "
FROM "0_Bullet Journal/Daily Notes"
WHERE date(split(file.name, " ")[0]) >= date(this.start_date)
    AND date(split(file.name, " ")[0]) <= date(this.end_date)
    AND book
GROUP BY book
```

```dataview
LIST
FROM "2_Literature notes/2_2_Book"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 👂播客 Podcast
```dataview
LIST without id podcast
FROM "0_Bullet Journal/Daily Notes"
WHERE date(split(file.name, " ")[0]) >= date(this.start_date)
    AND date(split(file.name, " ")[0]) <= date(this.end_date)
    AND podcast != null
```

### 📑 文章 Article
```dataview
LIST
FROM "2_Literature notes/2_1_Article"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 🎬 电影电视 Movie & TV
```dataview
LIST
FROM "2_Literature notes/2_4_Movie TV"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 📱 视频 Video
```dataview
LIST
FROM "2_Literature notes/2_5_Vedio"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 🎵 音乐 Music
```dataview
LIST
FROM "2_Literature notes/2_6_Music"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 📃小说 Fiction
```dataview
LIST
FROM "2_Literature notes/2_7_Fiction"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```
## ❓ 思考与发问 Thinking & Question
```dataview
LIST
FROM "3_Permanent notes/3_3_Questions问题"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

## 🧰 任务完成情况 Tasks Completion

### ✅ 本月已完成项 Done
```dataviewjs
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 尝试将元数据转换为 Luxon DateTime 对象
const startMoment = startDate ? dv.date(startDate) : null;
const endMoment = endDate ? dv.date(endDate) : null;

if (startMoment && endMoment && startMoment.isValid && endMoment.isValid) {
  // 获取 YYYY-MM-DD 格式的日期字符串
  const startDateStr = startMoment.toISODate();
  // 结束日期加一天作为 'before' 的边界
  const beforeDateStr = endMoment.plus({ days: 1 }).toISODate();

  // 构建包含具体日期的 Tasks 查询字符串
  const tasksQuery = `
\`\`\`tasks
done after ${startDateStr}
done before ${beforeDateStr}
sort by done reverse
short
\`\`\`
  `;
  // 使用 dv.paragraph 输出这个查询块
  dv.paragraph(tasksQuery);
} else {
  dv.paragraph("错误：元数据中缺少有效的 start_date 或 end_date。请检查 Frontmatter。");
}
```

## 📅 周记汇总 Weekly Reviews
```dataview
LIST
FROM "0_Bullet Journal/Weekly Notes"
WHERE contains(file.name, "W")
AND date(start_date) >= date(this.start_date)
AND date(start_date) <= date(this.end_date)
SORT file.name ASC
```
## 🤔 月度回顾与思考 Monthly Review & Thinking

### 🏆 本月亮点 Monthly Highlights
<!-- 本月完成的重要任务？值得庆祝的进展？ -->
-

### 🧗 克服的挑战 Challenges Overcome
<!-- 遇到了哪些困难或障碍？如何应对这些挑战？ -->
-

### 💡 关键洞察 Key Insights
<!-- 学到了什么新知识或技能？有什么新的灵感或想法？ -->
-

### 🛠️ 改进空间 Areas for Improvement
<!-- 哪些方面可以改进？如何在下个月做得更好？ -->
-

### 🌱 成长轨迹 Growth Trajectory
<!-- 本月的进展与长期目标的关系 -->
-

## 🎯 下月计划与目标 Plans & Goals for Next Month

-

## 💻 知识库维护 Knowledge System

### ➕ 本月创建的笔记 Created
```dataview
TABLE without id file.cday AS Date, file.link AS Name, file.folder AS Folder
WHERE file.cday >= date(this.start_date)
    AND file.cday <= date(this.end_date)
    AND contains(file.folder,"Templates") = False
    AND contains(file.folder,"5") = False
SORT file.cday DESC,file.folder ASC
```

### 🔧 本月修改的笔记 Updated
```dataview
TABLE without id file.mday AS Date, file.link AS Name, file.folder AS Folder
WHERE file.mday >= date(this.start_date)
    AND file.mday <= date(this.end_date)
    AND contains(file.folder,"Templates") = False
    AND contains(file.folder,"5") = False
SORT file.mday DESC,file.folder ASC
```
