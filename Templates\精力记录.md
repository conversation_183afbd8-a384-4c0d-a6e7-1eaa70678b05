---
tags:
  - type/energy
  - energy/<% tp.system.suggester(["补充剂", "中医", "习惯", "恢复", "睡眠", "其他"], ["补充剂", "中医", "习惯", "恢复", "睡眠", "其他"]) %>
created: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
updated: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
---

# <% tp.file.title %>

## 基本信息

- **类型**: <% tp.system.suggester(["补充剂", "中医", "习惯", "恢复", "睡眠", "其他"], ["补充剂", "中医", "习惯", "恢复", "睡眠", "其他"]) %>
- **记录日期**: <% tp.date.now("YYYY-MM-DD") %>
- **效果评分**: <% tp.system.suggester(["1 - 很差", "2 - 较差", "3 - 一般", "4 - 良好", "5 - 极佳"], ["1", "2", "3", "4", "5"]) %>

## 详细描述

<% tp.file.cursor(1) %>

## 影响因素

<% tp.file.cursor(2) %>

## 相关记录

```dataviewjs
// 获取当前文件标题
const currentTitle = dv.current().file.name;

// 查找日记中提到当前主题的记录
const mentions = dv.pages('"0_Bullet Journal/Daily Notes"')
    .file.lists
    .where(li => li.text.includes(currentTitle))
    .sort(li => li.link.path, 'desc');

if (mentions.length > 0) {
    dv.header(3, "日记中的相关记录");
    dv.list(mentions.map(m => `${m.text} (来自: ${m.link})`));
} else {
    dv.paragraph("暂无在日记中找到相关记录。");
}
```

## 参考资料

<% tp.file.cursor(3) %>
