<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合伙人告知书</title>
    <style>
        :root {
            --neon-blue: #00e5ff;
            --neon-purple: #b967ff;
            --neon-green: #00ffaa;
            --neon-yellow: #ffdd00;
            --neon-red: #ff5e7a;
            --dark-bg: #0a0e17;
            --card-bg: #141c2b;
        }

        body {
            background-color: #000000;
            color: #ffffff;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--neon-blue);
            text-shadow: 0 0 10px rgba(0, 229, 255, 0.7), 0 0 20px rgba(0, 229, 255, 0.5);
            margin-bottom: 0.5rem;
        }

        .subtitle {
            font-size: 0.9rem;
            color: #8a9ab0;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-top: 0.5rem;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.2rem;
            margin-bottom: 2rem;
        }

        .card {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-tag {
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
            opacity: 0.8;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.8rem;
        }

        .card-content {
            font-size: 0.9rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .card-price {
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: auto;
            padding-top: 1rem;
        }

        .card-icon {
            position: absolute;
            top: 1.2rem;
            right: 1.2rem;
            font-size: 1.2rem;
        }

        .card-divider {
            height: 2px;
            width: 100%;
            margin: 0.8rem 0;
            border-radius: 1px;
        }

        .card-quote {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-top: 0.5rem;
        }

        /* Card themes */
        .blue-theme {
            border-top: 3px solid var(--neon-blue);
        }
        .blue-theme .card-tag, .blue-theme .card-icon {
            color: var(--neon-blue);
        }
        .blue-theme .card-divider {
            background: linear-gradient(90deg, var(--neon-blue), transparent);
        }

        .purple-theme {
            border-top: 3px solid var(--neon-purple);
        }
        .purple-theme .card-tag, .purple-theme .card-icon {
            color: var(--neon-purple);
        }
        .purple-theme .card-divider {
            background: linear-gradient(90deg, var(--neon-purple), transparent);
        }

        .green-theme {
            border-top: 3px solid var(--neon-green);
        }
        .green-theme .card-tag, .green-theme .card-icon {
            color: var(--neon-green);
        }
        .green-theme .card-divider {
            background: linear-gradient(90deg, var(--neon-green), transparent);
        }

        .yellow-theme {
            border-top: 3px solid var(--neon-yellow);
        }
        .yellow-theme .card-tag, .yellow-theme .card-icon {
            color: var(--neon-yellow);
        }
        .yellow-theme .card-divider {
            background: linear-gradient(90deg, var(--neon-yellow), transparent);
        }

        .red-theme {
            border-top: 3px solid var(--neon-red);
        }
        .red-theme .card-tag, .red-theme .card-icon {
            color: var(--neon-red);
        }
        .red-theme .card-divider {
            background: linear-gradient(90deg, var(--neon-red), transparent);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--neon-blue);
            margin: 2.5rem 0 1rem;
            position: relative;
            display: inline-block;
            text-shadow: 0 0 5px rgba(0, 229, 255, 0.5);
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--neon-blue), transparent);
        }

        .intro-text {
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            color: #cccccc;
        }

        .wide-card {
            grid-column: 1 / -1;
        }

        .pain-point-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            border-left: 3px solid var(--neon-red);
        }

        .reference-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            border-left: 3px solid var(--neon-green);
        }

        .footer-text {
            font-size: 0.8rem;
            color: #888888;
            margin-top: 2rem;
            text-align: center;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .title {
                font-size: 2rem;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">合伙人告知书</h1>
            <div class="subtitle">OBSIDIAN SYSTEM TEMPLATE PROJECT</div>
        </div>

        <h2 class="section-title">项目介绍</h2>
        <p class="intro-text">项目：小红书开店卖Obsidian系统模板，卖我搭建好的系统</p>

        <div class="card-grid">
            <div class="card blue-theme">
                <div class="card-tag">基础系统</div>
                <div class="card-icon">✏️</div>
                <h3 class="card-title">睡眠日志系统</h3>
                <div class="card-content">引流用</div>
                <div class="card-divider"></div>
                <div class="card-price">¥9.9</div>
                <div class="card-quote">收尾阶段</div>
            </div>

            <div class="card purple-theme">
                <div class="card-tag">基础系统</div>
                <div class="card-icon">⚡</div>
                <h3 class="card-title">精力管理系统</h3>
                <div class="card-content">高效管理个人精力</div>
                <div class="card-divider"></div>
                <div class="card-price">¥9.9</div>
                <div class="card-quote">收尾阶段</div>
            </div>

            <div class="card green-theme">
                <div class="card-tag">基础系统</div>
                <div class="card-icon">⏱️</div>
                <h3 class="card-title">时间管理系统</h3>
                <div class="card-content">优化时间分配与利用</div>
                <div class="card-divider"></div>
                <div class="card-price">¥9.9</div>
                <div class="card-quote">收尾阶段</div>
            </div>

            <div class="card yellow-theme">
                <div class="card-tag">基础系统</div>
                <div class="card-icon">✅</div>
                <h3 class="card-title">任务管理系统</h3>
                <div class="card-content">高效任务跟踪与完成</div>
                <div class="card-divider"></div>
                <div class="card-price">¥9.9</div>
                <div class="card-quote">收尾阶段</div>
            </div>

            <div class="card red-theme">
                <div class="card-tag">进阶系统</div>
                <div class="card-icon">📊</div>
                <h3 class="card-title">项目管理系统</h3>
                <div class="card-content">全面项目规划与执行</div>
                <div class="card-divider"></div>
                <div class="card-price">¥36.9</div>
                <div class="card-quote">收尾阶段</div>
            </div>
        </div>

        <div class="card blue-theme wide-card" style="margin-bottom: 2rem;">
            <div class="card-tag">综合系统</div>
            <div class="card-icon">🧠</div>
            <h3 class="card-title">知识管理系统</h3>
            <div class="card-content">包含以上系统，还有其他零碎系统</div>
            <div class="card-divider"></div>
            <div class="card-price">¥79.9</div>
            <div class="card-quote">规划中</div>
        </div>

        <p class="footer-text">有写笔记的受众人群：这需要你去市场调查下</p>

        <h2 class="section-title">痛点</h2>
        <div class="pain-point-card">
            <p>我都愿意付费200以内买这样的模板或者请人设计这样的模板</p>
            <p>我找了市面上所有的模板，当然有些标价很贵的没试过，看图片介绍就大概知道了，没有适合我的</p>
            <p>这个项目是解决我本身的痛点，顺便卖给同样有需求的人</p>
        </div>

        <h2 class="section-title">参考</h2>
        <div class="reference-card">
            <p>以下是我参考的博主的店的链接</p>
        </div>
    </div>
</body>
</html>
