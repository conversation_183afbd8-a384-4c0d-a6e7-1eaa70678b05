---
tags:
  - type/dashboard
  - dashboard/task
  - theme/morandi
created: 2025-05-26T10:00
updated: 2025-05-26T10:00
---

# 📋 任务仪表盘 - 莫兰迪主题

> 优雅柔和的莫兰迪色系任务管理系统

## 📊 任务看板

```dataviewjs
// 莫兰迪色系配置
const morandiColors = {
    // 主色调 - 柔和低饱和度
    dustyRose: '#D4A5A5',      // 灰玫瑰
    sageGreen: '#A8B5A0',      // 鼠尾草绿
    dustyBlue: '#9BB0C1',      // 灰蓝色
    warmGray: '#B8A99C',       // 暖灰色
    softLavender: '#B5A7C7',   // 柔和薰衣草
    mutedTeal: '#8FA5A5',      // 柔和青色
    paleOchre: '#C7B299',      // 淡赭石色
    cloudWhite: '#F5F3F0',     // 云白色

    // 背景色调
    backgrounds: {
        dustyRose: '#F2E8E8',
        sageGreen: '#EDF0EB',
        dustyBlue: '#EBF0F5',
        warmGray: '#F0EDEA',
        softLavender: '#F0EDF5',
        mutedTeal: '#EBF0F0',
        paleOchre: '#F2EFEA',
        cloudWhite: '#FAFAF9'
    }
};

// 模拟任务统计数据
const stats = {
    overdue: 3,      // 逾期
    inProgress: 2,   // 进行中
    waiting: 8,      // 待办
    today: 5,        // 今天
    tomorrow: 3,     // 明天
    thisWeek: 7,     // 本周其他
    future: 12,      // 未来
    completed: 45    // 已完成
};

// 创建看板样式
const container = this.container;
container.innerHTML = '';

// 添加标题
const title = document.createElement('div');
title.style.cssText = `
    text-align: center;
    font-size: 1.3em;
    font-weight: 300;
    margin-bottom: 25px;
    color: ${morandiColors.warmGray};
    letter-spacing: 1px;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
`;
title.textContent = '任务清单 · 莫兰迪';
container.appendChild(title);

// 创建卡片容器
const cardContainer = document.createElement('div');
cardContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
    padding: 25px;
    background: linear-gradient(135deg, ${morandiColors.backgrounds.cloudWhite} 0%, ${morandiColors.backgrounds.dustyRose} 50%, ${morandiColors.backgrounds.sageGreen} 100%);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(184, 169, 156, 0.15);
    backdrop-filter: blur(10px);
`;

// 定义莫兰迪风格卡片数据
const cards = [
    {
        label: '逾期',
        count: stats.overdue,
        color: morandiColors.dustyRose,
        bgColor: morandiColors.backgrounds.dustyRose,
        icon: '⚠️'
    },
    {
        label: '进行中',
        count: stats.inProgress,
        color: morandiColors.mutedTeal,
        bgColor: morandiColors.backgrounds.mutedTeal,
        icon: '🔄'
    },
    {
        label: '待办',
        count: stats.waiting,
        color: morandiColors.warmGray,
        bgColor: morandiColors.backgrounds.warmGray,
        icon: '📝'
    },
    {
        label: '今天',
        count: stats.today,
        color: morandiColors.dustyBlue,
        bgColor: morandiColors.backgrounds.dustyBlue,
        icon: '☀️'
    },
    {
        label: '明天',
        count: stats.tomorrow,
        color: morandiColors.softLavender,
        bgColor: morandiColors.backgrounds.softLavender,
        icon: '🌙'
    },
    {
        label: '本周其他',
        count: stats.thisWeek,
        color: morandiColors.sageGreen,
        bgColor: morandiColors.backgrounds.sageGreen,
        icon: '📅'
    },
    {
        label: '未来',
        count: stats.future,
        color: morandiColors.paleOchre,
        bgColor: morandiColors.backgrounds.paleOchre,
        icon: '🔮'
    },
    {
        label: '已完成',
        count: stats.completed,
        color: morandiColors.sageGreen,
        bgColor: morandiColors.backgrounds.sageGreen,
        icon: '✅'
    }
];

// 创建莫兰迪风格卡片
cards.forEach((card, index) => {
    const cardElement = document.createElement('div');
    cardElement.style.cssText = `
        background: ${card.bgColor};
        border-radius: 16px;
        padding: 20px 15px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(184, 169, 156, 0.12);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(5px);
        position: relative;
        overflow: hidden;
    `;

    cardElement.innerHTML = `
        <div style="font-size: 1.5em; margin-bottom: 8px; opacity: 0.8;">
            ${card.icon}
        </div>
        <div style="font-size: 2.2em; font-weight: 300; color: ${card.color}; margin-bottom: 8px; font-family: 'SF Pro Display', sans-serif;">
            ${card.count}
        </div>
        <div style="font-size: 0.9em; color: ${card.color}; font-weight: 400; opacity: 0.9; letter-spacing: 0.5px;">
            ${card.label}
        </div>
    `;

    // 添加莫兰迪风格悬停效果
    cardElement.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.02)';
        this.style.boxShadow = `0 8px 30px rgba(184, 169, 156, 0.2)`;
        this.style.borderColor = card.color;
        this.style.background = `linear-gradient(135deg, ${card.bgColor} 0%, rgba(255, 255, 255, 0.4) 100%)`;
    });

    cardElement.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 4px 20px rgba(184, 169, 156, 0.12)';
        this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        this.style.background = card.bgColor;
    });

    cardContainer.appendChild(cardElement);
});

container.appendChild(cardContainer);

// 添加莫兰迪风格总计信息
const totalTasks = stats.overdue + stats.inProgress + stats.waiting + stats.today +
                  stats.tomorrow + stats.thisWeek + stats.future;
const totalInfo = document.createElement('div');
totalInfo.style.cssText = `
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, ${morandiColors.backgrounds.cloudWhite} 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: 16px;
    margin-top: 15px;
    border: 1px solid rgba(184, 169, 156, 0.2);
    box-shadow: 0 4px 20px rgba(184, 169, 156, 0.1);
`;
totalInfo.innerHTML = `
    <div style="font-size: 1.1em; color: ${morandiColors.warmGray}; font-weight: 300; font-family: 'SF Pro Display', sans-serif;">
        <span style="font-weight: 400;">📊 总计: ${totalTasks + stats.completed} 个任务</span>
        <div style="margin-top: 8px; font-size: 0.9em; opacity: 0.8;">
            未完成: ${totalTasks} | 已完成: ${stats.completed}
        </div>
    </div>
`;
container.appendChild(totalInfo);

// 添加主题说明
const themeInfo = document.createElement('div');
themeInfo.style.cssText = `
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    background: rgba(245, 243, 240, 0.6);
    border-radius: 12px;
    border: 1px solid rgba(184, 169, 156, 0.15);
`;
themeInfo.innerHTML = `
    <div style="font-size: 0.85em; color: ${morandiColors.warmGray}; opacity: 0.8; font-style: italic;">
        🎨 莫兰迪色系主题 - 优雅柔和的视觉体验
    </div>
`;
container.appendChild(themeInfo);
```

## 🎨 莫兰迪色彩说明

**主要色调：**
- 🌸 **灰玫瑰** (#D4A5A5) - 温柔优雅
- 🌿 **鼠尾草绿** (#A8B5A0) - 自然宁静
- 💙 **灰蓝色** (#9BB0C1) - 沉稳理性
- 🤎 **暖灰色** (#B8A99C) - 温暖包容
- 💜 **柔和薰衣草** (#B5A7C7) - 浪漫梦幻
- 💚 **柔和青色** (#8FA5A5) - 清新淡雅
- 🟤 **淡赭石色** (#C7B299) - 大地温暖
- ☁️ **云白色** (#F5F3F0) - 纯净柔和

## 📝 主题特色

- ✨ **低饱和度配色** - 减少视觉疲劳
- 🎭 **柔和渐变** - 营造温馨氛围
- 🌊 **流畅动效** - 提升交互体验
- 🏛️ **简约布局** - 突出内容本身
- 🎨 **艺术美感** - 莫兰迪大师级配色

> 💡 这是莫兰迪主题的预览版本，展示了色彩搭配和视觉效果。完整功能版本将在后续开发中实现。

## 🔥 今日任务预览

```dataviewjs
// 莫兰迪色系配置
const morandiColors = {
    dustyBlue: '#9BB0C1',
    backgrounds: { dustyBlue: '#EBF0F5' }
};

const container = this.container;
container.innerHTML = '';

// 创建今日任务预览卡片
const todayCard = document.createElement('div');
todayCard.style.cssText = `
    background: linear-gradient(135deg, ${morandiColors.backgrounds.dustyBlue} 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 6px 25px rgba(155, 176, 193, 0.15);
    border: 1px solid rgba(155, 176, 193, 0.2);
    backdrop-filter: blur(10px);
`;

todayCard.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 20px;">
        <div style="font-size: 1.5em; margin-right: 12px;">☀️</div>
        <h3 style="margin: 0; color: ${morandiColors.dustyBlue}; font-weight: 300; font-size: 1.2em;">今日任务</h3>
    </div>
    <div style="color: ${morandiColors.dustyBlue}; opacity: 0.8; font-style: italic; text-align: center; padding: 20px;">
        🎨 莫兰迪主题预览 - 柔和优雅的今日任务展示
    </div>
`;

container.appendChild(todayCard);
```

## 📅 本周任务预览

```dataviewjs
// 莫兰迪色系配置
const morandiColors = {
    sageGreen: '#A8B5A0',
    backgrounds: { sageGreen: '#EDF0EB' }
};

const container = this.container;
container.innerHTML = '';

// 创建本周任务预览卡片
const weekCard = document.createElement('div');
weekCard.style.cssText = `
    background: linear-gradient(135deg, ${morandiColors.backgrounds.sageGreen} 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 6px 25px rgba(168, 181, 160, 0.15);
    border: 1px solid rgba(168, 181, 160, 0.2);
    backdrop-filter: blur(10px);
`;

weekCard.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 20px;">
        <div style="font-size: 1.5em; margin-right: 12px;">📅</div>
        <h3 style="margin: 0; color: ${morandiColors.sageGreen}; font-weight: 300; font-size: 1.2em;">本周任务</h3>
    </div>
    <div style="color: ${morandiColors.sageGreen}; opacity: 0.8; font-style: italic; text-align: center; padding: 20px;">
        🌿 自然宁静的本周任务管理界面
    </div>
`;

container.appendChild(weekCard);
```

## 🍅 番茄钟看板预览

```dataviewjs
// 莫兰迪色系配置
const morandiColors = {
    dustyRose: '#D4A5A5',
    softLavender: '#B5A7C7',
    mutedTeal: '#8FA5A5',
    paleOchre: '#C7B299',
    backgrounds: {
        dustyRose: '#F2E8E8',
        softLavender: '#F0EDF5',
        mutedTeal: '#EBF0F0',
        paleOchre: '#F2EFEA'
    }
};

const container = this.container;
container.innerHTML = '';

// 创建番茄钟卡片容器
const tomatoContainer = document.createElement('div');
tomatoContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 20px;
    padding: 25px;
    background: linear-gradient(135deg, rgba(245, 243, 240, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(184, 169, 156, 0.12);
`;

// 番茄钟数据
const tomatoData = [
    {
        label: '今日目标',
        count: 8,
        icon: '🎯',
        color: morandiColors.dustyRose,
        bgColor: morandiColors.backgrounds.dustyRose,
        subtitle: '已设定'
    },
    {
        label: '今日完成',
        count: 5,
        icon: '🔥',
        color: morandiColors.softLavender,
        bgColor: morandiColors.backgrounds.softLavender,
        subtitle: '进行中'
    },
    {
        label: '昨日完成',
        count: 6,
        icon: '📊',
        color: morandiColors.mutedTeal,
        bgColor: morandiColors.backgrounds.mutedTeal,
        subtitle: '目标7'
    },
    {
        label: '本周累计',
        count: 28,
        icon: '📈',
        color: morandiColors.paleOchre,
        bgColor: morandiColors.backgrounds.paleOchre,
        subtitle: '5天记录'
    }
];

// 创建番茄钟卡片
tomatoData.forEach(item => {
    const card = document.createElement('div');
    card.style.cssText = `
        background: ${item.bgColor};
        border-radius: 16px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 6px 25px rgba(184, 169, 156, 0.12);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        border: 1px solid rgba(255, 255, 255, 0.4);
        backdrop-filter: blur(8px);
    `;

    card.innerHTML = `
        <div style="font-size: 2.5em; margin-bottom: 15px; opacity: 0.9;">
            ${item.icon}
        </div>
        <div style="font-size: 2.2em; font-weight: 300; color: ${item.color}; margin-bottom: 8px; font-family: 'SF Pro Display', sans-serif;">
            ${item.count}
        </div>
        <div style="font-size: 1em; color: ${item.color}; font-weight: 400; margin-bottom: 5px; letter-spacing: 0.5px;">
            ${item.label}
        </div>
        <div style="font-size: 0.8em; color: ${item.color}; opacity: 0.7;">
            ${item.subtitle}
        </div>
    `;

    // 添加悬停效果
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px) scale(1.02)';
        this.style.boxShadow = `0 12px 35px rgba(184, 169, 156, 0.18)`;
        this.style.borderColor = item.color;
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 6px 25px rgba(184, 169, 156, 0.12)';
        this.style.borderColor = 'rgba(255, 255, 255, 0.4)';
    });

    tomatoContainer.appendChild(card);
});

container.appendChild(tomatoContainer);
```

## 📁 项目管理预览

```dataviewjs
// 莫兰迪色系配置
const morandiColors = {
    warmGray: '#B8A99C',
    dustyRose: '#D4A5A5',
    sageGreen: '#A8B5A0',
    dustyBlue: '#9BB0C1',
    backgrounds: {
        warmGray: '#F0EDEA',
        dustyRose: '#F2E8E8',
        sageGreen: '#EDF0EB',
        dustyBlue: '#EBF0F5'
    }
};

const container = this.container;
container.innerHTML = '';

// 创建项目管理容器
const projectContainer = document.createElement('div');
projectContainer.style.cssText = `
    background: linear-gradient(135deg, rgba(245, 243, 240, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(184, 169, 156, 0.15);
    border: 1px solid rgba(184, 169, 156, 0.2);
`;

// 项目数据示例
const projects = [
    {
        name: '知识管理系统',
        planned: 12,
        pending: 3,
        completed: 8,
        color: morandiColors.dustyBlue,
        bgColor: morandiColors.backgrounds.dustyBlue
    },
    {
        name: '任务仪表盘优化',
        planned: 8,
        pending: 2,
        completed: 15,
        color: morandiColors.sageGreen,
        bgColor: morandiColors.backgrounds.sageGreen
    },
    {
        name: '莫兰迪主题设计',
        planned: 5,
        pending: 1,
        completed: 3,
        color: morandiColors.dustyRose,
        bgColor: morandiColors.backgrounds.dustyRose
    }
];

// 添加标题
const title = document.createElement('div');
title.style.cssText = `
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(184, 169, 156, 0.2);
`;
title.innerHTML = `
    <div style="font-size: 1.5em; margin-right: 12px;">📁</div>
    <h3 style="margin: 0; color: ${morandiColors.warmGray}; font-weight: 300; font-size: 1.3em;">项目管理</h3>
`;
projectContainer.appendChild(title);

// 创建项目卡片
projects.forEach(project => {
    const projectCard = document.createElement('div');
    projectCard.style.cssText = `
        background: ${project.bgColor};
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 4px 20px rgba(184, 169, 156, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.4);
        transition: all 0.3s ease;
        cursor: pointer;
    `;

    const total = project.planned + project.pending + project.completed;
    const completionRate = total > 0 ? Math.round((project.completed / total) * 100) : 0;

    projectCard.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="margin: 0; color: ${project.color}; font-weight: 400; font-size: 1.1em;">${project.name}</h4>
            <div style="background: ${project.color}; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: 500;">
                ${completionRate}%
            </div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
            <div style="text-align: center;">
                <div style="font-size: 1.5em; font-weight: 300; color: ${project.color};">${project.planned}</div>
                <div style="font-size: 0.8em; color: ${project.color}; opacity: 0.8;">已规划</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 1.5em; font-weight: 300; color: ${project.color};">${project.pending}</div>
                <div style="font-size: 0.8em; color: ${project.color}; opacity: 0.8;">待整理</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 1.5em; font-weight: 300; color: ${project.color};">${project.completed}</div>
                <div style="font-size: 0.8em; color: ${project.color}; opacity: 0.8;">已完成</div>
            </div>
        </div>
        <div style="background: rgba(255, 255, 255, 0.5); border-radius: 10px; height: 6px; overflow: hidden;">
            <div style="background: ${project.color}; height: 100%; width: ${completionRate}%; transition: width 0.3s ease; border-radius: 10px;"></div>
        </div>
    `;

    // 添加悬停效果
    projectCard.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = `0 8px 30px rgba(184, 169, 156, 0.15)`;
        this.style.borderColor = project.color;
    });

    projectCard.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 4px 20px rgba(184, 169, 156, 0.1)';
        this.style.borderColor = 'rgba(255, 255, 255, 0.4)';
    });

    projectContainer.appendChild(projectCard);
});

container.appendChild(projectContainer);
```

## 🌟 主题使用指南

### 📱 小红书推广要点

**🎨 莫兰迪色系任务仪表盘**
- ✨ **高级感配色** - 低饱和度莫兰迪色系
- 🎭 **优雅界面** - 柔和渐变与毛玻璃效果
- 📊 **完整功能** - 任务管理、番茄钟、项目跟踪
- 🌸 **治愈系** - 减少视觉疲劳，提升工作愉悦感

### 💰 定价建议
- **莫兰迪主题壳** - 19.9元
- **完整功能版** - 49.9元
- **定制配色版** - 79.9元

### 🎯 目标用户
- 📚 **学生党** - 优雅的学习管理工具
- 💼 **职场人** - 高效的工作任务跟踪
- 🎨 **设计师** - 审美在线的生产力工具
- 🌸 **颜值控** - 好看又好用的任务管理

### 📝 卖点文案
> 🎨 莫兰迪大师级配色，让任务管理也能如此优雅
> ✨ 低饱和度色彩，护眼又治愈
> 📊 功能完整，颜值在线
> 🌸 工作学习，从此爱上效率

---

**💡 这是莫兰迪主题的完整预览版本，展示了所有核心界面的设计风格。可直接用于小红书推广展示！**
