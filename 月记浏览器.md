---
created: 2025-05-25T17:00
updated: 2025-05-25T17:00
tags:
  - type/dashboard
  - monthly/browser
---

# 📅 月记浏览器

> 🗓️ 月度回顾与规划，把握成长节奏

## 🚀 快速操作

```dataviewjs
// 创建快速操作面板
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #FFFBF0 0%, #FFF8F0 100%);
    border-radius: 16px;
    border: 2px solid rgba(232, 213, 165, 0.3);
`;

// 计算当前月份
const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const currentMonth = `${year}-${month}`;

const quickActions = [
    {
        title: '本月月记',
        icon: '📝',
        desc: '创建本月总结',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText(`0_Bullet Journal/Monthly Notes/${currentMonth}`, '', true);
            }
        },
        color: '#E8D5A5'
    },
    {
        title: '月记模板',
        icon: '📋',
        desc: '查看月记模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates/Monthly Template', '', false);
            }
        },
        color: '#B8D6B8'
    },
    {
        title: '回顾仪表板',
        icon: '📊',
        desc: '查看回顾统计',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('回顾仪表板', '', false);
            }
        },
        color: '#A5D6E8'
    },
    {
        title: '周记浏览器',
        icon: '🗓️',
        desc: '查看周记记录',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('周记浏览器', '', false);
            }
        },
        color: '#E8B4A0'
    }
];

let actionsHTML = '';
quickActions.forEach((action, index) => {
    actionsHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 12px; padding: 18px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action-btn" data-index="${index}">
            <div style="font-size: 2em; margin-bottom: 8px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickActionsContainer.innerHTML = actionsHTML;

// 添加点击事件
setTimeout(() => {
    const actionBtns = quickActionsContainer.querySelectorAll('.quick-action-btn');
    actionBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            quickActions[index].action();
        });

        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 📅 所有月记

```dataview
TABLE WITHOUT ID
    file.link AS "🗓️ 月份",
    choice(monthly_goal, monthly_goal, "未设置目标") AS "🎯 月度目标",
    choice(key_achievements, key_achievements, "未记录成就") AS "🏆 关键成就",
    choice(monthly_reflection, monthly_reflection, "未写反思") AS "💭 月度反思",
    file.mtime AS "📝 修改时间"
FROM "0_Bullet Journal/Monthly Notes"
WHERE file.name != "README"
SORT file.name DESC
LIMIT 12
```

## 📊 月记统计

```dataviewjs
// 获取所有月记
const allMonthlies = dv.pages('"0_Bullet Journal/Monthly Notes"').where(p => p.file.name !== "README");

// 统计数据
const totalMonthlies = allMonthlies.length;
const currentYear = new Date().getFullYear();
const currentYearMonthlies = allMonthlies.where(p => p.file.name.startsWith(currentYear.toString())).length;

// 计算本年度已过去的月数
const currentMonth = new Date().getMonth() + 1; // 1-12
const completionRate = currentMonth > 0 ? Math.round((currentYearMonthlies / currentMonth) * 100) : 0;

// 创建统计容器
const statsContainer = document.createElement('div');
statsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #FFFBF0 0%, #FFF8F0 100%);
    border-radius: 16px;
    border: 2px solid rgba(232, 213, 165, 0.2);
`;

const stats = [
    { label: '总月记', value: totalMonthlies, icon: '📅', color: '#E8D5A5' },
    { label: '本年月记', value: currentYearMonthlies, icon: '📝', color: '#B8D6B8' },
    { label: '已过月数', value: currentMonth, icon: '⏰', color: '#A5D6E8' },
    { label: '记录率', value: completionRate + '%', icon: '📊', color: '#E8B4A0' }
];

let statsHTML = '';
stats.forEach(stat => {
    statsHTML += `
        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); 
                    border-radius: 12px; border: 1px solid ${stat.color}20;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${stat.icon}</div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${stat.color}; margin-bottom: 4px;">
                ${stat.value}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500;">
                ${stat.label}
            </div>
        </div>
    `;
});

statsContainer.innerHTML = statsHTML;
this.container.appendChild(statsContainer);
```

## 🗓️ 按年份浏览

```dataview
TABLE WITHOUT ID
    substring(file.name, 0, 4) AS "📅 年份",
    length(rows.file.link) AS "📊 月记数量",
    join(rows.file.link, ", ") AS "🗓️ 月记列表"
FROM "0_Bullet Journal/Monthly Notes"
WHERE file.name != "README" AND file.name CONTAINS "-"
GROUP BY substring(file.name, 0, 4)
SORT substring(file.name, 0, 4) DESC
```

## 🎯 目标达成情况

```dataview
TABLE WITHOUT ID
    file.link AS "🗓️ 月份",
    choice(monthly_goal, monthly_goal, "未设置") AS "🎯 月度目标",
    choice(goal_progress, 
           choice(goal_progress >= 80, "🟢 优秀", 
                  choice(goal_progress >= 60, "🟡 良好", "🔴 需改进")),
           "❓ 未评估") AS "📊 目标进度",
    choice(next_month_focus, next_month_focus, "未制定重点") AS "📋 下月重点"
FROM "0_Bullet Journal/Monthly Notes"
WHERE monthly_goal AND file.name != "README"
SORT file.name DESC
LIMIT 6
```

## 🏆 成就与挑战

```dataview
TABLE WITHOUT ID
    file.link AS "🗓️ 月份",
    choice(key_achievements, key_achievements, "无记录") AS "🏆 关键成就",
    choice(major_challenges, major_challenges, "无记录") AS "⚡ 主要挑战",
    choice(lessons_learned, lessons_learned, "无记录") AS "📚 经验教训"
FROM "0_Bullet Journal/Monthly Notes"
WHERE key_achievements AND file.name != "README"
SORT file.name DESC
LIMIT 6
```

## 📈 成长轨迹

```dataview
TABLE WITHOUT ID
    file.link AS "🗓️ 月份",
    choice(personal_growth, personal_growth, "无记录") AS "🌱 个人成长",
    choice(skill_development, skill_development, "无记录") AS "🎯 技能发展",
    choice(habit_formation, habit_formation, "无记录") AS "🔄 习惯养成"
FROM "0_Bullet Journal/Monthly Notes"
WHERE personal_growth AND file.name != "README"
SORT file.name DESC
LIMIT 6
```

---

<div style="text-align: center; margin-top: 30px; padding: 15px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 12px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600; margin-bottom: 6px;">
        🗓️ 月度回顾小贴士
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        月度回顾是成长的里程碑，总结得失，规划未来，让每个月都有收获
    </div>
</div>
