---
created: 2025-05-25T16:30
updated: 2025-05-25T16:30
tags:
  - type/dashboard
  - literature/browser
---

# 📚 文献笔记浏览器

> 📖 管理和浏览所有学习资料，构建知识体系

## 🚀 快速操作

```dataviewjs
// 创建快速操作面板
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F8FFF8 0%, #F0FFF0 100%);
    border-radius: 16px;
    border: 2px solid rgba(184, 214, 184, 0.3);
`;

const quickActions = [
    {
        title: '新建文献笔记',
        icon: '➕',
        desc: '添加新的学习资料',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates/Literature Note Template', '', false);
            }
        },
        color: '#B8D6B8'
    },
    {
        title: '知识库概览',
        icon: '📊',
        desc: '查看知识库统计',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('知识库概览仪表板', '', false);
            }
        },
        color: '#A5D6E8'
    },
    {
        title: '搜索文献',
        icon: '🔍',
        desc: '全局搜索文献',
        action: () => {
            if (app && app.commands) {
                app.commands.executeCommandById('global-search:open');
            }
        },
        color: '#E8D5A5'
    },
    {
        title: '文献模板',
        icon: '📋',
        desc: '查看文献模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates', '', false);
            }
        },
        color: '#E8B4A0'
    }
];

let actionsHTML = '';
quickActions.forEach((action, index) => {
    actionsHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 12px; padding: 18px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action-btn" data-index="${index}">
            <div style="font-size: 2em; margin-bottom: 8px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickActionsContainer.innerHTML = actionsHTML;

// 添加点击事件
setTimeout(() => {
    const actionBtns = quickActionsContainer.querySelectorAll('.quick-action-btn');
    actionBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            quickActions[index].action();
        });

        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 📖 所有文献笔记

```dataview
TABLE WITHOUT ID
    file.link AS "📄 文献标题",
    choice(author, author, "未知作者") AS "👤 作者",
    choice(type, type, "📝 未分类") AS "📚 类型",
    choice(subject, subject, "📖 未分类") AS "🏷️ 学科",
    choice(importance = "核心", "🔴 核心", choice(importance = "重要", "🟡 重要", "⚪ 一般")) AS "⭐ 重要性",
    file.mtime AS "📅 修改时间"
FROM "2_Literature notes"
WHERE file.name != "README"
SORT file.mtime DESC
```

## 🏷️ 按学科分类

```dataview
TABLE WITHOUT ID
    subject AS "🎓 学科",
    length(rows.file.link) AS "📊 文献数量",
    join(rows.file.link, ", ") AS "📚 文献列表"
FROM "2_Literature notes"
WHERE subject AND file.name != "README"
GROUP BY subject
SORT length(rows.file.link) DESC
```

## ⭐ 重要文献

```dataview
TABLE WITHOUT ID
    file.link AS "📄 文献标题",
    choice(author, author, "未知作者") AS "👤 作者",
    choice(type, type, "📝 未分类") AS "📚 类型",
    choice(subject, subject, "📖 未分类") AS "🏷️ 学科"
FROM "2_Literature notes"
WHERE importance = "核心" OR importance = "重要"
SORT importance DESC, file.mtime DESC
LIMIT 10
```

## 📊 文献统计

```dataviewjs
// 获取所有文献笔记
const allLiterature = dv.pages('"2_Literature notes"').where(p => p.file.name !== "README");

// 统计数据
const totalLiterature = allLiterature.length;
const coreImportance = allLiterature.where(p => p.importance === "核心").length;
const importantLiterature = allLiterature.where(p => p.importance === "重要").length;
const generalLiterature = allLiterature.where(p => !p.importance || p.importance === "一般").length;

// 按类型统计
const typeGroups = {};
allLiterature.forEach(p => {
    const type = p.type || "未分类";
    typeGroups[type] = (typeGroups[type] || 0) + 1;
});

// 创建统计容器
const statsContainer = document.createElement('div');
statsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F8FFF8 0%, #F0FFF0 100%);
    border-radius: 16px;
    border: 2px solid rgba(184, 214, 184, 0.2);
`;

const stats = [
    { label: '总文献', value: totalLiterature, icon: '📚', color: '#8B7355' },
    { label: '核心', value: coreImportance, icon: '🔴', color: '#F44336' },
    { label: '重要', value: importantLiterature, icon: '🟡', color: '#FF9800' },
    { label: '一般', value: generalLiterature, icon: '⚪', color: '#9E9E9E' }
];

let statsHTML = '';
stats.forEach(stat => {
    statsHTML += `
        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); 
                    border-radius: 12px; border: 1px solid ${stat.color}20;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${stat.icon}</div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${stat.color}; margin-bottom: 4px;">
                ${stat.value}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500;">
                ${stat.label}
            </div>
        </div>
    `;
});

statsContainer.innerHTML = statsHTML;
this.container.appendChild(statsContainer);
```

## 📈 最近添加

```dataview
TABLE WITHOUT ID
    file.link AS "📄 文献标题",
    choice(author, author, "未知作者") AS "👤 作者",
    choice(subject, subject, "📖 未分类") AS "🏷️ 学科",
    file.ctime AS "📅 创建时间"
FROM "2_Literature notes"
WHERE file.name != "README"
SORT file.ctime DESC
LIMIT 8
```

---

<div style="text-align: center; margin-top: 30px; padding: 15px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 12px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600; margin-bottom: 6px;">
        📚 学习小贴士
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        定期回顾文献笔记，将重要观点转化为永久笔记，构建个人知识体系
    </div>
</div>
