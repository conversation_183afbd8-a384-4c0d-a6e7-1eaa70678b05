<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码接码查询</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 500px;
            padding: 30px;
        }
        h1 {
            color: #4169e1;
            text-align: center;
            margin-bottom: 20px;
        }
        .notice {
            color: #ff4d4f;
            text-align: center;
            margin-bottom: 25px;
            font-size: 14px;
            line-height: 1.6;
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
        }
        input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            width: 100%;
            background-color: #4169e1;
            color: white;
            border: none;
            padding: 12px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #3a5fcd;
        }
        button:disabled {
            background-color: #b0c4de;
            cursor: not-allowed;
        }
        .result {
            margin-top: 25px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .result h2 {
            color: #333;
            margin-top: 0;
            font-size: 18px;
        }
        .no-result {
            color: #666;
            text-align: center;
        }
        .code-result {
            margin-top: 15px;
            padding: 15px;
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
        }
        .code-result p {
            margin: 5px 0;
        }
        .code-value {
            font-size: 24px;
            color: #1890ff;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }
        .error-message {
            color: #ff4d4f;
            text-align: center;
        }
        .loading {
            text-align: center;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>验证码接码查询</h1>
        <p class="notice">大号已经解封，可以阅读搜索"自信的魅族"用户购买账号。<br>期间"带刀的猫咪"可能回复复不及时，感谢支持。</p>
        
        <div class="input-group">
            <label for="email">请输入邮箱地址</label>
            <input type="email" id="email" placeholder="例如: <EMAIL>">
        </div>
        
        <button id="queryBtn">查询</button>
        
        <div class="result">
            <h2>查询结果</h2>
            <div id="resultContent" class="no-result">
                未找到包含验证码的邮件，请确认邮箱是否填写正确或者重试！
            </div>
        </div>
    </div>

    <script>
        document.getElementById('queryBtn').addEventListener('click', async function() {
            const email = document.getElementById('email').value.trim();
            const resultContent = document.getElementById('resultContent');
            const queryBtn = document.getElementById('queryBtn');
            
            if (!email) {
                resultContent.innerHTML = '<div class="error-message">请输入邮箱地址</div>';
                return;
            }
            
            // 显示加载状态
            resultContent.innerHTML = '<div class="loading">查询中，请稍候...</div>';
            queryBtn.disabled = true;
            
            try {
                // 调用后端API
                const response = await fetch('http://localhost:5000/api/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email })
                });
                
                const result = await response.json();
                
                if (result.success && result.data) {
                    // 显示验证码信息
                    const codeData = result.data;
                    resultContent.innerHTML = `
                        <div class="code-result">
                            <p><strong>服务：</strong>${codeData.service || '未知服务'}</p>
                            <p><strong>验证码：</strong></p>
                            <div class="code-value">${codeData.code}</div>
                            <p><strong>接收时间：</strong>${codeData.created_at}</p>
                            <p><strong>状态：</strong>${codeData.used ? '已使用' : '未使用'}</p>
                        </div>
                    `;
                } else {
                    // 显示未找到结果
                    resultContent.innerHTML = `<div class="no-result">${result.message || '未找到包含验证码的邮件，请确认邮箱是否填写正确或者重试！'}</div>`;
                }
            } catch (error) {
                console.error('查询出错:', error);
                resultContent.innerHTML = '<div class="error-message">查询失败，请检查网络连接或稍后重试</div>';
            } finally {
                queryBtn.disabled = false;
            }
        });
    </script>
</body>
</html> 