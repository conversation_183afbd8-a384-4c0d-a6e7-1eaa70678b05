import os
import requests
import re
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
import time
import json
import random
from queue import Queue
from threading import Thread, Lock

# 定义文件路径
desktop_path = os.path.dirname(os.path.abspath(__file__))
input_file = os.path.join(desktop_path, "Cursor 再来1.5k个.txt")
output_file = os.path.join(desktop_path, "有效.txt")

# 提取链接和推荐码的正则表达式
link_pattern = re.compile(r'https?://[^\s]+')
code_pattern = re.compile(r'code=([A-Za-z0-9]+)')

# API端点
api_url = "https://www.cursor.com/api/dashboard/check-referral-code"

# 用于线程安全的打印和计数
print_lock = Lock()
counter_lock = Lock()
processed_count = 0
valid_count = 0
total_count = 0

# 随机用户代理
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36"
]

# 检查推荐码是否有效
def check_referral_code(link):
    try:
        # 提取推荐码
        match = code_pattern.search(link)
        if not match:
            with print_lock:
                print(f"无法从链接提取推荐码: {link}")
            return None
        
        referral_code = match.group(1)
        
        # 发送POST请求到API端点
        headers = {
            "Content-Type": "application/json",
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "application/json",
            "Origin": "https://www.cursor.com",
            "Referer": "https://www.cursor.com/"
        }
        payload = {"referralCode": referral_code}
        
        # 添加随机延迟，避免请求过快被封
        time.sleep(random.uniform(0.1, 0.5))
        
        response = requests.post(api_url, headers=headers, json=payload, timeout=15)
        
        # 检查响应
        if response.status_code == 200:
            try:
                result = response.json()
                # 检查isValid和userIsEligible字段是否都为true
                if result.get("isValid") == True and result.get("userIsEligible") == True:
                    # 保存推荐码和完整信息
                    return {
                        "link": link,
                        "code": referral_code,
                        "metadata": result.get("metadata", {})
                    }
            except json.JSONDecodeError:
                with print_lock:
                    print(f"无法解析JSON响应: {link}")
    except Exception as e:
        with print_lock:
            print(f"检查推荐码出错: {link}, 错误: {e}")
    
    return None

# 工作线程函数
def worker(queue, results):
    global processed_count, valid_count
    while True:
        try:
            link = queue.get()
            if link is None:  # 结束信号
                queue.task_done()
                break
                
            result = check_referral_code(link)
            
            with counter_lock:
                processed_count += 1
                if result:
                    valid_count += 1
                    results.append(result)
                
                # 每处理10个或队列为空时更新进度
                if processed_count % 10 == 0 or queue.qsize() == 0:
                    with print_lock:
                        print(f"进度: {processed_count}/{total_count}, 有效链接: {valid_count}, 剩余: {queue.qsize()}")
            
            queue.task_done()
        except Exception as e:
            with print_lock:
                print(f"工作线程出错: {e}")
            queue.task_done()

# 保存结果函数
def save_results(results):
    with open(output_file, 'w', encoding='utf-8') as f:
        for result in results:
            # 只保存链接，每行一个
            f.write(f"{result['link']}\n")
    print(f"完成! 已将 {len(results)} 个有效链接保存到 '{output_file}'")

# 主函数
def main():
    global total_count
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 '{input_file}'")
        return
    
    # 读取输入文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        # 如果utf-8编码打开失败，尝试其他编码
        try:
            with open(input_file, 'r', encoding='gbk') as f:
                content = f.read()
        except Exception as e2:
            print(f"读取文件出错: {e2}")
            return
    
    # 提取所有链接
    links = link_pattern.findall(content)
    
    if not links:
        print("没有找到任何链接")
        return
    
    # 过滤只包含cursor.com和referral的链接
    referral_links = [link for link in links if "cursor.com" in link and "referral" in link]
    
    if not referral_links:
        print("没有找到任何Cursor推荐链接")
        return
    
    total_count = len(referral_links)
    print(f"找到 {total_count} 个Cursor推荐链接，开始检查...")
    
    # 使用队列和多线程处理
    link_queue = Queue()
    results = []
    
    # 填充队列
    for link in referral_links:
        link_queue.put(link)
    
    # 创建工作线程 (使用更多线程)
    thread_count = min(50, total_count)  # 最多50个线程，避免过载
    threads = []
    for _ in range(thread_count):
        t = Thread(target=worker, args=(link_queue, results))
        t.daemon = True
        t.start()
        threads.append(t)
    
    print(f"已启动 {thread_count} 个工作线程")
    
    # 等待所有任务完成
    link_queue.join()
    
    # 发送结束信号给所有线程
    for _ in range(thread_count):
        link_queue.put(None)
    
    # 等待所有线程结束
    for t in threads:
        t.join()
    
    # 保存结果
    if results:
        save_results(results)
    else:
        print("没有找到有效链接")

if __name__ == "__main__":
    start_time = time.time()
    main()
    print(f"程序执行时间: {time.time() - start_time:.2f} 秒")
