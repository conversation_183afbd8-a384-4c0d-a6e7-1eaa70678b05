# 🎨 项目仪表盘主题切换器

> 一键切换不同风格主题的实用工具

## 🚀 使用方法

1. **复制下面的代码**到您的项目仪表盘文件开头
2. **点击主题按钮**即可切换风格
3. **自定义主题**可以修改配置对象

```dataviewjs
// 🎨 主题切换器
const container = this.container;
container.innerHTML = '';

// 主题配置
const THEMES = {
    'milk-tea': {
        name: '🧋 治愈系奶茶风',
        config: {
            primaryBg: '#FFF9F5',
            secondaryBg: '#F7F3F0',
            textPrimary: '#8B7355',
            textSecondary: '#A0896B',
            accentWarm: '#E8D5A5',
            accentCool: '#A8C8A8',
            goalGradient: 'linear-gradient(135deg, #E8D5A5 0%, #D4C574 100%)',
            areaGradient: 'linear-gradient(135deg, #A8C8A8 0%, #8FB88F 100%)',
            shadowColor: 'rgba(139, 115, 85, 0.08)'
        }
    },
    'morandi': {
        name: '🎨 Morandi莫兰迪风',
        config: {
            primaryBg: '#F5F1EB',
            secondaryBg: '#E8E2D5',
            textPrimary: '#8B7D6B',
            textSecondary: '#A69B8A',
            accentWarm: '#D4C4A8',
            accentCool: '#B8C5C1',
            goalGradient: 'linear-gradient(135deg, #D4C4A8 0%, #C8B8A8 100%)',
            areaGradient: 'linear-gradient(135deg, #B8C5C1 0%, #A8B5B1 100%)',
            shadowColor: 'rgba(139, 125, 107, 0.1)'
        }
    },
    'dark': {
        name: '🌙 深色商务风',
        config: {
            primaryBg: '#1E1E1E',
            secondaryBg: '#2D2D2D',
            textPrimary: '#E5E5E5',
            textSecondary: '#B3B3B3',
            accentWarm: '#FF8C42',
            accentCool: '#4ECDC4',
            goalGradient: 'linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%)',
            areaGradient: 'linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%)',
            shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
    },
    'mint': {
        name: '🌿 清新薄荷风',
        config: {
            primaryBg: '#F0FFF4',
            secondaryBg: '#E6FFFA',
            textPrimary: '#2D5A3D',
            textSecondary: '#4A7C59',
            accentWarm: '#98D8C8',
            accentCool: '#6BCF7F',
            goalGradient: 'linear-gradient(135deg, #98D8C8 0%, #7FCDCD 100%)',
            areaGradient: 'linear-gradient(135deg, #6BCF7F 0%, #5CB85C 100%)',
            shadowColor: 'rgba(45, 90, 61, 0.1)'
        }
    },
    'violet': {
        name: '💜 优雅紫罗兰风',
        config: {
            primaryBg: '#F8F6FF',
            secondaryBg: '#F0EBFF',
            textPrimary: '#5D4E75',
            textSecondary: '#7A6B8D',
            accentWarm: '#B19CD9',
            accentCool: '#9B8CE8',
            goalGradient: 'linear-gradient(135deg, #B19CD9 0%, #A084C7 100%)',
            areaGradient: 'linear-gradient(135deg, #9B8CE8 0%, #8A7DD6 100%)',
            shadowColor: 'rgba(93, 78, 117, 0.1)'
        }
    },
    'orange': {
        name: '🧡 温暖橙色风',
        config: {
            primaryBg: '#FFF8F0',
            secondaryBg: '#FFF2E6',
            textPrimary: '#8B4513',
            textSecondary: '#A0522D',
            accentWarm: '#FFB366',
            accentCool: '#FF9A56',
            goalGradient: 'linear-gradient(135deg, #FFB366 0%, #FF9A56 100%)',
            areaGradient: 'linear-gradient(135deg, #FF9A56 0%, #FF8C42 100%)',
            shadowColor: 'rgba(139, 69, 19, 0.1)'
        }
    }
};

// 创建主题切换器界面
const themeSwitcher = document.createElement('div');
themeSwitcher.style.cssText = `
    background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(139, 115, 85, 0.08);
    border: 1px solid rgba(232, 180, 160, 0.2);
`;

themeSwitcher.innerHTML = `
    <div style="text-align: center; margin-bottom: 15px;">
        <h3 style="margin: 0; color: #8B7355; font-size: 1.1em;">
            🎨 主题切换器
        </h3>
        <p style="margin: 5px 0 0 0; color: #A0896B; font-size: 0.9em;">
            点击下方按钮切换不同风格主题
        </p>
    </div>
    <div id="theme-buttons" style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
    </div>
    <div style="margin-top: 15px; text-align: center;">
        <button id="export-theme" style="background: #E8D5A5; color: white; border: none;
                border-radius: 8px; padding: 6px 12px; cursor: pointer; font-size: 0.8em; margin-right: 8px;">
            📤 导出当前主题
        </button>
        <button id="reset-theme" style="background: #A8C8A8; color: white; border: none;
                border-radius: 8px; padding: 6px 12px; cursor: pointer; font-size: 0.8em;">
            🔄 重置为默认
        </button>
    </div>
`;

container.appendChild(themeSwitcher);

// 创建主题按钮
const buttonContainer = themeSwitcher.querySelector('#theme-buttons');
Object.keys(THEMES).forEach(themeKey => {
    const theme = THEMES[themeKey];
    const button = document.createElement('button');
    button.style.cssText = `
        background: ${theme.config.goalGradient};
        color: white;
        border: none;
        border-radius: 12px;
        padding: 8px 16px;
        cursor: pointer;
        font-size: 0.85em;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px ${theme.config.shadowColor};
    `;
    button.textContent = theme.name;

    // 悬浮效果
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px) scale(1.05)';
        this.style.boxShadow = `0 4px 16px ${theme.config.shadowColor}`;
    });

    button.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = `0 2px 8px ${theme.config.shadowColor}`;
    });

    // 点击切换主题
    button.addEventListener('click', function() {
        applyTheme(theme.config);
        showNotification(`已切换到 ${theme.name}`);
    });

    buttonContainer.appendChild(button);
});

// 导出主题功能
themeSwitcher.querySelector('#export-theme').addEventListener('click', function() {
    const currentTheme = getCurrentThemeConfig();
    const json = JSON.stringify(currentTheme, null, 2);
    downloadFile('my-custom-theme.json', json);
    showNotification('主题配置已导出');
});

// 重置主题功能
themeSwitcher.querySelector('#reset-theme').addEventListener('click', function() {
    applyTheme(THEMES['milk-tea'].config);
    showNotification('已重置为默认主题');
});

// 应用主题函数
function applyTheme(config) {
    // 获取所有仪表盘组件
    const dashboardElements = document.querySelectorAll('[data-dashboard-component]');

    // 应用主题到各个组件
    dashboardElements.forEach(element => {
        updateElementTheme(element, config);
    });

    // 保存主题配置到本地存储
    localStorage.setItem('dashboard-theme', JSON.stringify(config));
}

// 更新元素主题
function updateElementTheme(element, config) {
    // 更新背景色
    if (element.style.background && element.style.background.includes('linear-gradient')) {
        element.style.background = `linear-gradient(135deg, ${config.primaryBg} 0%, ${config.secondaryBg} 100%)`;
    }

    // 更新文字颜色
    if (element.style.color) {
        element.style.color = config.textPrimary;
    }

    // 更新阴影
    if (element.style.boxShadow) {
        element.style.boxShadow = `0 4px 20px ${config.shadowColor}`;
    }
}

// 获取当前主题配置
function getCurrentThemeConfig() {
    const saved = localStorage.getItem('dashboard-theme');
    return saved ? JSON.parse(saved) : THEMES['milk-tea'].config;
}

// 下载文件函数
function downloadFile(filename, content) {
    const blob = new Blob([content], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}

// 显示通知
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 1000;
        font-size: 0.9em;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);

    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        notification.remove();
        style.remove();
    }, 3000);
}

// 页面加载时应用保存的主题
const savedTheme = getCurrentThemeConfig();
if (savedTheme) {
    applyTheme(savedTheme);
}
```

## 🎯 使用说明

### 1. **安装主题切换器**
- 将上面的代码复制到您的 `项目仪表盘.md` 文件的**最开头**
- 保存文件，刷新页面

### 2. **切换主题**
- 点击任意主题按钮即可切换
- 主题会自动保存到浏览器本地存储
- 下次打开时会自动应用上次选择的主题

### 3. **导出/导入主题**
- 点击"📤 导出当前主题"可以保存主题配置文件
- 可以分享给其他用户或备份使用

### 4. **自定义主题**
- 修改 `THEMES` 对象中的配置
- 添加新的主题选项
- 调整颜色、渐变、阴影等属性

## 🔧 高级定制

### 添加新主题
```javascript
// 在 THEMES 对象中添加新主题
'my-theme': {
    name: '🌟 我的自定义主题',
    config: {
        primaryBg: '#您的颜色',
        secondaryBg: '#您的颜色',
        // ... 其他配置
    }
}
```

### 修改主题属性
```javascript
// 每个主题配置包含以下属性：
{
    primaryBg: '主背景色',
    secondaryBg: '次背景色',
    textPrimary: '主文字色',
    textSecondary: '次文字色',
    accentWarm: '暖色调强调色',
    accentCool: '冷色调强调色',
    goalGradient: '目标按钮渐变',
    areaGradient: '领域按钮渐变',
    shadowColor: '阴影颜色'
}
```

## 💡 使用技巧

1. **主题预览**：悬浮在按钮上可以看到该主题的代表色彩
2. **快速重置**：如果主题效果不满意，点击"🔄 重置为默认"
3. **主题备份**：定期导出喜欢的主题配置作为备份
4. **团队共享**：可以导出主题文件分享给团队成员

---

🎨 **现在您可以轻松切换各种风格的主题，让您的项目仪表盘始终保持新鲜感！**
