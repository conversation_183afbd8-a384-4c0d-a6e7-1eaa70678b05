---
tags:
  - type/dashboard
  - dashboard/energy
created: 2025-05-16T10:58
updated: 2025-05-16T14:30
---

# 精力管理仪表盘

## 📊 精力记录统计

```dataviewjs
// 定义精力类型及其图标
const energyTypes = {
    "补充剂": "💊",
    "中医": "🌿",
    "习惯": "🔄",
    "恢复": "🔋",
    "调理": "🌡️",
    "西医": "🏥",
    "其他": "❓"
};

// 获取所有包含精力标签的记录
const energyRecords = dv.pages()
    .file.lists
    .where(li =>
        li.text.includes("【健康】精力") ||
        li.text.includes("#精力/")
    );

// 按类型统计记录数量
const typeCounts = {};
const recentRecords = {};

// 初始化类型计数
Object.keys(energyTypes).forEach(type => {
    typeCounts[type] = 0;
    recentRecords[type] = [];
});

// 统计各类型记录数量和收集最近记录
energyRecords.forEach(record => {
    let matched = false;
    const lowerText = record.text.toLowerCase();

    // 检查是否匹配特定类型
    Object.keys(energyTypes).forEach(type => {
        if (!matched && (
            lowerText.includes(`精力/${type.toLowerCase()}`) ||
            lowerText.includes(`#精力/${type.toLowerCase()}`)
        )) {
            typeCounts[type]++;
            if (recentRecords[type].length < 3) {
                recentRecords[type].push(record);
            }
            matched = true;
        }
    });

    // 如果没有匹配到类型，尝试通过关键词匹配
    if (!matched) {
        const typeKeywords = {
            '补充剂': ['维生素', '钙片', '补充剂', '营养素'],
            '中医': ['中医', '健胰玉液', '中药'],
            '习惯': ['sy', 'sy频率', '梦遗', '习惯'],
            '恢复': ['恢复', '放松', '冥想', '光脚'],
            '调理': ['调理', '腹泻', '感冒', '症状'],
            '西医': ['西医', '医院', '检查', '西药']
        };

        for (const [type, keywords] of Object.entries(typeKeywords)) {
            if (!matched && keywords.some(kw => lowerText.includes(kw.toLowerCase()))) {
                typeCounts[type]++;
                if (recentRecords[type].length < 3) {
                    recentRecords[type].push(record);
                }
                matched = true;
            }
        }

        // 如果仍未匹配，归为"其他"
        if (!matched) {
            typeCounts["其他"]++;
            if (recentRecords["其他"].length < 3) {
                recentRecords["其他"].push(record);
            }
        }
    }
});

// 显示统计结果
dv.header(3, "记录数量");

// 创建表格显示各类型记录数量
const typeTable = dv.markdownTable(
    ["类型", "图标", "记录数量"],
    Object.entries(typeCounts).map(([type, count]) => {
        return [type, energyTypes[type], count + "条"];
    })
);
dv.paragraph(typeTable);

// 计算总记录数和百分比
const totalRecords = Object.values(typeCounts).reduce((sum, count) => sum + count, 0);

// 准备饼图数据
const types = Object.keys(typeCounts);
const counts = types.map(type => typeCounts[type]);
const labels = types.map(type => `"${energyTypes[type]} ${type}"`).join(", ");
const data = counts.join(", ");

// 显示图表
dv.header(4, "类型分布图表");

// 显示图表
dv.paragraph(`
<div style="display: flex; flex-wrap: wrap; gap: 20px; margin-top: 10px;">
<div style="flex: 1; min-width: 300px;">

#### 类型分布饼图

\`\`\`chart
type: pie
labels: [${labels}]
series:
  - title: 精力记录类型分布
    data: [${data}]
labelColors: true
width: 100%
height: 220px
legend: true
legendPosition: right
\`\`\`
</div>

<div style="flex: 1; min-width: 300px;">

#### 类型分布柱状图

\`\`\`chart
type: bar
labels: [${labels}]
series:
  - title: 记录数量
    data: [${data}]
labelColors: true
width: 100%
height: 220px
legend: true
legendPosition: bottom
fillType: solid
\`\`\`
</div>
</div>
`);

// 显示最近记录
dv.header(3, "最近记录");
Object.entries(recentRecords).forEach(([type, records]) => {
    if (records.length > 0) {
        dv.header(4, `${energyTypes[type]} ${type}`);
        dv.list(records.map(record => `${record.text} (来自: ${record.link})`));
    }
});

// 提取所有精力记录
const allEnergyRecords = dv.pages()
    .file.lists
    .where(li =>
        li.text.includes("【健康】精力") ||
        li.text.includes("#精力/")
    );

// 提取关键词和频率
let keywords = {};
allEnergyRecords.forEach(record => {
    // 提取关键词 (简单实现，可根据需要优化)
    const text = record.text.toLowerCase();
    const possibleKeywords = ["维生素", "钙片", "SY", "调理", "中医", "腹泻", "感冒", "恢复", "运动"];

    possibleKeywords.forEach(keyword => {
        if (text.includes(keyword.toLowerCase())) {
            keywords[keyword] = (keywords[keyword] || 0) + 1;
        }
    });
});

// 显示关键词频率
dv.header(3, "常见影响因素");
const sortedKeywords = Object.entries(keywords)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);

if (sortedKeywords.length > 0) {
    const keywordTable = dv.markdownTable(
        ["关键词", "出现次数", "频率可视化"],
        sortedKeywords.map(([keyword, count]) => {
            const barLength = Math.min(20, Math.max(1, Math.round(count / sortedKeywords[0][1] * 20)));
            const bar = "█".repeat(barLength) + "░".repeat(20 - barLength);
            return [keyword, count, bar];
        })
    );
    dv.paragraph(keywordTable);
} else {
    dv.paragraph("暂无足够数据分析关键词频率");
}
```

## 📈 精力记录趋势分析

```dataviewjs
// 获取最近90天的精力记录
const dailyNotes = dv.pages('"0_Bullet Journal/Daily Notes"')
    .sort(p => p.file.name, 'desc')
    .limit(90);

// 提取精力记录
let energyRecordsByDate = {};
let energyRecordsByType = {};
const types = ['补充剂', '中医', '习惯', '恢复', '调理', '西医', '其他'];

// 初始化类型计数
types.forEach(type => {
    energyRecordsByType[type] = [];
});

// 按日期和类型收集记录
dailyNotes.file.lists
    .where(li => li.text.includes("【健康】精力") || li.text.includes("#精力/"))
    .forEach(record => {
        // 提取日期 (YYYY-MM)
        const dateMatch = record.link.path.match(/(\d{4}-\d{2})-\d{2}/);
        if (dateMatch) {
            const monthYear = dateMatch[1]; // YYYY-MM

            // 按月份分组
            if (!energyRecordsByDate[monthYear]) {
                energyRecordsByDate[monthYear] = 0;
            }
            energyRecordsByDate[monthYear]++;

            // 按类型分组
            let matched = false;
            const lowerText = record.text.toLowerCase();

            // 检查是否匹配特定类型
            types.forEach(type => {
                if (!matched && (
                    lowerText.includes(`精力/${type.toLowerCase()}`) ||
                    lowerText.includes(`#精力/${type.toLowerCase()}`)
                )) {
                    energyRecordsByType[type].push(record);
                    matched = true;
                }
            });

            // 如果没有匹配到类型，尝试通过关键词匹配
            if (!matched) {
                const typeKeywords = {
                    '补充剂': ['维生素', '钙片', '补充剂', '营养素'],
                    '中医': ['中医', '健胰玉液', '中药'],
                    '习惯': ['sy', 'sy频率', '梦遗', '习惯'],
                    '恢复': ['恢复', '放松', '冥想', '光脚'],
                    '调理': ['调理', '腹泻', '感冒', '症状'],
                    '西医': ['西医', '医院', '检查', '西药']
                };

                for (const [type, keywords] of Object.entries(typeKeywords)) {
                    if (!matched && keywords.some(kw => lowerText.includes(kw.toLowerCase()))) {
                        energyRecordsByType[type].push(record);
                        matched = true;
                    }
                }

                // 如果仍未匹配，归为"其他"
                if (!matched) {
                    energyRecordsByType['其他'].push(record);
                }
            }
        }
    });

// 显示月度趋势
dv.header(3, "月度精力记录趋势");

// 按月份排序
const sortedMonths = Object.keys(energyRecordsByDate).sort();

if (sortedMonths.length > 0) {
    dv.paragraph(
        sortedMonths.map(month =>
            `${month}: ${energyRecordsByDate[month]}条`
        ).join(" | ")
    );

    // 准备月度趋势图数据
    const monthLabels = sortedMonths.map(month => `"${month}"`).join(", ");
    const monthData = sortedMonths.map(month => energyRecordsByDate[month]).join(", ");

        // 显示月度趋势图
    dv.header(4, "月度趋势分析");

    // 显示图表
    dv.paragraph(`
<div style="display: flex; flex-wrap: wrap; gap: 20px; margin-top: 10px;">
<div style="flex: 1; min-width: 300px;">

#### 月度记录数量趋势

\`\`\`chart
type: line
labels: [${monthLabels}]
series:
  - title: 月度精力记录数量
    data: [${monthData}]
fill: true
tension: 0.4
width: 100%
height: 220px
legend: true
legendPosition: bottom
\`\`\`
</div>

<div style="flex: 1; min-width: 300px;">

#### 月度记录数量柱状图

\`\`\`chart
type: bar
labels: [${monthLabels}]
series:
  - title: 月度精力记录数量
    data: [${monthData}]
labelColors: true
width: 100%
height: 220px
legend: true
legendPosition: bottom
fillType: solid
\`\`\`
</div>
</div>
`);
} else {
    dv.paragraph("暂无足够数据分析月度趋势");
}

// 显示类型分布统计
dv.header(3, "精力记录类型统计");

// 计算各类型记录数量
const typeDistCounts = {};
types.forEach(type => {
    typeDistCounts[type] = energyRecordsByType[type].length;
});

// 显示类型分布
dv.paragraph(
    types.map(type =>
        `${type}: ${typeDistCounts[type]}条`
    ).join(" | ")
);

// 准备类型分布图数据
const typeLabels = types.map(type => `"${type}"`).join(", ");
const typeData = types.map(type => typeDistCounts[type]).join(", ");

// 显示类型分布图表
dv.header(3, "精力记录类型图表");

// 显示图表
dv.paragraph(`
<div style="display: flex; flex-wrap: wrap; gap: 20px; margin-top: 10px;">
<div style="flex: 1; min-width: 300px;">

#### 类型分布饼图

\`\`\`chart
type: pie
labels: [${typeLabels}]
series:
  - title: 精力记录类型分布
    data: [${typeData}]
labelColors: true
width: 100%
height: 220px
legend: true
legendPosition: right
\`\`\`
</div>

<div style="flex: 1; min-width: 300px;">

#### 类型分布柱状图

\`\`\`chart
type: bar
labels: [${typeLabels}]
series:
  - title: 记录数量
    data: [${typeData}]
labelColors: true
width: 100%
height: 220px
legend: true
legendPosition: bottom
fillType: solid
\`\`\`
</div>
</div>
`);
```

## 🔍 精力影响因素分析

```dataviewjs
// 获取所有精力记录
const allRecords = dv.pages()
    .file.lists
    .where(li =>
        li.text.includes("【健康】精力") ||
        li.text.includes("#精力/")
    );

// 提取效果评分
const effectScores = {};
allRecords.forEach(record => {
    // 尝试提取效果评分
    const scoreMatch = record.text.match(/效果:(\d)/);
    if (scoreMatch && scoreMatch[1]) {
        const score = parseInt(scoreMatch[1]);

        // 提取类型
        let recordType = "其他";
        const typeMatch = record.text.match(/#精力\/(\w+)/);
        if (typeMatch && typeMatch[1]) {
            recordType = typeMatch[1];
        } else {
            // 尝试从文本中推断类型
            const typeKeywords = {
                '补充剂': ['维生素', '钙片', '补充剂', '营养素'],
                '中医': ['中医', '健胰玉液', '中药'],
                '习惯': ['sy', 'sy频率', '梦遗', '习惯'],
                '恢复': ['恢复', '放松', '冥想', '光脚'],
                '调理': ['调理', '腹泻', '感冒', '症状'],
                '西医': ['西医', '医院', '检查', '西药']
            };

            const lowerText = record.text.toLowerCase();
            for (const [type, keywords] of Object.entries(typeKeywords)) {
                if (keywords.some(kw => lowerText.includes(kw.toLowerCase()))) {
                    recordType = type;
                    break;
                }
            }
        }

        // 记录评分
        if (!effectScores[recordType]) {
            effectScores[recordType] = [];
        }
        effectScores[recordType].push(score);
    }
});

// 计算平均评分
dv.header(3, "各类型平均效果评分");
const avgScores = {};
for (const [type, scores] of Object.entries(effectScores)) {
    if (scores.length > 0) {
        const sum = scores.reduce((a, b) => a + b, 0);
        avgScores[type] = sum / scores.length;
    }
}

// 显示平均评分
if (Object.keys(avgScores).length > 0) {
    const scoreTable = dv.markdownTable(
        ["类型", "平均评分", "样本数", "评分可视化"],
        Object.entries(avgScores).sort((a, b) => b[1] - a[1]).map(([type, avg]) => {
            const scores = effectScores[type];
            const stars = "★".repeat(Math.round(avg)) + "☆".repeat(5 - Math.round(avg));
            return [type, avg.toFixed(1), scores.length, stars];
        })
    );
    dv.paragraph(scoreTable);

    // 准备评分图表数据
    const scoreLabels = Object.keys(avgScores).map(type => `"${type}"`).join(", ");
    const scoreData = Object.values(avgScores).map(avg => avg.toFixed(1)).join(", ");
    const sampleData = Object.keys(avgScores).map(type => effectScores[type].length).join(", ");

    // 显示评分图表
    dv.header(4, "效果评分分析");

    // 显示图表
    dv.paragraph(`
<div style="display: flex; flex-wrap: wrap; gap: 20px; margin-top: 10px;">
<div style="flex: 1; min-width: 300px;">

#### 各类型平均效果评分

\`\`\`chart
type: bar
labels: [${scoreLabels}]
series:
  - title: 平均评分
    data: [${scoreData}]
labelColors: true
width: 100%
height: 250px
legend: true
legendPosition: bottom
fillType: solid
options:
  scales:
    y:
      min: 0
      max: 5
      ticks:
        stepSize: 1
\`\`\`
</div>

<div style="flex: 1; min-width: 300px;">

#### 各类型样本数量

\`\`\`chart
type: bar
labels: [${scoreLabels}]
series:
  - title: 样本数
    data: [${sampleData}]
labelColors: true
width: 100%
height: 250px
legend: true
legendPosition: bottom
fillType: solid
\`\`\`
</div>
</div>
`);
} else {
    dv.paragraph("暂无足够的评分数据进行分析");
}
```

## 📚 精力管理资源

### 补充剂资源
- [[维生素D研究]] - 维生素D对精力的影响研究
- [[钙片补充指南]] - 钙片补充的最佳实践

### 中医资源
- [[中医精力调理方案]] - 中医角度的精力管理
- [[健胰玉液饮茶配方]] - 健胰玉液饮茶的制作方法

### 恢复方法
- [[精力恢复技巧]] - 快速恢复精力的方法集
- [[冥想与精力]] - 冥想对精力的影响

### 调理方法
- [[调理记录]] - 调理方法与效果记录

### 西医治疗
- [[医院检查记录]] - 医院检查结果汇总
- [[西医治疗方案]] - 西医角度的精力管理

### 习惯管理
- [[SY频率研究]] - SY频率对精力的影响研究
- [[精力习惯追踪]] - 影响精力的习惯追踪
