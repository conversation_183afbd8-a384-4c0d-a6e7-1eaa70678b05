# 🚀 Obsidian项目仪表盘 - 快速开始指南

> 5分钟快速部署您的项目管理仪表盘！

## ⚡ 快速部署（5分钟）

### 第1步：准备环境（2分钟）

1. **确保Obsidian已安装**
   - 如果没有，请访问 [obsidian.md](https://obsidian.md/) 下载

2. **安装Dataview插件**
   ```
   设置 → 第三方插件 → 浏览 → 搜索"Dataview" → 安装 → 启用
   ```

3. **启用JavaScript查询**
   ```
   设置 → Dataview → ✅ Enable JavaScript Queries
   ```

### 第2步：创建文件夹结构（1分钟）

在您的库中创建以下文件夹：
```
📁 1_Areas/          # 领域管理
📁 2_Goals/          # 目标管理  
📁 6_Project Notes/  # 项目文件
```

### 第3步：复制仪表盘文件（1分钟）

1. 复制 `项目仪表盘.md` 到库根目录
2. 打开文件，确认显示正常

### 第4步：创建第一个项目（1分钟）

在 `6_Project Notes/` 文件夹中创建 `测试项目.md`：

```yaml
---
tags: [type/project]
Status: active
Goal: "[[学习项目管理]]"
Area: "[[个人发展]]"
start_date: 2024-01-01
end_date: 2024-03-31
progress: 30
---

# 测试项目

这是我的第一个项目！
```

### 第5步：验证部署

打开 `项目仪表盘.md`，您应该看到：
- ✅ 项目看板显示统计数据
- ✅ 活跃项目展示您的测试项目
- ✅ 可以点击目标和领域标签

🎉 **恭喜！您的项目仪表盘已经可以使用了！**

## 📋 常用模板

### 项目文件模板

```yaml
---
tags: [type/project]
Status: active  # active, completed, risk, warning, cancelled
Goal: "[[目标名称]]"
Area: "[[领域名称]]"
start_date: 2024-01-01
end_date: 2024-12-31
progress: 0
priority: medium  # high, medium, low
---

# 项目名称

## 项目描述
[项目的详细描述]

## 目标
- [ ] 目标1
- [ ] 目标2
- [ ] 目标3

## 里程碑
- 2024-01-01: 项目启动
- 2024-06-01: 中期检查
- 2024-12-31: 项目完成

## 资源链接
- [[相关文档]]
- [[参考资料]]
```

### 目标文件模板

```yaml
---
tags: [type/goal]
status: active
deadline: 2024-12-31
priority: high
---

# 目标名称

## 目标描述
[详细描述这个目标]

## 关键结果
1. 结果1
2. 结果2
3. 结果3

## 相关项目
- [[项目1]]
- [[项目2]]
```

### 领域文件模板

```yaml
---
tags: [type/area]
description: "领域描述"
---

# 领域名称

## 概述
[领域的详细描述]

## 当前项目
```dataview
TABLE Status, progress + "%" as Progress
FROM "6_Project Notes"
WHERE Area = [[领域名称]]
SORT Status ASC
```

## 相关目标
- [[目标1]]
- [[目标2]]
```

## 🎯 使用技巧

### 1. 项目状态管理
```yaml
Status: active     # 🟢 进行中
Status: completed  # ✅ 已完成
Status: risk       # 🔴 风险
Status: warning    # 🟡 警告
Status: cancelled  # ❌ 已取消
```

### 2. 进度跟踪
```yaml
progress: 75  # 手动设置进度百分比
```

### 3. 时间管理
```yaml
start_date: 2024-01-01  # 开始日期
end_date: 2024-12-31    # 结束日期
```

### 4. 优先级设置
```yaml
priority: high    # 高优先级
priority: medium  # 中等优先级
priority: low     # 低优先级
```

## 🔧 常见问题解决

### Q: 仪表盘显示空白？
**A:** 检查以下项目：
- ✅ Dataview插件已启用
- ✅ JavaScript查询已启用
- ✅ 项目文件包含正确的YAML头部
- ✅ 文件夹路径正确

### Q: 点击跳转不工作？
**A:** 确保：
- ✅ 目标/领域文件存在
- ✅ 文件名不包含特殊字符
- ✅ 使用正确的双链格式 `[[文件名]]`

### Q: 样式显示异常？
**A:** 尝试：
- 🔄 刷新页面
- 📱 检查Obsidian版本
- 🎨 确认CSS代码完整

### Q: 如何添加新的项目状态？
**A:** 在仪表盘代码中找到状态配置部分，添加新的状态：
```javascript
case 'your-status':
    filteredProjects = allProjects.filter(p => p.Status === "your-status");
    break;
```

## 🎨 个性化定制

### 修改颜色主题
在仪表盘文件中找到颜色配置：
```css
/* 主背景色 */
background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%);

/* 目标按钮颜色 */
background: linear-gradient(135deg, #E8D5A5 0%, #D4C574 100%);

/* 领域按钮颜色 */
background: linear-gradient(135deg, #A8C8A8 0%, #8FB88F 100%);
```

### 调整文件夹路径
如果您的项目文件夹名称不同，修改查询路径：
```javascript
const projects = dv.pages('"您的项目文件夹名称"')
```

### 添加自定义字段
在项目模板中添加新字段：
```yaml
---
team: "团队名称"
budget: 10000
client: "客户名称"
---
```

## 📚 进阶使用

### 1. 批量创建项目
使用Templater插件创建项目模板，快速生成新项目。

### 2. 数据导出
添加导出功能，将项目数据导出为CSV或JSON格式。

### 3. 集成其他插件
- **Calendar** - 项目时间线视图
- **Kanban** - 看板式项目管理
- **Charts** - 数据可视化图表

### 4. 自动化工作流
使用Obsidian的自动化功能，定期更新项目状态和进度。

## 🌟 最佳实践

### 1. 命名规范
- 项目文件：`项目名称.md`
- 目标文件：`YYYY年目标名称.md`
- 领域文件：`领域名称.md`

### 2. 标签体系
```yaml
tags: [type/project, priority/high, team/dev]
```

### 3. 定期维护
- 每周更新项目进度
- 每月回顾项目状态
- 及时清理已完成项目

### 4. 备份策略
- 定期备份项目数据
- 使用Git进行版本控制
- 云端同步重要文件

## 🎯 下一步

现在您已经成功部署了项目仪表盘，建议：

1. **创建更多项目** - 将现有项目迁移到系统中
2. **设置目标和领域** - 建立完整的项目分类体系
3. **定制界面** - 根据个人喜好调整颜色和布局
4. **探索高级功能** - 查看完整文档了解更多功能

## 📞 获取帮助

如果遇到问题，可以：
- 📖 查看 `项目仪表盘完整文档.md`
- 🌐 访问Obsidian社区论坛
- 💬 在GitHub提交Issue

---

🌸 **享受您的项目管理之旅！**

*这个仪表盘将帮助您更好地组织和跟踪项目，提高工作效率。*
