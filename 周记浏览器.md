---
created: 2025-05-25T16:55
updated: 2025-05-25T16:55
tags:
  - type/dashboard
  - weekly/browser
---

# 📅 周记浏览器

> 🗓️ 每周回顾与总结，追踪成长轨迹

## 🚀 快速操作

```dataviewjs
// 创建快速操作面板
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F0F8FF 0%, #F8F0FF 100%);
    border-radius: 16px;
    border: 2px solid rgba(165, 214, 232, 0.3);
`;

// 计算当前周数
const now = new Date();
const year = now.getFullYear();
const weekNumber = Math.ceil((now - new Date(now.getFullYear(), 0, 1)) / (7 * 24 * 60 * 60 * 1000));
const currentWeek = `${year}-W${String(weekNumber).padStart(2, '0')}`;

const quickActions = [
    {
        title: '本周周记',
        icon: '📝',
        desc: '创建本周总结',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText(`0_Bullet Journal/Weekly Notes/${currentWeek}`, '', true);
            }
        },
        color: '#A5D6E8'
    },
    {
        title: '周记模板',
        icon: '📋',
        desc: '查看周记模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates/Weekly Template', '', false);
            }
        },
        color: '#B8D6B8'
    },
    {
        title: '回顾仪表板',
        icon: '📊',
        desc: '查看回顾统计',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('回顾仪表板', '', false);
            }
        },
        color: '#E8D5A5'
    },
    {
        title: '日记浏览器',
        icon: '📅',
        desc: '查看日记记录',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('日志浏览器', '', false);
            }
        },
        color: '#E8B4A0'
    }
];

let actionsHTML = '';
quickActions.forEach((action, index) => {
    actionsHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 12px; padding: 18px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action-btn" data-index="${index}">
            <div style="font-size: 2em; margin-bottom: 8px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickActionsContainer.innerHTML = actionsHTML;

// 添加点击事件
setTimeout(() => {
    const actionBtns = quickActionsContainer.querySelectorAll('.quick-action-btn');
    actionBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            quickActions[index].action();
        });

        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 📅 所有周记

```dataview
TABLE WITHOUT ID
    file.link AS "🗓️ 周次",
    choice(week_goal, week_goal, "未设置目标") AS "🎯 本周目标",
    choice(achievements, achievements, "未记录成就") AS "🏆 主要成就",
    choice(reflection, reflection, "未写反思") AS "💭 反思总结",
    file.mtime AS "📝 修改时间"
FROM "0_Bullet Journal/Weekly Notes"
WHERE file.name != "README"
SORT file.name DESC
LIMIT 12
```

## 📊 周记统计

```dataviewjs
// 获取所有周记
const allWeeklies = dv.pages('"0_Bullet Journal/Weekly Notes"').where(p => p.file.name !== "README");

// 统计数据
const totalWeeklies = allWeeklies.length;
const currentYear = new Date().getFullYear();
const currentYearWeeklies = allWeeklies.where(p => p.file.name.startsWith(currentYear.toString())).length;

// 计算本年度已过去的周数
const startOfYear = new Date(currentYear, 0, 1);
const now = new Date();
const weeksPassed = Math.ceil((now - startOfYear) / (7 * 24 * 60 * 60 * 1000));
const completionRate = weeksPassed > 0 ? Math.round((currentYearWeeklies / weeksPassed) * 100) : 0;

// 创建统计容器
const statsContainer = document.createElement('div');
statsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F0F8FF 0%, #F8F0FF 100%);
    border-radius: 16px;
    border: 2px solid rgba(165, 214, 232, 0.2);
`;

const stats = [
    { label: '总周记', value: totalWeeklies, icon: '📅', color: '#A5D6E8' },
    { label: '本年周记', value: currentYearWeeklies, icon: '📝', color: '#B8D6B8' },
    { label: '已过周数', value: weeksPassed, icon: '⏰', color: '#E8D5A5' },
    { label: '记录率', value: completionRate + '%', icon: '📊', color: '#E8B4A0' }
];

let statsHTML = '';
stats.forEach(stat => {
    statsHTML += `
        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); 
                    border-radius: 12px; border: 1px solid ${stat.color}20;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${stat.icon}</div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${stat.color}; margin-bottom: 4px;">
                ${stat.value}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500;">
                ${stat.label}
            </div>
        </div>
    `;
});

statsContainer.innerHTML = statsHTML;
this.container.appendChild(statsContainer);
```

## 🗓️ 按年份浏览

```dataview
TABLE WITHOUT ID
    substring(file.name, 0, 4) AS "📅 年份",
    length(rows.file.link) AS "📊 周记数量",
    join(rows.file.link, ", ") AS "🗓️ 周记列表"
FROM "0_Bullet Journal/Weekly Notes"
WHERE file.name != "README" AND file.name CONTAINS "-W"
GROUP BY substring(file.name, 0, 4)
SORT substring(file.name, 0, 4) DESC
```

## 🎯 目标达成情况

```dataview
TABLE WITHOUT ID
    file.link AS "🗓️ 周次",
    choice(week_goal, week_goal, "未设置") AS "🎯 目标",
    choice(goal_achieved, 
           choice(goal_achieved = true, "✅ 已达成", "❌ 未达成"),
           "❓ 未评估") AS "📊 达成情况",
    choice(next_week_plan, next_week_plan, "未制定计划") AS "📋 下周计划"
FROM "0_Bullet Journal/Weekly Notes"
WHERE week_goal AND file.name != "README"
SORT file.name DESC
LIMIT 8
```

## 🏆 成就回顾

```dataview
TABLE WITHOUT ID
    file.link AS "🗓️ 周次",
    choice(achievements, achievements, "无记录") AS "🏆 主要成就",
    choice(challenges, challenges, "无记录") AS "⚡ 面临挑战",
    choice(lessons_learned, lessons_learned, "无记录") AS "📚 经验教训"
FROM "0_Bullet Journal/Weekly Notes"
WHERE achievements AND file.name != "README"
SORT file.name DESC
LIMIT 6
```

---

<div style="text-align: center; margin-top: 30px; padding: 15px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 12px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600; margin-bottom: 6px;">
        🗓️ 周记回顾小贴士
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        每周回顾是成长的阶梯，总结经验，规划未来，让每一周都有意义
    </div>
</div>
