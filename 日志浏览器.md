---
created: 2025-05-25T16:50
updated: 2025-05-25T16:50
tags:
  - type/dashboard
  - journal/browser
---

# 📝 日志浏览器

> 📅 记录生活点滴，追踪成长轨迹

## 🚀 快速操作

```dataviewjs
// 创建快速操作面板
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #FFF5F5 0%, #FFF8F5 100%);
    border-radius: 16px;
    border: 2px solid rgba(232, 165, 165, 0.3);
`;

// 创建正确的日期格式：YYYY-MM-DD ddd WW
const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const day = String(now.getDate()).padStart(2, '0');
const dayOfWeek = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][now.getDay()];
const weekNumber = Math.ceil((now - new Date(now.getFullYear(), 0, 1)) / (7 * 24 * 60 * 60 * 1000));
const formattedDate = `${year}-${month}-${day} ${dayOfWeek} ${String(weekNumber).padStart(2, '0')}`;

const quickActions = [
    {
        title: '今日日记',
        icon: '📝',
        desc: '创建今日记录',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText(`0_Bullet Journal/Daily Notes/${formattedDate}`, '', true);
            }
        },
        color: '#E8A5A5'
    },
    {
        title: '精力记录',
        icon: '⚡',
        desc: '记录精力状态',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('6_Project Notes/精力/精力仪表盘', '', false);
            }
        },
        color: '#E8D5A5'
    },
    {
        title: '睡眠日志',
        icon: '😴',
        desc: '记录睡眠质量',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('0_Bullet Journal/睡眠日志/睡眠日志统计图表', '', false);
            }
        },
        color: '#D0C4E8'
    },
    {
        title: '日记模板',
        icon: '📋',
        desc: '查看日记模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates', '', false);
            }
        },
        color: '#B8D6B8'
    }
];

let actionsHTML = '';
quickActions.forEach((action, index) => {
    actionsHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 12px; padding: 18px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action-btn" data-index="${index}">
            <div style="font-size: 2em; margin-bottom: 8px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickActionsContainer.innerHTML = actionsHTML;

// 添加点击事件
setTimeout(() => {
    const actionBtns = quickActionsContainer.querySelectorAll('.quick-action-btn');
    actionBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            quickActions[index].action();
        });

        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 📅 最近日记

```dataview
TABLE WITHOUT ID
    file.link AS "📅 日期",
    choice(tomato_goal, "🍅 " + tomato_goal, "🍅 未设置") AS "番茄目标",
    choice(tomato_actual, "✅ " + tomato_actual, "✅ 0") AS "实际完成",
    choice(mood, mood, "😐 未记录") AS "心情",
    file.mtime AS "📝 修改时间"
FROM "0_Bullet Journal/Daily Notes"
WHERE file.name != "README"
SORT file.name DESC
LIMIT 15
```

## 📊 本月统计

```dataviewjs
// 获取本月的日记
const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
const monthlyLogs = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.startsWith(currentMonth) && p.file.name !== "README");

// 统计数据
const totalDays = monthlyLogs.length;
const totalTomatoGoal = monthlyLogs.map(p => p.tomato_goal || 0).reduce((a, b) => a + b, 0);
const totalTomatoActual = monthlyLogs.map(p => p.tomato_actual || 0).reduce((a, b) => a + b, 0);
const avgTomatoGoal = totalDays > 0 ? Math.round(totalTomatoGoal / totalDays) : 0;
const avgTomatoActual = totalDays > 0 ? Math.round(totalTomatoActual / totalDays) : 0;
const completionRate = totalTomatoGoal > 0 ? Math.round((totalTomatoActual / totalTomatoGoal) * 100) : 0;

// 创建统计容器
const statsContainer = document.createElement('div');
statsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #FFF5F5 0%, #FFF8F5 100%);
    border-radius: 16px;
    border: 2px solid rgba(232, 165, 165, 0.2);
`;

const stats = [
    { label: '记录天数', value: totalDays, icon: '📅', color: '#E8A5A5' },
    { label: '平均目标', value: avgTomatoGoal, icon: '🎯', color: '#E8D5A5' },
    { label: '平均完成', value: avgTomatoActual, icon: '✅', color: '#B8D6B8' },
    { label: '完成率', value: completionRate + '%', icon: '📊', color: '#A5D6E8' }
];

let statsHTML = '';
stats.forEach(stat => {
    statsHTML += `
        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); 
                    border-radius: 12px; border: 1px solid ${stat.color}20;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${stat.icon}</div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${stat.color}; margin-bottom: 4px;">
                ${stat.value}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500;">
                ${stat.label}
            </div>
        </div>
    `;
});

statsContainer.innerHTML = statsHTML;
this.container.appendChild(statsContainer);
```

## 🗓️ 按月份浏览

```dataview
TABLE WITHOUT ID
    substring(file.name, 0, 7) AS "📅 月份",
    length(rows.file.link) AS "📊 记录天数",
    join(rows.file.link, ", ") AS "📝 日记列表"
FROM "0_Bullet Journal/Daily Notes"
WHERE file.name != "README" AND file.name CONTAINS "-"
GROUP BY substring(file.name, 0, 7)
SORT substring(file.name, 0, 7) DESC
LIMIT 6
```

## 🎯 番茄钟记录

```dataview
TABLE WITHOUT ID
    file.link AS "📅 日期",
    choice(tomato_goal, tomato_goal, 0) AS "🎯 目标",
    choice(tomato_actual, tomato_actual, 0) AS "✅ 完成",
    choice(tomato_actual AND tomato_goal, 
           round((tomato_actual / tomato_goal) * 100) + "%", 
           "0%") AS "📊 完成率"
FROM "0_Bullet Journal/Daily Notes"
WHERE (tomato_goal OR tomato_actual) AND file.name != "README"
SORT file.name DESC
LIMIT 10
```

## 😴 睡眠记录

```dataview
TABLE WITHOUT ID
    file.link AS "📅 日期",
    choice(sleep_time, sleep_time, "未记录") AS "😴 入睡时间",
    choice(wake_time, wake_time, "未记录") AS "⏰ 起床时间",
    choice(sleep_quality, sleep_quality, "未评价") AS "💤 睡眠质量"
FROM "0_Bullet Journal/Daily Notes"
WHERE (sleep_time OR wake_time OR sleep_quality) AND file.name != "README"
SORT file.name DESC
LIMIT 10
```

---

<div style="text-align: center; margin-top: 30px; padding: 15px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 12px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600; margin-bottom: 6px;">
        📝 记录生活小贴士
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        每日记录是自我成长的镜子，坚持记录，见证自己的进步
    </div>
</div>
