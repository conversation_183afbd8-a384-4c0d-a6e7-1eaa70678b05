# 🍅 番茄钟历史数据仪表盘

## 📊 **本周趋势分析**

```dataviewjs
// 数据处理辅助函数
function parseNumber(value) {
    if (value === null || value === undefined || value === '') return 0;
    if (typeof value === 'string') {
        const parsed = parseInt(value.replace(/[^\d]/g, ''));
        return isNaN(parsed) ? 0 : parsed;
    }
    return typeof value === 'number' ? value : 0;
}

// 获取本周数据
const now = new Date();
const dayOfWeek = now.getDay();
const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
const startOfWeek = new Date(now);
startOfWeek.setDate(now.getDate() - (adjustedDayOfWeek - 1));

const weekData = [];
const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

for (let i = 0; i < 7; i++) {
    const checkDate = new Date(startOfWeek);
    checkDate.setDate(startOfWeek.getDate() + i);
    const checkDateStr = checkDate.toISOString().split('T')[0];

    const dayLog = dv.pages('"0_Bullet Journal/Daily Notes"')
        .where(p => p.file.name.includes(checkDateStr))
        .first();

    // 安全地解析数据
    const goal = parseNumber(dayLog?.tomato_goal);
    const actual = parseNumber(dayLog?.tomato_actual);
    const rate = goal > 0 ? Math.round((actual / goal) * 100) : 0;

    weekData.push({
        date: checkDateStr,
        dayName: weekDays[i],
        goal: goal,
        actual: actual,
        rate: rate,
        isToday: checkDateStr === now.toISOString().split('T')[0],
        hasData: dayLog && (goal > 0 || actual > 0)
    });
}

// 创建本周趋势的美观界面
const weekContainer = this.container.createDiv();
weekContainer.style.cssText = `
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-bottom: 20px;
`;

let weekHTML = `
    <h3 style="margin: 0 0 15px 0; color: #333;">📈 本周番茄钟趋势</h3>
    <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 10px;">
`;

weekData.forEach(day => {
    const isToday = day.isToday;
    const hasData = day.hasData;
    const bgColor = isToday ? '#fff3e0' : (hasData ? '#f8f9fa' : '#f5f5f5');
    const borderColor = isToday ? '#ff8c42' : (hasData ? '#dee2e6' : '#e9ecef');
    const rate = day.rate;

    // 数据状态显示
    const dataStatus = hasData ? '' : '<div style="font-size: 0.7em; color: #999; margin-top: 3px;">📝 无数据</div>';
    const actualDisplay = hasData ? `🍅 ${day.actual}` : '🍅 -';
    const goalDisplay = hasData ? `目标: ${day.goal}` : '目标: -';
    const rateDisplay = hasData ? `${rate}%` : '-';

    weekHTML += `
        <div style="background: ${bgColor}; border: 2px solid ${borderColor}; border-radius: 8px; padding: 15px; text-align: center; opacity: ${hasData ? '1' : '0.7'};">
            <div style="font-weight: bold; color: #333; margin-bottom: 5px;">${day.dayName}</div>
            <div style="font-size: 0.8em; color: #666; margin-bottom: 8px;">${day.date.slice(5)}</div>
            <div style="font-size: 1.5em; font-weight: bold; color: ${hasData ? '#ff8c42' : '#999'}; margin-bottom: 5px;">${actualDisplay}</div>
            <div style="font-size: 0.8em; color: #666;">${goalDisplay}</div>
            <div style="font-size: 0.8em; color: ${rate >= 100 ? '#51cf66' : rate >= 80 ? '#ffd43b' : rate > 0 ? '#ff6b6b' : '#999'};">
                ${rateDisplay}
            </div>
            ${dataStatus}
        </div>
    `;
});

weekHTML += `</div>`;
weekContainer.innerHTML = weekHTML;
```

## 📈 **月度统计与分析**

```dataviewjs
// 数据处理辅助函数
function parseNumber(value) {
    if (value === null || value === undefined || value === '') return 0;
    if (typeof value === 'string') {
        const parsed = parseInt(value.replace(/[^\d]/g, ''));
        return isNaN(parsed) ? 0 : parsed;
    }
    return typeof value === 'number' ? value : 0;
}

// 获取最近30天的数据
const today = new Date();
const monthData = [];
let totalGoal = 0;
let totalActual = 0;
let activeDays = 0;
let recordedDays = 0; // 有记录的天数（包括目标为0的天数）

for (let i = 29; i >= 0; i--) {
    const checkDate = new Date(today);
    checkDate.setDate(today.getDate() - i);
    const checkDateStr = checkDate.toISOString().split('T')[0];

    const dayLog = dv.pages('"0_Bullet Journal/Daily Notes"')
        .where(p => p.file.name.includes(checkDateStr))
        .first();

    if (dayLog) {
        // 使用安全的数据解析函数
        const goal = parseNumber(dayLog.tomato_goal);
        const actual = parseNumber(dayLog.tomato_actual);
        const rate = goal > 0 ? Math.round((actual / goal) * 100) : 0;

        // 只要有日记文件就算有记录
        recordedDays++;

        // 只有设置了目标或有实际完成数据才算活跃天数
        if (goal > 0 || actual > 0) {
            monthData.push({
                date: checkDateStr,
                goal: goal,
                actual: actual,
                rate: rate
            });

            totalGoal += goal;
            totalActual += actual;
            activeDays++;
        }
    }
}

// 计算统计数据
const avgGoal = activeDays > 0 ? Math.round(totalGoal / activeDays) : 0;
const avgActual = activeDays > 0 ? Math.round(totalActual / activeDays) : 0;
const overallRate = totalGoal > 0 ? Math.round((totalActual / totalGoal) * 100) : 0;

// 1. 创建月度统计的美观卡片
const monthContainer = this.container.createDiv();
monthContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
`;

const monthStats = [
    {
        label: '记录天数',
        value: recordedDays,
        unit: '天',
        icon: '📝',
        color: '#868e96',
        bgColor: '#f8f9fa'
    },
    {
        label: '活跃天数',
        value: activeDays,
        unit: '天',
        icon: '📅',
        color: '#339af0',
        bgColor: '#f0f8ff'
    },
    {
        label: '总目标',
        value: totalGoal,
        unit: '🍅',
        icon: '🎯',
        color: '#9c88ff',
        bgColor: '#f8f7ff'
    },
    {
        label: '总完成',
        value: totalActual,
        unit: '🍅',
        icon: '✅',
        color: '#51cf66',
        bgColor: '#f3fff3'
    },
    {
        label: '平均目标',
        value: avgGoal,
        unit: '🍅/天',
        icon: '📊',
        color: '#ff8c42',
        bgColor: '#fff8f0'
    },
    {
        label: '平均完成',
        value: avgActual,
        unit: '🍅/天',
        icon: '🔥',
        color: '#ff6b6b',
        bgColor: '#fff5f5'
    },
    {
        label: '总体达成率',
        value: overallRate,
        unit: '%',
        icon: '📈',
        color: '#74c0fc',
        bgColor: '#f0f8ff'
    }
];

monthStats.forEach(stat => {
    const card = monthContainer.createDiv();
    card.style.cssText = `
        background: ${stat.bgColor};
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border: 2px solid transparent;
        transition: all 0.3s ease;
        cursor: pointer;
    `;

    card.innerHTML = `
        <div style="font-size: 2em; margin-bottom: 10px;">${stat.icon}</div>
        <div style="font-size: 1.8em; font-weight: bold; color: ${stat.color}; margin-bottom: 5px;">
            ${stat.value}
        </div>
        <div style="font-size: 1em; color: #666; font-weight: 500;">
            ${stat.label}
        </div>
        <div style="font-size: 0.8em; color: #999; margin-top: 5px;">
            ${stat.unit}
        </div>
    `;

    // 添加悬停效果
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px) scale(1.02)';
        this.style.boxShadow = '0 8px 20px rgba(0,0,0,0.15)';
        this.style.borderColor = stat.color;
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
        this.style.borderColor = 'transparent';
    });
});

// 2. 创建效率分析的美观界面
const performanceData = monthData.filter(d => d.goal > 0);
const highPerformance = performanceData.filter(d => d.rate >= 90);
const mediumPerformance = performanceData.filter(d => d.rate >= 70 && d.rate < 90);
const lowPerformance = performanceData.filter(d => d.rate < 70);

const analysisContainer = this.container.createDiv();
analysisContainer.style.cssText = `
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-bottom: 20px;
`;

let analysisHTML = `
    <h3 style="margin: 0 0 15px 0; color: #333;">🎯 效率分析报告</h3>

    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
        <div style="background: #f3fff3; border-radius: 8px; padding: 15px; text-align: center;">
            <div style="font-size: 1.5em; color: #51cf66; font-weight: bold;">${highPerformance.length}</div>
            <div style="color: #666;">高效天数 (≥90%)</div>
        </div>
        <div style="background: #fff8e1; border-radius: 8px; padding: 15px; text-align: center;">
            <div style="font-size: 1.5em; color: #ffd43b; font-weight: bold;">${mediumPerformance.length}</div>
            <div style="color: #666;">中效天数 (70-89%)</div>
        </div>
        <div style="background: #fff5f5; border-radius: 8px; padding: 15px; text-align: center;">
            <div style="font-size: 1.5em; color: #ff6b6b; font-weight: bold;">${lowPerformance.length}</div>
            <div style="color: #666;">低效天数 (<70%)</div>
        </div>
    </div>
`;

// 最佳表现日期
if (highPerformance.length > 0) {
    const bestDays = highPerformance.slice(-5).map(d => d.date.slice(5)).join(', ');
    analysisHTML += `
        <div style="background: #f3fff3; border-left: 4px solid #51cf66; padding: 15px; margin-bottom: 15px;">
            <strong>🌟 最佳表现日期：</strong> ${bestDays}
        </div>
    `;
}

// 改进建议
let suggestions = [];
if (lowPerformance.length > highPerformance.length) {
    suggestions.push('考虑降低每日目标，设定更现实的期望');
}
if (avgActual < avgGoal * 0.8) {
    suggestions.push('分析任务被打断的原因，优化工作环境');
}
if (activeDays < 20) {
    suggestions.push('提高记录频率，养成每日复盘的习惯');
}

if (suggestions.length > 0) {
    analysisHTML += `
        <div style="background: #fff3e0; border-left: 4px solid #ff8c42; padding: 15px;">
            <strong>💡 改进建议：</strong>
            <ul style="margin: 10px 0 0 0; padding-left: 20px;">
                ${suggestions.map(s => `<li>${s}</li>`).join('')}
            </ul>
        </div>
    `;
}

analysisContainer.innerHTML = analysisHTML;

// 4. 添加数据状态提示
const statusContainer = this.container.createDiv();
statusContainer.style.cssText = `
    background: #e8f5e8;
    border: 1px solid #c3e6c3;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
`;

let statusMessage = '';
if (activeDays === 0) {
    statusMessage = `
        <div style="font-size: 1.2em; color: #2d5a2d; margin-bottom: 10px;">🌱 番茄钟之旅开始</div>
        <p style="color: #2d5a2d; margin: 0;">
            欢迎开始您的番茄钟时间管理之旅！在Daily Notes的YAML属性中添加：<br>
            <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px;">tomato_goal: 8</code> 和
            <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px;">tomato_actual: 6</code>
        </p>
    `;
} else if (activeDays < 7) {
    statusMessage = `
        <div style="font-size: 1.2em; color: #2d5a2d; margin-bottom: 10px;">📈 数据积累中</div>
        <p style="color: #2d5a2d; margin: 0;">
            很好！您已经记录了 ${activeDays} 天的番茄钟数据。继续保持，一周后将有更丰富的趋势分析。
        </p>
    `;
} else {
    statusMessage = `
        <div style="font-size: 1.2em; color: #2d5a2d; margin-bottom: 10px;">🎉 数据充足</div>
        <p style="color: #2d5a2d; margin: 0;">
            太棒了！您已经积累了 ${activeDays} 天的番茄钟数据，可以进行有意义的趋势分析了。
        </p>
    `;
}

statusContainer.innerHTML = statusMessage;

// 3. 创建详细记录表的美观界面
const tableContainer = this.container.createDiv();
tableContainer.style.cssText = `
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

if (monthData.length > 0) {
    let tableHTML = `
        <h3 style="margin: 0 0 15px 0; color: #333;">📋 最近记录详情</h3>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse; font-size: 0.9em;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">日期</th>
                        <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">目标</th>
                        <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">完成</th>
                        <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">达成率</th>
                        <th style="padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6;">状态</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // 显示最近10天的记录
    monthData.slice(-10).forEach(day => {
        const rateColor = day.rate >= 100 ? '#51cf66' :
                         day.rate >= 80 ? '#ffd43b' : '#ff6b6b';
        const statusIcon = day.rate >= 100 ? '🎉' :
                          day.rate >= 80 ? '👍' : '💪';
        const statusText = day.rate >= 100 ? '超额完成' :
                          day.rate >= 80 ? '良好' : '需努力';

        tableHTML += `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px; font-weight: 500;">${day.date}</td>
                <td style="padding: 12px; text-align: center;">🎯 ${day.goal}</td>
                <td style="padding: 12px; text-align: center;">🍅 ${day.actual}</td>
                <td style="padding: 12px; text-align: center; color: ${rateColor}; font-weight: bold;">${day.rate}%</td>
                <td style="padding: 12px; text-align: center;">${statusIcon} ${statusText}</td>
            </tr>
        `;
    });

    tableHTML += `
                </tbody>
            </table>
        </div>
    `;

    tableContainer.innerHTML = tableHTML;
} else {
    tableContainer.innerHTML = `
        <h3 style="margin: 0 0 15px 0; color: #333;">📋 最近记录详情</h3>
        <div style="text-align: center; padding: 40px; color: #666;">
            <div style="font-size: 3em; margin-bottom: 15px;">📝</div>
            <p>暂无历史记录</p>
            <p style="font-size: 0.9em; color: #999;">请在Daily Log中设置番茄钟目标和实际完成数据</p>
        </div>
    `;
}
```

---

## 💡 **使用说明**

### 📊 **数据来源**
- 读取 `0_Bullet Journal/Daily Notes/` 中的所有日记文件
- 基于 YAML 属性：`tomato_goal`、`tomato_actual`、`tomato_rate`

### 🔧 **数据设置**
在每日日记文件的YAML front matter中添加以下属性：
```yaml
tomato_goal: 8        # 当日番茄钟目标数量
tomato_actual: 6      # 实际完成的番茄钟数量
tomato_rate: 75       # 达成率（可选，系统会自动计算）
```

### 🔄 **数据更新**
- 每次打开页面自动刷新数据
- 建议每日在 Daily Log 中更新实际完成数据
- 系统会自动处理数据类型不一致的问题

### 📈 **分析维度**
- **本周趋势**：7天详细对比，显示无数据的天数
- **月度统计**：30天整体表现，区分记录天数和活跃天数
- **效率分析**：高中低效天数分布
- **详细记录**：最近10天明细

### 🎯 **改进建议**
系统会根据你的数据自动生成个性化改进建议，帮助优化番茄钟使用效果。

### 📊 **数据说明**
- **记录天数**：有Daily Notes文件的天数（包括没有番茄钟数据的日期）
- **活跃天数**：实际记录了番茄钟数据的天数
- **历史数据**：以前没有记录番茄钟是正常的，系统只分析有数据的时间段
- **数据积累**：随着使用时间增长，分析结果会越来越准确

### 🌱 **新手指南**
如果您刚开始使用番茄钟技术：
1. 先设定合理的每日目标（建议从6-8个番茄钟开始）
2. 每天记录实际完成数量
3. 一周后查看趋势分析
4. 根据数据调整目标和工作方式
