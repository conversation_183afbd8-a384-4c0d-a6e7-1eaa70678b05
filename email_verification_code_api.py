from flask import Flask, request, jsonify
from flask_cors import CORS
import sqlite3
import os
import time

app = Flask(__name__)
CORS(app)  # 启用跨域请求支持

# 数据库设置
DB_PATH = 'verification_codes.db'

def init_db():
    """初始化数据库，创建表结构"""
    if not os.path.exists(DB_PATH):
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('''
        CREATE TABLE verification_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT NOT NULL,
            code TEXT NOT NULL,
            service TEXT,
            created_at INTEGER NOT NULL,
            used INTEGER DEFAULT 0
        )
        ''')
        
        # 添加一些测试数据
        test_data = [
            ('<EMAIL>', '123456', '域名注册服务', int(time.time()), 0),
            ('<EMAIL>', '654321', '账号验证', int(time.time()), 0)
        ]
        cursor.executemany('INSERT INTO verification_codes (email, code, service, created_at, used) VALUES (?, ?, ?, ?, ?)', test_data)
        
        conn.commit()
        conn.close()

@app.route('/api/query', methods=['POST'])
def query_verification_code():
    """查询邮箱验证码"""
    data = request.json
    email = data.get('email')
    
    if not email:
        return jsonify({'success': False, 'message': '请提供邮箱地址'}), 400
    
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # 查询最近的验证码
    cursor.execute('''
    SELECT * FROM verification_codes 
    WHERE email = ? 
    ORDER BY created_at DESC 
    LIMIT 1
    ''', (email,))
    
    result = cursor.fetchone()
    conn.close()
    
    if result:
        # 转换为字典
        code_data = dict(result)
        # 格式化时间
        code_data['created_at'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(code_data['created_at']))
        return jsonify({
            'success': True, 
            'data': code_data
        })
    else:
        return jsonify({
            'success': False, 
            'message': '未找到包含验证码的邮件，请确认邮箱是否填写正确或者重试！'
        })

@app.route('/api/add', methods=['POST'])
def add_verification_code():
    """添加新的验证码（仅用于测试）"""
    data = request.json
    email = data.get('email')
    code = data.get('code')
    service = data.get('service', '未知服务')
    
    if not email or not code:
        return jsonify({'success': False, 'message': '邮箱和验证码不能为空'}), 400
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
    INSERT INTO verification_codes (email, code, service, created_at, used)
    VALUES (?, ?, ?, ?, ?)
    ''', (email, code, service, int(time.time()), 0))
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'message': '验证码添加成功'})

if __name__ == '__main__':
    init_db()  # 初始化数据库
    app.run(debug=True, host='0.0.0.0', port=5000) 