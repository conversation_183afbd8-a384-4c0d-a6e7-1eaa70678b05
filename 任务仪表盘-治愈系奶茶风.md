---
tags:
  - type/dashboard
  - dashboard/task
  - theme/healing
created: 2025-05-25T10:30
updated: 2025-05-25T10:30
---

# 🌸 任务仪表盘 - 治愈系奶茶风

> ✨ 温暖治愈的任务管理系统，让规划变得美好

## 🍃 任务看板

```dataviewjs
// 获取所有任务
const allTasks = dv.pages().file.tasks;
const today = new Date().toISOString().split('T')[0];
const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);
const tomorrowStr = tomorrow.toISOString().split('T')[0];

// 计算本周范围（中国标准：周一开始，周日结束）
const now = new Date();
const dayOfWeek = now.getDay();
const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
const startOfWeek = new Date(now);
startOfWeek.setDate(now.getDate() - (adjustedDayOfWeek - 1));
const endOfWeek = new Date(startOfWeek);
endOfWeek.setDate(startOfWeek.getDate() + 6);
const startDate = startOfWeek.toISOString().split('T')[0];
const endDate = endOfWeek.toISOString().split('T')[0];

// 统计各类任务
let stats = {
    overdue: 0,      // 逾期
    inProgress: 0,   // 进行中
    waiting: 0,      // 待办
    today: 0,        // 今天
    tomorrow: 0,     // 明天
    thisWeek: 0,     // 一周内
    future: 0,       // 未来
    completed: 0     // 已完成
};

allTasks.forEach(task => {
    if (task.completed) {
        stats.completed++;
        return;
    }

    const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');
    if (isInProgress) {
        stats.inProgress++;
        return;
    }

    // 支持多种日期格式：📅 2025-05-25, due: 2025-05-25, [due:: 2025-05-25]
    const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
    if (!dueDateMatch) {
        stats.waiting++;
        return;
    }

    const dueDate = dueDateMatch[1];

    if (dueDate < today && !isInProgress) {
        stats.overdue++;
    } else if (dueDate === today && !isInProgress) {
        stats.today++;
    } else if (dueDate === tomorrowStr && !isInProgress) {
        stats.tomorrow++;
    } else if (dueDate >= startDate && dueDate <= endDate && dueDate !== today && dueDate !== tomorrowStr && !isInProgress) {
        stats.thisWeek++;
    } else if (!isInProgress) {
        stats.future++;
    }
});

// 创建治愈系看板
const container = this.container;
container.innerHTML = '';

// 添加温馨标题
const title = document.createElement('div');
title.style.cssText = `
    text-align: center;
    font-size: 1.4em;
    font-weight: 600;
    margin-bottom: 25px;
    color: #8B7355;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
`;
title.innerHTML = '🌸 今日任务清单 🌸';
container.appendChild(title);

// 创建卡片容器 - 奶茶色渐变背景
const cardContainer = document.createElement('div');
cardContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
    gap: 12px;
    margin-bottom: 25px;
    padding: 25px;
    background: linear-gradient(135deg, #F7F3F0 0%, #FFF9F5 50%, #F5F1EC 100%);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1);
    border: 1px solid rgba(232, 180, 160, 0.2);
`;

// 定义治愈系卡片数据
const cards = [
    { label: '逾期', count: stats.overdue, color: '#E8A5A5', bgColor: '#FFF5F5', icon: '🌺' },
    { label: '进行中', count: stats.inProgress, color: '#A5D6E8', bgColor: '#F0F8FF', icon: '🌿' },
    { label: '待办', count: stats.waiting, color: '#E8D5A5', bgColor: '#FFFBF0', icon: '🌼' },
    { label: '今天', count: stats.today, color: '#D4A574', bgColor: '#FFF8F0', icon: '☀️' },
    { label: '明天', count: stats.tomorrow, color: '#E8B4CB', bgColor: '#FFF5F8', icon: '🌙' },
    { label: '本周其他', count: stats.thisWeek, color: '#B8D6B8', bgColor: '#F8FFF8', icon: '🍃' },
    { label: '未来', count: stats.future, color: '#D0C4E8', bgColor: '#F8F5FF', icon: '✨' },
    { label: '完成', count: stats.completed, color: '#A8C8A8', bgColor: '#F5FFF5', icon: '🌸' }
];

// 创建治愈系卡片
cards.forEach((card, index) => {
    const cardElement = document.createElement('div');
    cardElement.style.cssText = `
        background: ${card.bgColor};
        border-radius: 16px;
        padding: 18px 12px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(139, 115, 85, 0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    `;

    // 添加微妙的光泽效果
    cardElement.innerHTML = `
        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%;
                    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
                    transform: rotate(45deg); pointer-events: none;"></div>
        <div style="position: relative; z-index: 1;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">
                ${card.icon}
            </div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${card.color}; margin-bottom: 6px;
                        text-shadow: 0 1px 2px rgba(139, 115, 85, 0.1);">
                ${card.count}
            </div>
            <div style="font-size: 0.85em; color: #8B7355; font-weight: 500;
                        letter-spacing: 0.5px;">
                ${card.label}
            </div>
        </div>
    `;

    // 治愈系悬停效果
    cardElement.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-8px) scale(1.05)';
        this.style.boxShadow = '0 12px 40px rgba(139, 115, 85, 0.15)';
        this.style.borderColor = card.color;
        this.style.background = `linear-gradient(135deg, ${card.bgColor} 0%, #FFFFFF 100%)`;
    });

    cardElement.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 4px 20px rgba(139, 115, 85, 0.08)';
        this.style.borderColor = 'transparent';
        this.style.background = card.bgColor;
    });

    // 添加点击事件 - 修复作用域问题
    cardElement.addEventListener('click', function() {
        showTasksByCategory(card.label, card.color, allTasks);
    });

    cardContainer.appendChild(cardElement);
});

container.appendChild(cardContainer);

// 添加温馨总计信息
const totalTasks = stats.overdue + stats.inProgress + stats.waiting + stats.today +
                  stats.tomorrow + stats.thisWeek + stats.future;
const totalInfo = document.createElement('div');
totalInfo.style.cssText = `
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%);
    border-radius: 16px;
    margin-top: 15px;
    border: 2px solid rgba(232, 180, 160, 0.3);
    box-shadow: 0 4px 20px rgba(139, 115, 85, 0.08);
`;
totalInfo.innerHTML = `
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600;">
        <span style="font-size: 1.2em;">🌸</span>
        <strong style="color: #D4A574; margin: 0 8px;">总计: ${totalTasks + stats.completed} 个任务</strong>
        <span style="font-size: 1.2em;">🌸</span>
    </div>
    <div style="margin-top: 8px; font-size: 0.9em; color: #A0896B;">
        <span style="background: rgba(232, 180, 160, 0.2); padding: 4px 12px; border-radius: 12px; margin: 0 5px;">
            未完成: ${totalTasks}
        </span>
        <span style="background: rgba(168, 200, 168, 0.2); padding: 4px 12px; border-radius: 12px; margin: 0 5px;">
            已完成: ${stats.completed}
        </span>
    </div>
`;
container.appendChild(totalInfo);

// 创建任务详情显示区域
const taskDetailContainer = document.createElement('div');
taskDetailContainer.id = 'task-detail-container';
taskDetailContainer.style.cssText = `
    margin-top: 25px;
    display: none;
`;
container.appendChild(taskDetailContainer);

// 显示指定类别的任务函数 - 完整功能版本
function showTasksByCategory(category, color, allTasks) {
    const detailContainer = document.getElementById('task-detail-container');
    let filteredTasks = [];

    // 重新定义时间变量
    const today = new Date().toISOString().split('T')[0];
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    // 计算本周范围
    const now = new Date();
    const dayOfWeek = now.getDay();
    const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - (adjustedDayOfWeek - 1));
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    const startDate = startOfWeek.toISOString().split('T')[0];
    const endDate = endOfWeek.toISOString().split('T')[0];

    // 根据类别筛选任务
    switch(category) {
        case '逾期':
            filteredTasks = allTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                if (!dueDateMatch) return false;
                const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');
                return dueDateMatch[1] < today && !isInProgress;
            });
            break;
        case '进行中':
            filteredTasks = allTasks.filter(task =>
                !task.completed && (task.text.includes('🔄') || task.text.includes('进行中'))
            );
            break;
        case '待办':
            filteredTasks = allTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');
                return !dueDateMatch && !isInProgress;
            });
            break;
        case '今天':
            filteredTasks = allTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                return dueDateMatch && dueDateMatch[1] === today;
            });
            break;
        case '明天':
            filteredTasks = allTasks.filter(task => {
                if (task.completed) return false;
                const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                return dueDateMatch && dueDateMatch[1] === tomorrowStr && !isInProgress;
            });
            break;
        case '本周其他':
            filteredTasks = allTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                if (!dueDateMatch) return false;
                const dueDate = dueDateMatch[1];
                const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');
                return dueDate >= startDate && dueDate <= endDate && dueDate !== today && dueDate !== tomorrowStr && !isInProgress;
            });
            break;
        case '未来':
            filteredTasks = allTasks.filter(task => {
                if (task.completed) return false;
                const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
                if (!dueDateMatch) return false;
                const dueDate = dueDateMatch[1];
                const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');
                return dueDate > endDate && dueDate !== today && dueDate !== tomorrowStr && !isInProgress;
            });
            break;
        case '完成':
            filteredTasks = allTasks.filter(task => task.completed);
            break;
    }

    // 生成治愈系任务列表
    if (filteredTasks.length > 0) {
        let html = `
            <div style="background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%);
                        border: 2px solid ${color}40; border-radius: 20px; padding: 25px; margin-top: 20px;
                        box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="margin: 0; color: ${color}; font-weight: 600; font-size: 1.2em;">
                        🌸 ${category} (${filteredTasks.length} 个)
                    </h3>
                    <button onclick="document.getElementById('task-detail-container').style.display='none'"
                            style="background: ${color}; color: white; border: none; border-radius: 12px;
                                   padding: 8px 16px; cursor: pointer; font-weight: 500;
                                   box-shadow: 0 4px 12px ${color}40;">
                        ✕ 关闭
                    </button>
                </div>
                <div style="max-height: 400px; overflow-y: auto; padding-right: 5px;">
        `;

        filteredTasks.forEach(task => {
            const cleanText = task.text
                .replace(/🍅\d+/g, '')
                .replace(/⏫|🔼|🔽/g, '')
                .replace(/(?:📅\s*|due:\s*|\[due::\s*)\d{4}-\d{2}-\d{2}(?:\])?/g, '')
                .replace(/#project\/[^\s#]+/g, '')
                .replace(/#重要|#不重要/g, '')
                .replace(/🔄/g, '')
                .trim();

            const priority = task.text.includes('⏫') ? '⏫' :
                           task.text.includes('🔼') ? '🔼' :
                           task.text.includes('🔽') ? '🔽' : '';

            const tomatoMatch = task.text.match(/🍅(\d+)/);
            const tomato = tomatoMatch ? `🍅${tomatoMatch[1]}` : '';

            const dueDateMatch = task.text.match(/(?:📅\s*|due:\s*|\[due::\s*)(\d{4}-\d{2}-\d{2})/);
            const dueDate = dueDateMatch ? `📅 ${dueDateMatch[1]}` : '';

            const important = task.text.includes('#重要') ? '⭐' : '';

            const taskId = `task-${Math.random().toString(36).substr(2, 9)}`;

            html += `
                <div id="${taskId}" style="background: rgba(255,255,255,0.8); border-radius: 16px; padding: 18px; margin-bottom: 12px;
                                           box-shadow: 0 4px 16px rgba(139, 115, 85, 0.08); transition: all 0.3s ease;
                                           border: 1px solid rgba(232, 180, 160, 0.2);">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1; cursor: pointer;" class="task-content" data-path="${task.path}" data-text="${cleanText}">
                            <div style="font-weight: 500; margin-bottom: 8px; color: #8B7355; font-size: 1.05em;">
                                ${task.completed ? '✅' : '🌸'} ${cleanText}
                            </div>
                            <div style="font-size: 0.85em; color: #A0896B;">
                                <span style="background: rgba(232, 180, 160, 0.2); padding: 3px 8px; border-radius: 8px; margin-right: 6px;">
                                    📁 ${task.path}
                                </span>
                                <span style="color: #D4A574; font-size: 0.8em;">
                                    👆 点击跳转
                                </span>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 6px;">
                            <button class="jump-btn" data-path="${task.path}" data-text="${cleanText}"
                                    style="background: linear-gradient(135deg, #E8B4A0 0%, #D4A574 100%); color: white;
                                           border: none; border-radius: 10px; padding: 6px 12px; cursor: pointer;
                                           font-size: 0.8em; font-weight: 500; box-shadow: 0 3px 8px rgba(212, 165, 116, 0.3);">
                                📍 跳转
                            </button>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px; justify-content: flex-end;">
                                ${priority ? `<span style="font-size: 1.1em;">${priority}</span>` : ''}
                                ${important ? `<span style="font-size: 1.1em;">${important}</span>` : ''}
                                ${tomato ? `<span style="background: rgba(255, 248, 240, 0.8); color: #D4A574; padding: 2px 8px; border-radius: 8px; font-size: 0.85em;">${tomato}</span>` : ''}
                                ${dueDate ? `<span style="background: rgba(240, 248, 255, 0.8); color: #A5D6E8; padding: 2px 8px; border-radius: 8px; font-size: 0.85em;">${dueDate}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `</div></div>`;
        detailContainer.innerHTML = html;
        detailContainer.style.display = 'block';

        // 添加事件监听
        setTimeout(() => {
            const jumpBtns = detailContainer.querySelectorAll('.jump-btn');
            jumpBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const path = this.getAttribute('data-path');
                    const text = this.getAttribute('data-text');
                    openTaskLocation(path, text);
                });
            });

            const taskContents = detailContainer.querySelectorAll('.task-content');
            taskContents.forEach(content => {
                content.addEventListener('click', function() {
                    const path = this.getAttribute('data-path');
                    const text = this.getAttribute('data-text');
                    openTaskLocation(path, text);
                });
            });
        }, 100);

        detailContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
        detailContainer.innerHTML = `
            <div style="background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%);
                        border: 2px solid ${color}40; border-radius: 20px; padding: 25px; margin-top: 20px;
                        box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1); text-align: center;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="margin: 0; color: ${color}; font-weight: 600; font-size: 1.2em;">
                        🌸 ${category} (0 个)
                    </h3>
                    <button onclick="document.getElementById('task-detail-container').style.display='none'"
                            style="background: ${color}; color: white; border: none; border-radius: 12px;
                                   padding: 8px 16px; cursor: pointer; font-weight: 500;
                                   box-shadow: 0 4px 12px ${color}40;">
                        ✕ 关闭
                    </button>
                </div>
                <div style="padding: 40px; color: #A0896B;">
                    <div style="font-size: 3em; margin-bottom: 15px;">🌸</div>
                    <p style="font-size: 1.1em;">暂无 ${category} 任务</p>
                    <p style="font-size: 0.9em; opacity: 0.7;">享受这份宁静吧～</p>
                </div>
            </div>
        `;
        detailContainer.style.display = 'block';
    }
}

// 全局函数
function openTaskLocation(filePath, taskText) {
    try {
        if (app && app.workspace) {
            app.workspace.openLinkText(filePath, '', false);
        } else {
            alert(`请手动打开文件: ${filePath}`);
        }
    } catch (error) {
        alert(`无法打开文件: ${filePath}`);
    }
}
```

## 🍯 番茄钟看板

```dataviewjs
// 数据处理辅助函数
function parseNumber(value) {
    if (value === null || value === undefined || value === '') return 0;
    if (typeof value === 'string') {
        const parsed = parseInt(value.replace(/[^\d]/g, ''));
        return isNaN(parsed) ? 0 : parsed;
    }
    return typeof value === 'number' ? value : 0;
}

// 获取今日番茄钟数据
const today = new Date().toISOString().split('T')[0];
const todayDailyLog = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.includes(today))
    .first();

let dailyGoal = 0;
let dailyActual = 0;
if (todayDailyLog) {
    dailyGoal = parseNumber(todayDailyLog.tomato_goal);
    dailyActual = parseNumber(todayDailyLog.tomato_actual);
}

// 获取昨日数据
const yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);
const yesterdayStr = yesterday.toISOString().split('T')[0];
const yesterdayDailyLog = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.includes(yesterdayStr))
    .first();

let yesterdayActual = 0;
let yesterdayGoal = 0;
if (yesterdayDailyLog) {
    yesterdayActual = parseNumber(yesterdayDailyLog.tomato_actual);
    yesterdayGoal = parseNumber(yesterdayDailyLog.tomato_goal);
}

// 获取本周数据
const now = new Date();
const dayOfWeek = now.getDay();
const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
const startOfWeek = new Date(now);
startOfWeek.setDate(now.getDate() - (adjustedDayOfWeek - 1));

let weeklyGoal = 0;
let weeklyActual = 0;
let weeklyDays = 0;

for (let i = 0; i < adjustedDayOfWeek; i++) {
    const checkDate = new Date(startOfWeek);
    checkDate.setDate(startOfWeek.getDate() + i);
    const checkDateStr = checkDate.toISOString().split('T')[0];

    const dayLog = dv.pages('"0_Bullet Journal/Daily Notes"')
        .where(p => p.file.name.includes(checkDateStr))
        .first();

    if (dayLog) {
        const dayGoal = parseNumber(dayLog.tomato_goal);
        const dayActual = parseNumber(dayLog.tomato_actual);
        weeklyGoal += dayGoal;
        weeklyActual += dayActual;
        if (dayGoal > 0) weeklyDays++;
    }
}

// 统计任务中的番茄钟（作为备用数据）
const allTasks = dv.pages().file.tasks;
let totalPlanned = 0;
let totalCompleted = 0;
let todayPlanned = 0;
let todayCompleted = 0;

allTasks.forEach(task => {
    const tomatoMatch = task.text.match(/🍅(\d+)/);
    if (tomatoMatch) {
        const tomatoCount = parseInt(tomatoMatch[1]);
        totalPlanned += tomatoCount;

        if (task.completed) {
            totalCompleted += tomatoCount;
        }

        if (task.text.includes(`📅 ${today}`)) {
            todayPlanned += tomatoCount;
            if (task.completed) {
                todayCompleted += tomatoCount;
            }
        }
    }
});

// 优先使用Daily Log记录的实际数据
const actualTodayCompleted = dailyActual > 0 ? dailyActual : todayCompleted;

// 创建治愈系番茄钟看板
const container = this.container;
container.innerHTML = '';

// 温馨标题
const title = document.createElement('div');
title.style.cssText = `
    text-align: center;
    font-size: 1.3em;
    font-weight: 600;
    margin-bottom: 20px;
    color: #8B7355;
`;
title.innerHTML = '🍯 今日番茄钟记录 🍯';
container.appendChild(title);

// 创建番茄钟卡片容器
const tomatoContainer = document.createElement('div');
tomatoContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
    padding: 25px;
    background: linear-gradient(135deg, #FFF8F0 0%, #FFF5E6 50%, #FFF2DC 100%);
    border-radius: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(212, 165, 116, 0.1);
    border: 1px solid rgba(232, 180, 160, 0.2);
`;

// 治愈系番茄钟数据 - 完整版本
const tomatoData = [
    {
        label: '今日目标',
        count: dailyGoal,
        icon: '🎯',
        color: '#E8B4A0',
        bgColor: '#FFF8F5',
        subtitle: dailyGoal > 0 ? '已设定' : '未设定'
    },
    {
        label: '今日完成',
        count: actualTodayCompleted,
        icon: '🍯',
        color: '#D4A574',
        bgColor: '#FFFBF0',
        subtitle: dailyActual > 0 ? '已记录' : '实时统计'
    },
    {
        label: '昨日完成',
        count: yesterdayActual,
        icon: '🌙',
        color: '#C8A8E8',
        bgColor: '#F8F5FF',
        subtitle: `目标${yesterdayGoal}`
    },
    {
        label: '本周累计',
        count: weeklyActual,
        icon: '📈',
        color: '#B8D6B8',
        bgColor: '#F8FFF8',
        subtitle: `${weeklyDays}天记录`
    }
];

// 创建治愈系番茄钟卡片
tomatoData.forEach(item => {
    const card = document.createElement('div');
    card.style.cssText = `
        background: ${item.bgColor};
        border-radius: 18px;
        padding: 22px;
        text-align: center;
        box-shadow: 0 6px 24px rgba(139, 115, 85, 0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    `;

    card.innerHTML = `
        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%;
                    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.15) 50%, transparent 70%);
                    transform: rotate(45deg); pointer-events: none;"></div>
        <div style="position: relative; z-index: 1;">
            <div style="font-size: 2.2em; margin-bottom: 12px;">
                ${item.icon}
            </div>
            <div style="font-size: 2.2em; font-weight: 700; color: ${item.color}; margin-bottom: 8px;
                        text-shadow: 0 2px 4px rgba(139, 115, 85, 0.1);">
                ${item.count}${item.subtitle === '%' ? '%' : ''}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500; margin-bottom: 4px;">
                ${item.label}
            </div>
            <div style="font-size: 0.75em; color: #A0896B; opacity: 0.8;">
                ${item.subtitle !== '%' ? item.subtitle : ''}
            </div>
        </div>
    `;

    // 治愈系悬停效果
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px) scale(1.05)';
        this.style.boxShadow = '0 12px 40px rgba(139, 115, 85, 0.15)';
        this.style.borderColor = item.color;
        this.style.background = `linear-gradient(135deg, ${item.bgColor} 0%, #FFFFFF 100%)`;
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 6px 24px rgba(139, 115, 85, 0.08)';
        this.style.borderColor = 'transparent';
        this.style.background = item.bgColor;
    });

    tomatoContainer.appendChild(card);
});

container.appendChild(tomatoContainer);

// 添加完成率对比卡片
const todayRate = dailyGoal > 0 ? Math.round((actualTodayCompleted / dailyGoal) * 100) : 0;
const weeklyRate = weeklyGoal > 0 ? Math.round((weeklyActual / weeklyGoal) * 100) : 0;
const yesterdayRate = yesterdayGoal > 0 ? Math.round((yesterdayActual / yesterdayGoal) * 100) : 0;

const rateContainer = document.createElement('div');
rateContainer.style.cssText = `
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    margin-top: 20px;
    margin-bottom: 20px;
`;

// 今日达成率
const todayCard = document.createElement('div');
todayCard.style.cssText = `
    background: linear-gradient(135deg, #FFF8F0 0%, #FFECD2 100%);
    color: #8B7355;
    padding: 20px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 6px 24px rgba(139, 115, 85, 0.08);
    border: 2px solid rgba(232, 180, 160, 0.2);
`;

const todayRateDetail = dailyGoal > 0 ?
    `${actualTodayCompleted}/${dailyGoal} 🍯` :
    '未设定目标';

todayCard.innerHTML = `
    <div style="font-size: 1.8em; font-weight: bold; margin-bottom: 5px; color: #D4A574;">
        ${todayRate}%
    </div>
    <div style="font-size: 1em; opacity: 0.8; margin-bottom: 5px;">
        今日达成率
    </div>
    <div style="font-size: 0.8em; opacity: 0.6;">
        ${todayRateDetail}
    </div>
`;

// 昨日对比
const yesterdayCard = document.createElement('div');
yesterdayCard.style.cssText = `
    background: linear-gradient(135deg, #F8F5FF 0%, #F0E6FF 100%);
    color: #8B7355;
    padding: 20px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 6px 24px rgba(139, 115, 85, 0.08);
    border: 2px solid rgba(200, 168, 232, 0.2);
`;

const compareIcon = actualTodayCompleted >= yesterdayActual ? '📈' : '📉';
const compareText = actualTodayCompleted >= yesterdayActual ? '超越昨日' : '低于昨日';

yesterdayCard.innerHTML = `
    <div style="font-size: 1.8em; font-weight: bold; margin-bottom: 5px; color: #C8A8E8;">
        ${compareIcon}
    </div>
    <div style="font-size: 1em; opacity: 0.8; margin-bottom: 5px;">
        ${compareText}
    </div>
    <div style="font-size: 0.8em; opacity: 0.6;">
        昨日${yesterdayActual}🍯
    </div>
`;

// 本周进度
const weeklyCard = document.createElement('div');
weeklyCard.style.cssText = `
    background: linear-gradient(135deg, #F8FFF8 0%, #F0FFF0 100%);
    color: #8B7355;
    padding: 20px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 6px 24px rgba(139, 115, 85, 0.08);
    border: 2px solid rgba(184, 214, 184, 0.2);
`;

weeklyCard.innerHTML = `
    <div style="font-size: 1.8em; font-weight: bold; margin-bottom: 5px; color: #B8D6B8;">
        ${weeklyRate}%
    </div>
    <div style="font-size: 1em; opacity: 0.8; margin-bottom: 5px;">
        本周进度
    </div>
    <div style="font-size: 0.8em; opacity: 0.6;">
        ${weeklyActual}/${weeklyGoal} 🍯
    </div>
`;

rateContainer.appendChild(todayCard);
rateContainer.appendChild(yesterdayCard);
rateContainer.appendChild(weeklyCard);
container.appendChild(rateContainer);

// 温馨提示卡片
const tipCard = document.createElement('div');
tipCard.style.cssText = `
    background: linear-gradient(135deg, #F5FFF5 0%, #F0FFF0 100%);
    border-radius: 16px;
    padding: 20px;
    text-align: center;
    border: 2px solid rgba(168, 200, 168, 0.3);
    box-shadow: 0 4px 20px rgba(139, 115, 85, 0.08);
`;

const encouragement = actualTodayCompleted >= dailyGoal && dailyGoal > 0 ?
    "🎉 太棒了！今日目标已达成！" :
    dailyGoal > 0 ?
    `🌸 加油！还差 ${Math.max(0, dailyGoal - actualTodayCompleted)} 个番茄钟就达成目标了！` :
    "💕 记得设定今日番茄钟目标哦～";

tipCard.innerHTML = `
    <div style="font-size: 1.1em; color: #6B8E6B; font-weight: 500;">
        ${encouragement}
    </div>
`;

container.appendChild(tipCard);
```

## 🌺 快速筛选

```dataviewjs
// 创建治愈系快速筛选按钮
const container = this.container;
container.innerHTML = '';

// 筛选按钮容器
const filterContainer = document.createElement('div');
filterContainer.style.cssText = `
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding: 20px;
    background: linear-gradient(135deg, #F8F5FF 0%, #F5F2FF 100%);
    border-radius: 18px;
    margin-bottom: 20px;
    box-shadow: 0 6px 24px rgba(139, 115, 85, 0.08);
    border: 1px solid rgba(200, 168, 232, 0.2);
`;

// 治愈系筛选按钮数据
const filters = [
    { label: '🌸 重要任务', color: '#E8B4CB', bgColor: '#FFF5F8' },
    { label: '⭐ 高优先级', color: '#E8D5A5', bgColor: '#FFFBF0' },
    { label: '🍯 大任务(3+)', color: '#D4A574', bgColor: '#FFF8F0' },
    { label: '📝 无日期', color: '#A5D6E8', bgColor: '#F0F8FF' }
];

filters.forEach(filter => {
    const btn = document.createElement('button');
    btn.style.cssText = `
        background: ${filter.bgColor};
        color: ${filter.color};
        border: 2px solid ${filter.color}40;
        border-radius: 14px;
        padding: 10px 18px;
        cursor: pointer;
        font-weight: 500;
        font-size: 0.9em;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 12px ${filter.color}20;
    `;

    btn.textContent = filter.label;

    // 添加点击事件
    btn.addEventListener('click', function() {
        filterTasks(filter.label, filter.color);
    });

    btn.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.05)';
        this.style.boxShadow = `0 8px 20px ${filter.color}30`;
        this.style.background = filter.color;
        this.style.color = 'white';
    });

    btn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = `0 4px 12px ${filter.color}20`;
        this.style.background = filter.bgColor;
        this.style.color = filter.color;
    });

    filterContainer.appendChild(btn);
});

container.appendChild(filterContainer);

// 筛选结果显示区域
const resultDiv = document.createElement('div');
resultDiv.id = 'filter-results';
container.appendChild(resultDiv);

// 跳转函数 - 在快速筛选中也需要使用
function openTaskLocation(filePath, taskText) {
    try {
        if (app && app.workspace) {
            app.workspace.openLinkText(filePath, '', false);
        } else {
            alert(`请手动打开文件: ${filePath}`);
        }
    } catch (error) {
        alert(`无法打开文件: ${filePath}`);
    }
}

// 筛选功能函数
function filterTasks(filterType, color) {
    const allTasks = dv.pages().file.tasks;
    let filteredTasks = [];
    let title = '';

    switch(filterType) {
        case '🌸 重要任务':
            filteredTasks = allTasks.filter(task => !task.completed && task.text.includes('#重要'));
            title = '重要任务';
            break;
        case '⭐ 高优先级':
            filteredTasks = allTasks.filter(task => !task.completed && task.text.includes('⏫'));
            title = '高优先级任务';
            break;
        case '🍯 大任务(3+)':
            filteredTasks = allTasks.filter(task => {
                if (task.completed) return false;
                const tomatoMatch = task.text.match(/🍅(\d+)/);
                return tomatoMatch && parseInt(tomatoMatch[1]) >= 3;
            });
            title = '大任务(3+番茄钟)';
            break;
        case '📝 无日期':
            filteredTasks = allTasks.filter(task => {
                if (task.completed) return false;
                const isInProgress = task.text.includes('🔄') || task.text.includes('进行中');
                return !task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/) && !isInProgress;
            });
            title = '无日期任务';
            break;
    }

    const resultDiv = document.getElementById('filter-results');
    if (filteredTasks.length > 0) {
        let html = `
            <div style="background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%); border-radius: 20px; padding: 25px;
                        box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1); margin-top: 20px; border: 2px solid ${color}40;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h4 style="margin: 0; color: ${color}; font-weight: 600; font-size: 1.2em;">🌸 ${title} (${filteredTasks.length} 个)</h4>
                    <button onclick="document.getElementById('filter-results').innerHTML=''"
                            style="background: ${color}; color: white; border: none; border-radius: 12px; padding: 8px 16px;
                                   cursor: pointer; font-weight: 500; box-shadow: 0 4px 12px ${color}40;">
                        ✕ 关闭
                    </button>
                </div>
                <div style="max-height: 400px; overflow-y: auto; padding-right: 5px; scrollbar-width: thin; scrollbar-color: #ccc #f1f1f1;">
                <style>
                    .filter-scroll-container::-webkit-scrollbar {
                        width: 8px;
                    }
                    .filter-scroll-container::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 4px;
                    }
                    .filter-scroll-container::-webkit-scrollbar-thumb {
                        background: #ccc;
                        border-radius: 4px;
                    }
                    .filter-scroll-container::-webkit-scrollbar-thumb:hover {
                        background: #999;
                    }
                </style>
                <div class="filter-scroll-container" style="max-height: 400px; overflow-y: auto; padding-right: 5px;">
        `;

        filteredTasks.forEach(task => {
            const cleanText = task.text.replace(/🍅\d+|⏫|🔼|🔽|📅\s*\d{4}-\d{2}-\d{2}|#\w+/g, '').trim();
            const priority = task.text.includes('⏫') ? '⏫' : task.text.includes('🔼') ? '🔼' : task.text.includes('🔽') ? '🔽' : '';
            const tomatoMatch = task.text.match(/🍅(\d+)/);
            const tomato = tomatoMatch ? `🍅${tomatoMatch[1]}` : '';
            const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
            const dueDate = dueDateMatch ? `📅 ${dueDateMatch[1]}` : '';
            const important = task.text.includes('#重要') ? '⭐' : '';

            html += `
                <div style="background: rgba(255,255,255,0.8); border-radius: 16px; padding: 18px; margin-bottom: 12px;
                           box-shadow: 0 4px 16px rgba(139, 115, 85, 0.08); transition: all 0.3s ease;
                           border: 1px solid rgba(232, 180, 160, 0.2);">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1; cursor: pointer;" class="task-content" data-path="${task.path}" data-text="${cleanText}">
                            <div style="font-weight: 500; margin-bottom: 8px; color: #8B7355; font-size: 1.05em;">
                                🌸 ${cleanText}
                            </div>
                            <div style="font-size: 0.85em; color: #A0896B;">
                                <span style="background: rgba(232, 180, 160, 0.2); padding: 3px 8px; border-radius: 8px; margin-right: 6px;">
                                    📁 ${task.path}
                                </span>
                                <span style="color: #D4A574; font-size: 0.8em;">👆 点击跳转</span>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 6px;">
                            <button class="jump-btn" data-path="${task.path}" data-text="${cleanText}"
                                    style="background: linear-gradient(135deg, #E8B4A0 0%, #D4A574 100%); color: white;
                                           border: none; border-radius: 10px; padding: 6px 12px; cursor: pointer;
                                           font-size: 0.8em; font-weight: 500; box-shadow: 0 3px 8px rgba(212, 165, 116, 0.3);">
                                📍 跳转
                            </button>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px; justify-content: flex-end;">
                                ${priority ? `<span style="font-size: 1.1em;">${priority}</span>` : ''}
                                ${important ? `<span style="font-size: 1.1em;">${important}</span>` : ''}
                                ${tomato ? `<span style="background: rgba(255, 248, 240, 0.8); color: #D4A574; padding: 2px 8px; border-radius: 8px; font-size: 0.85em;">${tomato}</span>` : ''}
                                ${dueDate ? `<span style="background: rgba(240, 248, 255, 0.8); color: #A5D6E8; padding: 2px 8px; border-radius: 8px; font-size: 0.85em;">${dueDate}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `</div></div></div>`;
        resultDiv.innerHTML = html;

        // 添加事件监听
        setTimeout(() => {
            const jumpBtns = resultDiv.querySelectorAll('.jump-btn');
            jumpBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const path = this.getAttribute('data-path');
                    const text = this.getAttribute('data-text');
                    openTaskLocation(path, text);
                });
            });

            const taskContents = resultDiv.querySelectorAll('.task-content');
            taskContents.forEach(content => {
                content.addEventListener('click', function() {
                    const path = this.getAttribute('data-path');
                    const text = this.getAttribute('data-text');
                    openTaskLocation(path, text);
                });
            });
        }, 100);
    } else {
        resultDiv.innerHTML = `
            <div style="background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%); border-radius: 20px; padding: 25px;
                        box-shadow: 0 8px 32px rgba(139, 115, 85, 0.1); margin-top: 20px; border: 2px solid ${color}40; text-align: center;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h4 style="margin: 0; color: ${color}; font-weight: 600; font-size: 1.2em;">🌸 ${title} (0 个)</h4>
                    <button onclick="document.getElementById('filter-results').innerHTML=''"
                            style="background: ${color}; color: white; border: none; border-radius: 12px; padding: 8px 16px;
                                   cursor: pointer; font-weight: 500; box-shadow: 0 4px 12px ${color}40;">
                        ✕ 关闭
                    </button>
                </div>
                <div style="padding: 40px; color: #A0896B;">
                    <div style="font-size: 3em; margin-bottom: 15px;">🌸</div>
                    <p style="font-size: 1.1em;">未找到 ${title}</p>
                    <p style="font-size: 0.9em; opacity: 0.7;">享受这份宁静吧～</p>
                </div>
            </div>
        `;
    }
}
```
