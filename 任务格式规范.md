---
tags:
  - type/guide
  - guide/task
created: 2025-05-16T16:30
updated: 2025-05-16T16:30
---

# 📋 任务格式规范指南

> 本文档详细说明了任务仪表盘系统中的任务格式规范，帮助您标准化任务管理。

## 🎯 核心格式

### 主任务格式
```markdown
- [ ] 主任务名称 📅 YYYY-MM-DD 🍅x ⏫/🔼/🔽 #project/项目名 #重要/不重要
```

### 子任务格式
```markdown
- [ ] @主任务名称 子任务描述 📅 YYYY-MM-DD 🍅x ⏫/🔼/🔽 #project/项目名
```

## 📝 元素详解

| 元素 | 说明 | 示例 | 必需性 | 备注 |
|------|------|------|--------|------|
| `- [ ]` | 任务标记 | `- [ ]` | ✅ 必需 | Obsidian标准任务格式 |
| 任务名称 | 任务描述 | `完成年度报告` | ✅ 必需 | 简洁明确的任务描述 |
| `📅` | 截止日期 | `📅 2025-05-20` | 🔶 推荐 | 用于时间维度汇总 |
| `🍅x` | 番茄钟数量 | `🍅3` | 🔶 推荐 | x代表需要的番茄钟数量 |
| `⏫🔼🔽` | 优先级 | `⏫` 最高, `🔼` 高, `🔽` 低 | 🔶 可选 | 用于优先级排序 |
| `#project/` | 项目标签 | `#project/工作` | 🔶 推荐 | 用于项目分组 |
| `#重要/#不重要` | 重要性标签 | `#重要` | 🔶 可选 | 用于重要性筛选 |
| `@主任务名` | 子任务关联 | `@完成年度报告` | 🔶 子任务必需 | 建立主子任务关系 |

## 🌟 完整示例

### 工作项目示例
```markdown
# 年度报告项目
- [ ] 完成年度报告 📅 2025-05-20 🍅5 ⏫ #project/工作 #重要
- [ ] @完成年度报告 收集Q1数据 📅 2025-05-15 🍅2 🔼 #project/工作
- [ ] @完成年度报告 分析数据趋势 📅 2025-05-17 🍅2 🔼 #project/工作
- [ ] @完成年度报告 撰写报告初稿 📅 2025-05-19 🍅1 🔽 #project/工作
- [ ] @完成年度报告 审核和修改 📅 2025-05-20 🍅1 🔼 #project/工作
```

### 学习项目示例
```markdown
# Python学习计划
- [ ] 学习Python编程 📅 2025-06-01 🍅10 🔼 #project/学习 #重要
- [ ] @学习Python编程 完成基础语法学习 📅 2025-05-25 🍅3 ⏫ #project/学习
- [ ] @学习Python编程 练习编程题目 📅 2025-05-30 🍅4 🔼 #project/学习
- [ ] @学习Python编程 完成项目实战 📅 2025-06-01 🍅3 🔼 #project/学习
```

### 生活任务示例
```markdown
# 日常生活
- [ ] 整理房间 📅 2025-05-18 🍅2 🔽 #project/生活 #不重要
- [ ] 购买生活用品 📅 2025-05-19 🍅1 🔽 #project/生活 #不重要
- [ ] 预约体检 📅 2025-05-22 🍅1 🔼 #project/健康 #重要
```

## 🔧 使用技巧

### 1. 番茄钟估算
- **简单任务**: 🍅1 (25分钟)
- **中等任务**: 🍅2-3 (50-75分钟)
- **复杂任务**: 🍅4-5 (100-125分钟)
- **大型任务**: 建议拆分为子任务

### 2. 优先级设置
- **⏫ 最高优先级**: 今天必须完成的任务
- **🔼 高优先级**: 重要且紧急的任务
- **🔽 低优先级**: 可以延后的任务
- **无优先级**: 普通任务

### 3. 项目分类建议
- `#project/工作` - 工作相关任务
- `#project/学习` - 学习和技能提升
- `#project/生活` - 日常生活事务
- `#project/健康` - 健康和运动
- `#project/财务` - 财务管理
- `#project/社交` - 社交和人际关系

### 4. 重要性标记
- `#重要` - 对目标有重大影响的任务
- `#不重要` - 可做可不做的任务
- 不标记 - 普通重要性任务

## ⚠️ 常见错误

### ❌ 错误格式
```markdown
- [ ] 完成报告 2025-05-20 3个番茄钟 高优先级 工作项目 重要
- [ ] @报告 收集数据 明天 1小时
```

### ✅ 正确格式
```markdown
- [ ] 完成报告 📅 2025-05-20 🍅3 ⏫ #project/工作 #重要
- [ ] @完成报告 收集数据 📅 2025-05-17 🍅2 🔼 #project/工作
```

## 🚀 最佳实践

### 1. 任务命名
- 使用动词开头：`完成`、`撰写`、`分析`
- 具体明确：避免模糊描述
- 长度适中：建议10-30个字符

### 2. 日期设置
- 设置合理的截止日期
- 考虑依赖关系：子任务应早于主任务
- 留有缓冲时间

### 3. 番茄钟规划
- 根据任务复杂度估算
- 考虑个人工作效率
- 定期回顾和调整估算准确性

### 4. 层级管理
- 主任务：代表完整目标
- 子任务：具体执行步骤
- 避免层级过深（建议最多2层）

## 📊 与仪表盘的配合

使用标准格式的任务将在[[任务仪表盘]]中获得最佳体验：

1. **筛选功能**: 通过重要性、项目、类型筛选
2. **时间汇总**: 按今日、本周、本月汇总
3. **番茄钟统计**: 自动统计和分析番茄钟使用情况
4. **层级关系**: 清晰显示主任务与子任务关系
5. **提醒系统**: 逾期和即将到期任务提醒

## 🔄 格式迁移

如果您已有任务需要转换为标准格式，可以：

1. 使用[[任务仪表盘]]中的快速操作工具
2. 批量查找替换常见格式
3. 逐步迁移，保持系统一致性

---

💡 **提示**: 坚持使用标准格式将大大提升任务管理效率，建议将此文档加入书签以便随时参考。
