---
tags:
  - type/dashboard
  - dashboard/task
created: 2025-05-16T16:00
updated: 2025-05-16T16:00
---

# 📋 任务仪表盘

> 简洁实用的任务管理仪表盘

## 🔍 任务筛选器

```dataviewjs
// 全局样式和主题设置
const globalStyles = document.createElement('style');
globalStyles.textContent = `
    .task-dashboard {
        --primary-color: #007bff;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --border-color: #dee2e6;
        --shadow-sm: 0 .125rem .25rem rgba(0,0,0,.075);
        --shadow: 0 .5rem 1rem rgba(0,0,0,.15);
        --radius: 8px;
        --spacing: 1rem;

        font-family: -apple-system, BlinkMacSystemFont, "Se<PERSON><PERSON> UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
        line-height: 1.5;
        color: var(--dark-color);
    }

    .task-dashboard .card {
        background: white;
        border-radius: var(--radius);
        box-shadow: var(--shadow-sm);
        padding: var(--spacing);
        margin-bottom: var(--spacing);
        transition: all 0.3s ease;
    }

    .task-dashboard .card:hover {
        box-shadow: var(--shadow);
        transform: translateY(-2px);
    }

    .task-dashboard .btn {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .task-dashboard .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .task-dashboard .btn-success {
        background: var(--success-color);
        color: white;
    }

    .task-dashboard .btn-warning {
        background: var(--warning-color);
        color: black;
    }

    .task-dashboard .btn-danger {
        background: var(--danger-color);
        color: white;
    }

    .task-dashboard .grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing);
    }

    .task-dashboard .badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 50rem;
        font-size: 0.875rem;
        font-weight: 700;
    }

    .task-dashboard .badge-primary {
        background: var(--primary-color);
        color: white;
    }

    .task-dashboard .badge-success {
        background: var(--success-color);
        color: white;
    }

    .task-dashboard .badge-warning {
        background: var(--warning-color);
        color: black;
    }

    .task-dashboard .badge-danger {
        background: var(--danger-color);
        color: white;
    }

    .task-dashboard .progress {
        height: 8px;
        background: var(--light-color);
        border-radius: 4px;
        overflow: hidden;
    }

    .task-dashboard .progress-bar {
        height: 100%;
        background: var(--primary-color);
        transition: width 0.3s ease;
    }

    .task-dashboard .table {
        width: 100%;
        border-collapse: collapse;
    }

    .task-dashboard .table th,
    .task-dashboard .table td {
        padding: 0.75rem;
        border-bottom: 1px solid var(--border-color);
    }

    .task-dashboard .table th {
        background: var(--light-color);
        font-weight: 600;
        text-align: left;
    }

    .task-dashboard .input {
        display: block;
        width: 100%;
        padding: 0.5rem;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 1rem;
        line-height: 1.5;
    }

    .task-dashboard .input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    @media (max-width: 768px) {
        .task-dashboard .grid {
            grid-template-columns: 1fr;
        }

        .task-dashboard .card {
            margin-bottom: 0.5rem;
        }

        .task-dashboard .table {
            display: block;
            overflow-x: auto;
        }
    }

    @media (prefers-color-scheme: dark) {
        .task-dashboard {
            --light-color: #2d3436;
            --dark-color: #dfe6e9;
            --border-color: #636e72;
            color: var(--dark-color);
        }

        .task-dashboard .card {
            background: #2d3436;
        }

        .task-dashboard .table th {
            background: #2d3436;
        }
    }
`;

document.head.appendChild(globalStyles);

// 将主容器添加到类
this.container.className = 'task-dashboard';

// 创建任务筛选界面
const container = this.container;

// 重要性筛选器
const importanceSelector = document.createElement('select');
importanceSelector.id = 'task-importance-selector';
importanceSelector.style.margin = '10px 5px';
importanceSelector.style.padding = '5px';
importanceSelector.style.width = '120px';

const importanceOptions = ['全部', '重要', '不重要'];
importanceOptions.forEach(option => {
    const opt = document.createElement('option');
    opt.value = option;
    opt.text = option;
    importanceSelector.appendChild(opt);
});

// 任务类型筛选器
const typeSelector = document.createElement('select');
typeSelector.id = 'task-type-selector';
typeSelector.style.margin = '10px 5px';
typeSelector.style.padding = '5px';
typeSelector.style.width = '120px';

const typeOptions = ['全部', '主任务', '子任务'];
typeOptions.forEach(option => {
    const opt = document.createElement('option');
    opt.value = option;
    opt.text = option;
    typeSelector.appendChild(opt);
});

// 项目筛选器
const projectSelector = document.createElement('select');
projectSelector.id = 'task-project-selector';
projectSelector.style.margin = '10px 5px';
projectSelector.style.padding = '5px';
projectSelector.style.width = '150px';

// 查询按钮
const queryButton = document.createElement('button');
queryButton.textContent = '查询任务';
queryButton.style.margin = '0 10px';
queryButton.style.padding = '5px 15px';
queryButton.style.backgroundColor = '#4CAF50';
queryButton.style.color = 'white';
queryButton.style.border = 'none';
queryButton.style.borderRadius = '4px';
queryButton.style.cursor = 'pointer';

// 结果容器
const resultContainer = document.createElement('div');
resultContainer.id = 'task-query-results';
resultContainer.style.marginTop = '15px';

// 添加到页面
container.appendChild(document.createTextNode('重要性: '));
container.appendChild(importanceSelector);
container.appendChild(document.createTextNode('类型: '));
container.appendChild(typeSelector);
container.appendChild(document.createTextNode('项目: '));
container.appendChild(projectSelector);
container.appendChild(queryButton);
container.appendChild(document.createElement('hr'));
container.appendChild(resultContainer);

// 初始化项目选项
async function initProjectOptions() {
    const allTasks = dv.pages().file.tasks.where(t => !t.completed);
    const projects = new Set(['全部']);

    allTasks.forEach(task => {
        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        if (projectMatch) {
            projects.add(projectMatch[1]);
        }
    });

    projects.forEach(project => {
        const opt = document.createElement('option');
        opt.value = project;
        opt.text = project;
        projectSelector.appendChild(opt);
    });
}

// 查询函数
async function queryTasks() {
    const selectedImportance = importanceSelector.value;
    const selectedType = typeSelector.value;
    const selectedProject = projectSelector.value;
    const resultDiv = document.getElementById('task-query-results');

    resultDiv.innerHTML = '<p>正在查询...</p>';

    // 获取所有未完成任务
    let allTasks = dv.pages().file.tasks.where(t => !t.completed).array();

    // 筛选任务
    let filteredTasks = allTasks.filter(task => {
        // 重要性筛选
        if (selectedImportance !== '全部') {
            const hasImportantTag = task.text.includes('#重要');
            const hasUnimportantTag = task.text.includes('#不重要');

            if (selectedImportance === '重要' && !hasImportantTag) return false;
            if (selectedImportance === '不重要' && !hasUnimportantTag) return false;
        }

        // 类型筛选
        if (selectedType !== '全部') {
            const isSubTask = task.text.trim().startsWith('@');
            if (selectedType === '主任务' && isSubTask) return false;
            if (selectedType === '子任务' && !isSubTask) return false;
        }

        // 项目筛选
        if (selectedProject !== '全部') {
            const projectMatch = task.text.match(/#project\/([^\s#]+)/);
            const taskProject = projectMatch ? projectMatch[1] : '';
            if (taskProject !== selectedProject) return false;
        }

        return true;
    });

    // 显示结果
    if (filteredTasks.length > 0) {
        let html = `<h3>找到 ${filteredTasks.length} 个任务</h3>`;
        html += '<div style="display: grid; gap: 10px;">';

        filteredTasks.forEach(task => {
            // 解析任务信息
            const isSubTask = task.text.trim().startsWith('@');
            const taskIcon = isSubTask ? '📎' : '📋';

            // 提取番茄钟数量
            const tomatoMatch = task.text.match(/🍅(\d+)/);
            const tomatoCount = tomatoMatch ? parseInt(tomatoMatch[1]) : 0;
            const tomatoDisplay = '🍅'.repeat(tomatoCount);

            // 提取优先级
            let priorityIcon = '';
            if (task.text.includes('⏫')) priorityIcon = '⏫';
            else if (task.text.includes('🔼')) priorityIcon = '🔼';
            else if (task.text.includes('🔽')) priorityIcon = '🔽';

            // 提取截止日期
            const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
            const dueDate = dueDateMatch ? dueDateMatch[1] : '';

            // 提取项目
            const projectMatch = task.text.match(/#project\/([^\s#]+)/);
            const project = projectMatch ? projectMatch[1] : '';

            // 清理任务文本
            let cleanText = task.text
                .replace(/🍅\d+/g, '')
                .replace(/⏫|🔼|🔽/g, '')
                .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
                .replace(/#project\/[^\s#]+/g, '')
                .replace(/#重要|#不重要/g, '')
                .trim();

            html += `
                <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; background: #f9f9f9;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                        <span style="font-size: 1.2em;">${taskIcon}</span>
                        <span style="font-weight: bold;">${cleanText}</span>
                        ${priorityIcon ? `<span style="font-size: 1.1em;">${priorityIcon}</span>` : ''}
                    </div>
                    <div style="display: flex; gap: 15px; font-size: 0.9em; color: #666;">
                        ${dueDate ? `<span>📅 ${dueDate}</span>` : ''}
                        ${project ? `<span>📁 ${project}</span>` : ''}
                        ${tomatoCount > 0 ? `<span>${tomatoDisplay} (${tomatoCount}个)</span>` : ''}
                    </div>
                    <div style="margin-top: 5px; font-size: 0.8em; color: #888;">
                        来源: <span onclick="app.workspace.openLinkText('${task.path}', '', true)" style="color: #0077cc; cursor: pointer; text-decoration: underline;">${task.path}</span>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        resultDiv.innerHTML = html;
    } else {
        resultDiv.innerHTML = '<p>未找到符合条件的任务</p>';
    }
}

// 初始化
initProjectOptions();

// 添加事件监听
queryButton.addEventListener('click', queryTasks);
```

## 🔍 高级筛选功能

```dataviewjs
// 创建高级筛选界面
const advancedContainer = this.container;

// 创建筛选器容器
const filterContainer = document.createElement('div');
filterContainer.style.display = 'grid';
filterContainer.style.gridTemplateColumns = 'repeat(auto-fit, minmax(200px, 1fr))';
filterContainer.style.gap = '15px';
filterContainer.style.padding = '15px';
filterContainer.style.backgroundColor = '#f8f9fa';
filterContainer.style.borderRadius = '8px';
filterContainer.style.marginBottom = '20px';

// 优先级筛选
const priorityFilter = document.createElement('div');
priorityFilter.innerHTML = `
    <label style="font-weight: bold; margin-bottom: 5px; display: block;">优先级筛选:</label>
    <select id="priority-filter" style="width: 100%; padding: 5px;">
        <option value="">全部优先级</option>
        <option value="⏫">⏫ 最高优先级</option>
        <option value="🔼">🔼 高优先级</option>
        <option value="🔽">🔽 低优先级</option>
        <option value="none">无优先级</option>
    </select>
`;

// 日期范围筛选
const dateFilter = document.createElement('div');
dateFilter.innerHTML = `
    <label style="font-weight: bold; margin-bottom: 5px; display: block;">截止日期范围:</label>
    <div style="display: flex; gap: 5px;">
        <input type="date" id="date-from" style="flex: 1; padding: 5px;" placeholder="开始日期">
        <input type="date" id="date-to" style="flex: 1; padding: 5px;" placeholder="结束日期">
    </div>
`;

// 番茄钟范围筛选
const tomatoFilter = document.createElement('div');
tomatoFilter.innerHTML = `
    <label style="font-weight: bold; margin-bottom: 5px; display: block;">番茄钟范围:</label>
    <div style="display: flex; gap: 5px; align-items: center;">
        <input type="number" id="tomato-min" min="0" max="20" style="width: 60px; padding: 5px;" placeholder="最小">
        <span>-</span>
        <input type="number" id="tomato-max" min="0" max="20" style="width: 60px; padding: 5px;" placeholder="最大">
        <span>🍅</span>
    </div>
`;

// 快速筛选预设
const presetFilter = document.createElement('div');
presetFilter.innerHTML = `
    <label style="font-weight: bold; margin-bottom: 5px; display: block;">快速筛选:</label>
    <select id="preset-filter" style="width: 100%; padding: 5px;">
        <option value="">选择预设筛选</option>
        <option value="urgent">紧急任务(今明两天)</option>
        <option value="high-priority">高优先级任务</option>
        <option value="big-tasks">大任务(3+番茄钟)</option>
        <option value="quick-tasks">快速任务(1番茄钟)</option>
        <option value="overdue">逾期任务</option>
        <option value="no-date">无截止日期</option>
    </select>
`;

// 添加筛选器到容器
filterContainer.appendChild(priorityFilter);
filterContainer.appendChild(dateFilter);
filterContainer.appendChild(tomatoFilter);
filterContainer.appendChild(presetFilter);

// 操作按钮
const buttonContainer = document.createElement('div');
buttonContainer.style.display = 'flex';
buttonContainer.style.gap = '10px';
buttonContainer.style.marginTop = '15px';

const advancedSearchBtn = document.createElement('button');
advancedSearchBtn.textContent = '🔍 高级搜索';
advancedSearchBtn.style.padding = '8px 16px';
advancedSearchBtn.style.backgroundColor = '#007bff';
advancedSearchBtn.style.color = 'white';
advancedSearchBtn.style.border = 'none';
advancedSearchBtn.style.borderRadius = '4px';
advancedSearchBtn.style.cursor = 'pointer';

const clearFiltersBtn = document.createElement('button');
clearFiltersBtn.textContent = '🗑️ 清除筛选';
clearFiltersBtn.style.padding = '8px 16px';
clearFiltersBtn.style.backgroundColor = '#6c757d';
clearFiltersBtn.style.color = 'white';
clearFiltersBtn.style.border = 'none';
clearFiltersBtn.style.borderRadius = '4px';
clearFiltersBtn.style.cursor = 'pointer';

const savePresetBtn = document.createElement('button');
savePresetBtn.textContent = '💾 保存筛选';
savePresetBtn.style.padding = '8px 16px';
savePresetBtn.style.backgroundColor = '#28a745';
savePresetBtn.style.color = 'white';
savePresetBtn.style.border = 'none';
savePresetBtn.style.borderRadius = '4px';
savePresetBtn.style.cursor = 'pointer';

buttonContainer.appendChild(advancedSearchBtn);
buttonContainer.appendChild(clearFiltersBtn);
buttonContainer.appendChild(savePresetBtn);

// 结果显示区域
const advancedResultContainer = document.createElement('div');
advancedResultContainer.id = 'advanced-search-results';
advancedResultContainer.style.marginTop = '20px';

// 添加到页面
advancedContainer.appendChild(filterContainer);
advancedContainer.appendChild(buttonContainer);
advancedContainer.appendChild(advancedResultContainer);

// 高级搜索函数
async function performAdvancedSearch() {
    const priorityFilter = document.getElementById('priority-filter').value;
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    const tomatoMin = document.getElementById('tomato-min').value;
    const tomatoMax = document.getElementById('tomato-max').value;
    const presetFilter = document.getElementById('preset-filter').value;

    const resultDiv = document.getElementById('advanced-search-results');
    resultDiv.innerHTML = '<p>正在搜索...</p>';

    // 获取所有未完成任务
    let allTasks = dv.pages().file.tasks.where(t => !t.completed).array();

    // 应用筛选条件
    let filteredTasks = allTasks.filter(task => {
        // 优先级筛选
        if (priorityFilter) {
            if (priorityFilter === 'none') {
                if (task.text.includes('⏫') || task.text.includes('🔼') || task.text.includes('🔽')) {
                    return false;
                }
            } else {
                if (!task.text.includes(priorityFilter)) {
                    return false;
                }
            }
        }

        // 日期范围筛选
        if (dateFrom || dateTo) {
            const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
            if (!dueDateMatch) return false;

            const taskDate = dueDateMatch[1];
            if (dateFrom && taskDate < dateFrom) return false;
            if (dateTo && taskDate > dateTo) return false;
        }

        // 番茄钟范围筛选
        if (tomatoMin || tomatoMax) {
            const tomatoMatch = task.text.match(/🍅(\d+)/);
            const tomatoCount = tomatoMatch ? parseInt(tomatoMatch[1]) : 0;

            if (tomatoMin && tomatoCount < parseInt(tomatoMin)) return false;
            if (tomatoMax && tomatoCount > parseInt(tomatoMax)) return false;
        }

        // 预设筛选
        if (presetFilter) {
            const today = new Date().toISOString().split('T')[0];
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowStr = tomorrow.toISOString().split('T')[0];

            switch (presetFilter) {
                case 'urgent':
                    const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
                    if (!dueDateMatch) return false;
                    const taskDate = dueDateMatch[1];
                    return taskDate === today || taskDate === tomorrowStr;

                case 'high-priority':
                    return task.text.includes('⏫') || task.text.includes('🔼');

                case 'big-tasks':
                    const tomatoMatch = task.text.match(/🍅(\d+)/);
                    const tomatoCount = tomatoMatch ? parseInt(tomatoMatch[1]) : 0;
                    return tomatoCount >= 3;

                case 'quick-tasks':
                    const quickTomatoMatch = task.text.match(/🍅(\d+)/);
                    const quickTomatoCount = quickTomatoMatch ? parseInt(quickTomatoMatch[1]) : 0;
                    return quickTomatoCount === 1;

                case 'overdue':
                    const overdueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
                    if (!overdueDateMatch) return false;
                    return overdueDateMatch[1] < today;

                case 'no-date':
                    return !task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
            }
        }

        return true;
    });

    // 显示结果
    if (filteredTasks.length > 0) {
        let html = `<h3>🔍 找到 ${filteredTasks.length} 个符合条件的任务</h3>`;
        html += '<div style="display: grid; gap: 10px;">';

        filteredTasks.forEach(task => {
            const isSubTask = task.text.trim().startsWith('@');
            const taskIcon = isSubTask ? '📎' : '📋';

            // 提取任务信息
            const tomatoMatch = task.text.match(/🍅(\d+)/);
            const tomatoCount = tomatoMatch ? parseInt(tomatoMatch[1]) : 0;

            const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
            const dueDate = dueDateMatch ? dueDateMatch[1] : '';

            const projectMatch = task.text.match(/#project\/([^\s#]+)/);
            const project = projectMatch ? projectMatch[1] : '未分类';

            let priorityIcon = '';
            if (task.text.includes('⏫')) priorityIcon = '⏫';
            else if (task.text.includes('🔼')) priorityIcon = '🔼';
            else if (task.text.includes('🔽')) priorityIcon = '🔽';

            let cleanText = task.text
                .replace(/🍅\d+/g, '')
                .replace(/⏫|🔼|🔽/g, '')
                .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
                .replace(/#project\/[^\s#]+/g, '')
                .replace(/#重要|#不重要/g, '')
                .trim();

            // 判断任务状态
            let statusBadge = '';
            if (dueDate) {
                const today = new Date().toISOString().split('T')[0];
                if (dueDate < today) {
                    statusBadge = '<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">逾期</span>';
                } else if (dueDate === today) {
                    statusBadge = '<span style="background: #fd7e14; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">今日</span>';
                }
            }

            html += `
                <div style="border: 1px solid #dee2e6; padding: 12px; border-radius: 6px; background: white;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                        <span style="font-size: 1.2em;">${taskIcon}</span>
                        <span style="font-weight: bold; flex: 1;">${cleanText}</span>
                        ${priorityIcon ? `<span style="font-size: 1.1em;">${priorityIcon}</span>` : ''}
                        ${statusBadge}
                    </div>
                    <div style="display: flex; gap: 15px; font-size: 0.9em; color: #6c757d;">
                        ${dueDate ? `<span>📅 ${dueDate}</span>` : '<span>📅 无截止日期</span>'}
                        <span>📁 ${project}</span>
                        ${tomatoCount > 0 ? `<span>🍅 ${tomatoCount}个</span>` : '<span>🍅 未设置</span>'}
                    </div>
                    <div style="margin-top: 8px; font-size: 0.8em;">
                        <span onclick="app.workspace.openLinkText('${task.path}', '', true)" style="color: #007bff; cursor: pointer; text-decoration: underline;">📂 ${task.path}</span>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        resultDiv.innerHTML = html;
    } else {
        resultDiv.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;"><p>😔 未找到符合条件的任务</p></div>';
    }
}

// 清除筛选条件
function clearAllFilters() {
    document.getElementById('priority-filter').value = '';
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';
    document.getElementById('tomato-min').value = '';
    document.getElementById('tomato-max').value = '';
    document.getElementById('preset-filter').value = '';
    document.getElementById('advanced-search-results').innerHTML = '';
}

// 保存筛选预设
function saveFilterPreset() {
    const presetName = prompt('请输入预设名称:');
    if (presetName) {
        const filterData = {
            priority: document.getElementById('priority-filter').value,
            dateFrom: document.getElementById('date-from').value,
            dateTo: document.getElementById('date-to').value,
            tomatoMin: document.getElementById('tomato-min').value,
            tomatoMax: document.getElementById('tomato-max').value
        };

        // 这里可以保存到localStorage或其他地方
        localStorage.setItem(`task-filter-${presetName}`, JSON.stringify(filterData));
        alert(`筛选预设 "${presetName}" 已保存！`);
    }
}

// 添加事件监听器
advancedSearchBtn.addEventListener('click', performAdvancedSearch);
clearFiltersBtn.addEventListener('click', clearAllFilters);
savePresetBtn.addEventListener('click', saveFilterPreset);

// 预设筛选变化时自动搜索 - 延迟添加事件监听器
setTimeout(() => {
    const presetFilterElement = document.getElementById('preset-filter');
    if (presetFilterElement) {
        presetFilterElement.addEventListener('change', function() {
            if (this.value) {
                // 清除其他筛选条件
                document.getElementById('priority-filter').value = '';
                document.getElementById('date-from').value = '';
                document.getElementById('date-to').value = '';
                document.getElementById('tomato-min').value = '';
                document.getElementById('tomato-max').value = '';

                // 执行搜索
                performAdvancedSearch();
            }
        });
    }
}, 100);
```

## 📊 任务统计概览

```dataviewjs
// 获取所有未完成任务
const allTasks = dv.pages().file.tasks.where(t => !t.completed);

// 统计数据
let stats = {
    total: 0,
    mainTasks: 0,
    subTasks: 0,
    important: 0,
    unimportant: 0,
    totalTomatoes: 0,
    byProject: {},
    byPriority: { high: 0, medium: 0, low: 0, none: 0 }
};

allTasks.forEach(task => {
    stats.total++;

    // 主任务 vs 子任务
    if (task.text.trim().startsWith('@')) {
        stats.subTasks++;
    } else {
        stats.mainTasks++;
    }

    // 重要性统计
    if (task.text.includes('#重要')) stats.important++;
    if (task.text.includes('#不重要')) stats.unimportant++;

    // 番茄钟统计
    const tomatoMatch = task.text.match(/🍅(\d+)/);
    if (tomatoMatch) {
        stats.totalTomatoes += parseInt(tomatoMatch[1]);
    }

    // 项目统计
    const projectMatch = task.text.match(/#project\/([^\s#]+)/);
    if (projectMatch) {
        const project = projectMatch[1];
        stats.byProject[project] = (stats.byProject[project] || 0) + 1;
    }

    // 优先级统计
    if (task.text.includes('⏫')) stats.byPriority.high++;
    else if (task.text.includes('🔼')) stats.byPriority.medium++;
    else if (task.text.includes('🔽')) stats.byPriority.low++;
    else stats.byPriority.none++;
});

// 显示统计表格
dv.table(
    ["指标", "数量", "可视化"],
    [
        ["📋 总任务数", stats.total, "█".repeat(Math.min(20, stats.total))],
        ["🎯 主任务", stats.mainTasks, "█".repeat(Math.min(20, stats.mainTasks))],
        ["📎 子任务", stats.subTasks, "█".repeat(Math.min(20, stats.subTasks))],
        ["⭐ 重要任务", stats.important, "█".repeat(Math.min(20, stats.important))],
        ["🍅 总番茄钟", stats.totalTomatoes, "🍅".repeat(Math.min(10, stats.totalTomatoes))]
    ]
);

// 项目分布
if (Object.keys(stats.byProject).length > 0) {
    dv.header(3, "📁 项目分布");
    const projectTable = Object.entries(stats.byProject)
        .sort((a, b) => b[1] - a[1])
        .map(([project, count]) => [
            project,
            count,
            "█".repeat(Math.min(15, count))
        ]);
    dv.table(["项目", "任务数", "分布"], projectTable);
}
```

## 📅 时间维度任务汇总

### 🔥 今日任务

```dataviewjs
const today = new Date().toISOString().split('T')[0];
const todayTasks = dv.pages().file.tasks
    .where(t => !t.completed && t.text.includes(`📅 ${today}`))
    .sort(t => {
        if (t.text.includes('⏫')) return 0;
        if (t.text.includes('🔼')) return 1;
        if (t.text.includes('🔽')) return 3;
        return 2;
    });

if (todayTasks.length > 0) {
    let todayTomatoes = 0;
    const taskData = todayTasks.map(task => {
        const isSubTask = task.text.trim().startsWith('@');
        const taskIcon = isSubTask ? '📎' : '📋';

        const tomatoMatch = task.text.match(/🍅(\d+)/);
        const tomatoCount = tomatoMatch ? parseInt(tomatoMatch[1]) : 0;
        todayTomatoes += tomatoCount;

        let priorityIcon = '';
        if (task.text.includes('⏫')) priorityIcon = '⏫ 最高';
        else if (task.text.includes('🔼')) priorityIcon = '🔼 高';
        else if (task.text.includes('🔽')) priorityIcon = '🔽 低';
        else priorityIcon = '➖ 普通';

        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? projectMatch[1] : '未分类';

        let cleanText = task.text
            .replace(/🍅\d+/g, '')
            .replace(/⏫|🔼|🔽/g, '')
            .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
            .replace(/#project\/[^\s#]+/g, '')
            .replace(/#重要|#不重要/g, '')
            .trim();

        return [
            `${taskIcon} ${cleanText}`,
            priorityIcon,
            project,
            tomatoCount > 0 ? `🍅 ${tomatoCount}` : '',
            `[[${task.path}|查看]]`
        ];
    });

    dv.paragraph(`**今日任务总览**: ${todayTasks.length} 个任务，预计需要 ${todayTomatoes} 个番茄钟 🍅`);
    dv.table(["任务", "优先级", "项目", "番茄钟", "来源"], taskData);
} else {
    dv.paragraph("🎉 今日暂无任务安排");
}
```

### 📅 本周任务

```dataviewjs
// 计算本周日期范围
const now = new Date();
const dayOfWeek = now.getDay();
const startOfWeek = new Date(now);
startOfWeek.setDate(now.getDate() - dayOfWeek);
const endOfWeek = new Date(startOfWeek);
endOfWeek.setDate(startOfWeek.getDate() + 6);

const startDate = startOfWeek.toISOString().split('T')[0];
const endDate = endOfWeek.toISOString().split('T')[0];

const weekTasks = dv.pages().file.tasks
    .where(t => {
        if (t.completed) return false;
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch) return false;
        const dueDate = dueDateMatch[1];
        return dueDate >= startDate && dueDate <= endDate;
    })
    .sort(t => {
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        return dueDateMatch ? dueDateMatch[1] : '9999-99-99';
    });

if (weekTasks.length > 0) {
    let weekTomatoes = 0;
    const weekData = weekTasks.map(task => {
        const isSubTask = task.text.trim().startsWith('@');
        const taskIcon = isSubTask ? '📎' : '📋';

        const tomatoMatch = task.text.match(/🍅(\d+)/);
        const tomatoCount = tomatoMatch ? parseInt(tomatoMatch[1]) : 0;
        weekTomatoes += tomatoCount;

        const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        const dueDate = dueDateMatch ? dueDateMatch[1] : '';

        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? projectMatch[1] : '未分类';

        let cleanText = task.text
            .replace(/🍅\d+/g, '')
            .replace(/⏫|🔼|🔽/g, '')
            .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
            .replace(/#project\/[^\s#]+/g, '')
            .replace(/#重要|#不重要/g, '')
            .trim();

        return [
            `${taskIcon} ${cleanText}`,
            dueDate,
            project,
            tomatoCount > 0 ? `🍅 ${tomatoCount}` : '',
            `[[${task.path}|查看]]`
        ];
    });

    dv.paragraph(`**本周任务总览**: ${weekTasks.length} 个任务，预计需要 ${weekTomatoes} 个番茄钟 🍅`);
    dv.table(["任务", "截止日期", "项目", "番茄钟", "来源"], weekData);
} else {
    dv.paragraph("📅 本周暂无任务安排");
}
```

### 📅 本月任务

```dataviewjs
// 计算本月日期范围
const currentDate = new Date();
const year = currentDate.getFullYear();
const month = currentDate.getMonth();
const startOfMonth = new Date(year, month, 1);
const endOfMonth = new Date(year, month + 1, 0);

const monthStart = startOfMonth.toISOString().split('T')[0];
const monthEnd = endOfMonth.toISOString().split('T')[0];

const monthTasks = dv.pages().file.tasks
    .where(t => {
        if (t.completed) return false;
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch) return false;
        const dueDate = dueDateMatch[1];
        return dueDate >= monthStart && dueDate <= monthEnd;
    })
    .sort(t => {
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        return dueDateMatch ? dueDateMatch[1] : '9999-99-99';
    });

if (monthTasks.length > 0) {
    let monthTomatoes = 0;

    // 按项目分组统计
    const projectStats = {};
    monthTasks.forEach(task => {
        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? projectMatch[1] : '未分类';

        if (!projectStats[project]) {
            projectStats[project] = { count: 0, tomatoes: 0 };
        }
        projectStats[project].count++;

        const tomatoMatch = task.text.match(/🍅(\d+)/);
        const tomatoCount = tomatoMatch ? parseInt(tomatoMatch[1]) : 0;
        projectStats[project].tomatoes += tomatoCount;
        monthTomatoes += tomatoCount;
    });

    dv.paragraph(`**本月任务总览**: ${monthTasks.length} 个任务，预计需要 ${monthTomatoes} 个番茄钟 🍅`);

    // 显示项目统计
    const projectData = Object.entries(projectStats)
        .sort((a, b) => b[1].count - a[1].count)
        .map(([project, stats]) => [
            project,
            stats.count,
            stats.tomatoes > 0 ? `🍅 ${stats.tomatoes}` : '',
            "█".repeat(Math.min(10, stats.count))
        ]);

    dv.table(["项目", "任务数", "番茄钟", "分布"], projectData);
} else {
    dv.paragraph("📅 本月暂无任务安排");
}
```

## 🎯 主任务与子任务关系

```dataviewjs
// 获取所有主任务和子任务
const allTasks = dv.pages().file.tasks.where(t => !t.completed);
const mainTasks = allTasks.where(t => !t.text.trim().startsWith('@'));
const subTasks = allTasks.where(t => t.text.trim().startsWith('@'));

// 建立主任务与子任务的关系映射
const taskRelations = {};

mainTasks.forEach(mainTask => {
    let cleanMainTaskName = mainTask.text
        .replace(/🍅\d+/g, '')
        .replace(/⏫|🔼|🔽/g, '')
        .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
        .replace(/#project\/[^\s#]+/g, '')
        .replace(/#重要|#不重要/g, '')
        .trim();

    taskRelations[cleanMainTaskName] = {
        mainTask: mainTask,
        subTasks: [],
        totalTomatoes: 0
    };

    // 提取主任务的番茄钟
    const tomatoMatch = mainTask.text.match(/🍅(\d+)/);
    if (tomatoMatch) {
        taskRelations[cleanMainTaskName].totalTomatoes += parseInt(tomatoMatch[1]);
    }
});

// 匹配子任务到主任务
subTasks.forEach(subTask => {
    const subTaskText = subTask.text.trim();
    const atMatch = subTaskText.match(/^@([^@]+?)\s/);
    if (atMatch) {
        const mainTaskName = atMatch[1].trim();

        // 寻找匹配的主任务
        for (const [key, relation] of Object.entries(taskRelations)) {
            if (key.includes(mainTaskName) || mainTaskName.includes(key)) {
                relation.subTasks.push(subTask);

                // 累加子任务的番茄钟
                const tomatoMatch = subTask.text.match(/🍅(\d+)/);
                if (tomatoMatch) {
                    relation.totalTomatoes += parseInt(tomatoMatch[1]);
                }
                break;
            }
        }
    }
});

// 显示任务关系
dv.header(3, "🔗 任务层级关系");

Object.entries(taskRelations).forEach(([mainTaskName, relation]) => {
    if (relation.subTasks.length > 0 || relation.totalTomatoes > 0) {
        dv.header(4, `📋 ${mainTaskName}`);

        // 主任务信息
        const mainTaskInfo = [
            `**总番茄钟**: 🍅 ${relation.totalTomatoes}`,
            `**子任务数**: ${relation.subTasks.length}`,
            `**来源**: [[${relation.mainTask.path}|查看]]`
        ].join(" | ");

        dv.paragraph(mainTaskInfo);

        // 子任务列表
        if (relation.subTasks.length > 0) {
            const subTaskData = relation.subTasks.map(subTask => {
                const tomatoMatch = subTask.text.match(/🍅(\d+)/);
                const tomatoCount = tomatoMatch ? parseInt(tomatoMatch[1]) : 0;

                const dueDateMatch = subTask.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
                const dueDate = dueDateMatch ? dueDateMatch[1] : '';

                let priorityIcon = '';
                if (subTask.text.includes('⏫')) priorityIcon = '⏫';
                else if (subTask.text.includes('🔼')) priorityIcon = '🔼';
                else if (subTask.text.includes('🔽')) priorityIcon = '🔽';

                let cleanText = subTask.text
                    .replace(/^@[^@]+?\s/, '')
                    .replace(/🍅\d+/g, '')
                    .replace(/⏫|🔼|🔽/g, '')
                    .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
                    .replace(/#project\/[^\s#]+/g, '')
                    .replace(/#重要|#不重要/g, '')
                    .trim();

                return [
                    `📎 ${cleanText}`,
                    priorityIcon,
                    dueDate,
                    tomatoCount > 0 ? `🍅 ${tomatoCount}` : '',
                    `[[${subTask.path}|查看]]`
                ];
            });

            dv.table(["子任务", "优先级", "截止日期", "番茄钟", "来源"], subTaskData);
        }

        dv.paragraph("---");
    }
});
```

## 🍅 番茄钟统计分析

```dataviewjs
// 获取所有任务的番茄钟数据
const allTasks = dv.pages().file.tasks;
const completedTasks = allTasks.where(t => t.completed);
const pendingTasks = allTasks.where(t => !t.completed);

// 统计数据
let tomatoStats = {
    totalPlanned: 0,
    totalCompleted: 0,
    completedTaskTomatoes: 0,
    pendingTaskTomatoes: 0,
    byProject: {},
    byPriority: { high: 0, medium: 0, low: 0, none: 0 },
    dailyStats: {}
};

// 统计所有任务的番茄钟
allTasks.forEach(task => {
    const tomatoMatch = task.text.match(/🍅(\d+)/);
    if (tomatoMatch) {
        const tomatoCount = parseInt(tomatoMatch[1]);
        tomatoStats.totalPlanned += tomatoCount;

        if (task.completed) {
            tomatoStats.completedTaskTomatoes += tomatoCount;
        } else {
            tomatoStats.pendingTaskTomatoes += tomatoCount;
        }

        // 按项目统计
        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? projectMatch[1] : '未分类';
        if (!tomatoStats.byProject[project]) {
            tomatoStats.byProject[project] = { planned: 0, completed: 0 };
        }
        tomatoStats.byProject[project].planned += tomatoCount;
        if (task.completed) {
            tomatoStats.byProject[project].completed += tomatoCount;
        }

        // 按优先级统计
        if (task.text.includes('⏫')) tomatoStats.byPriority.high += tomatoCount;
        else if (task.text.includes('🔼')) tomatoStats.byPriority.medium += tomatoCount;
        else if (task.text.includes('🔽')) tomatoStats.byPriority.low += tomatoCount;
        else tomatoStats.byPriority.none += tomatoCount;
    }
});

// 计算完成率
const completionRate = tomatoStats.totalPlanned > 0 ?
    Math.round((tomatoStats.completedTaskTomatoes / tomatoStats.totalPlanned) * 100) : 0;

// 显示总体统计
dv.header(3, "🍅 番茄钟总体统计");
dv.table(
    ["指标", "数量", "可视化"],
    [
        ["📊 总计划番茄钟", tomatoStats.totalPlanned, "🍅".repeat(Math.min(15, tomatoStats.totalPlanned))],
        ["✅ 已完成番茄钟", tomatoStats.completedTaskTomatoes, "🍅".repeat(Math.min(15, tomatoStats.completedTaskTomatoes))],
        ["⏳ 待完成番茄钟", tomatoStats.pendingTaskTomatoes, "🍅".repeat(Math.min(15, tomatoStats.pendingTaskTomatoes))],
        ["📈 完成率", `${completionRate}%`, "█".repeat(Math.min(20, Math.round(completionRate/5)))]
    ]
);

// 按项目显示番茄钟分布
if (Object.keys(tomatoStats.byProject).length > 0) {
    dv.header(3, "📁 项目番茄钟分布");
    const projectTomatoData = Object.entries(tomatoStats.byProject)
        .sort((a, b) => b[1].planned - a[1].planned)
        .map(([project, stats]) => {
            const projectCompletionRate = stats.planned > 0 ?
                Math.round((stats.completed / stats.planned) * 100) : 0;
            return [
                project,
                stats.planned,
                stats.completed,
                stats.planned - stats.completed,
                `${projectCompletionRate}%`,
                "█".repeat(Math.min(10, Math.round(projectCompletionRate/10)))
            ];
        });

    dv.table(
        ["项目", "计划🍅", "完成🍅", "剩余🍅", "完成率", "进度"],
        projectTomatoData
    );
}

// 按优先级显示番茄钟分布
dv.header(3, "⚡ 优先级番茄钟分布");
const priorityData = [
    ["⏫ 最高优先级", tomatoStats.byPriority.high, "🍅".repeat(Math.min(10, tomatoStats.byPriority.high))],
    ["🔼 高优先级", tomatoStats.byPriority.medium, "🍅".repeat(Math.min(10, tomatoStats.byPriority.medium))],
    ["🔽 低优先级", tomatoStats.byPriority.low, "🍅".repeat(Math.min(10, tomatoStats.byPriority.low))],
    ["➖ 无优先级", tomatoStats.byPriority.none, "🍅".repeat(Math.min(10, tomatoStats.byPriority.none))]
];

dv.table(["优先级", "番茄钟数", "分布"], priorityData);
```

## 📅 每日番茄钟统计

```dataviewjs
// 获取最近7天的日期
const last7Days = [];
for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    last7Days.push(date.toISOString().split('T')[0]);
}

// 统计每日番茄钟数据
const dailyTomatoStats = {};
last7Days.forEach(date => {
    dailyTomatoStats[date] = {
        planned: 0,
        completed: 0,
        tasks: []
    };
});

// 获取所有任务
const allTasks = dv.pages().file.tasks;

// 统计每日数据
allTasks.forEach(task => {
    const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
    if (dueDateMatch) {
        const dueDate = dueDateMatch[1];
        if (dailyTomatoStats[dueDate]) {
            const tomatoMatch = task.text.match(/🍅(\d+)/);
            if (tomatoMatch) {
                const tomatoCount = parseInt(tomatoMatch[1]);
                dailyTomatoStats[dueDate].planned += tomatoCount;

                if (task.completed) {
                    dailyTomatoStats[dueDate].completed += tomatoCount;
                }

                dailyTomatoStats[dueDate].tasks.push({
                    text: task.text,
                    completed: task.completed,
                    tomatoes: tomatoCount
                });
            }
        }
    }
});

// 显示每日统计
dv.header(3, "📊 最近7天番茄钟统计");

const dailyData = last7Days.map(date => {
    const stats = dailyTomatoStats[date];
    const completionRate = stats.planned > 0 ? Math.round((stats.completed / stats.planned) * 100) : 0;

    // 格式化日期显示
    const dateObj = new Date(date);
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[dateObj.getDay()];
    const formattedDate = `${date.split('-')[1]}-${date.split('-')[2]} ${weekday}`;

    // 今天标记
    const today = new Date().toISOString().split('T')[0];
    const isToday = date === today;
    const dateDisplay = isToday ? `${formattedDate} 📍` : formattedDate;

    return [
        dateDisplay,
        stats.planned,
        stats.completed,
        stats.planned - stats.completed,
        `${completionRate}%`,
        "█".repeat(Math.min(10, Math.round(completionRate/10)))
    ];
});

dv.table(
    ["日期", "计划🍅", "完成🍅", "剩余🍅", "完成率", "进度"],
    dailyData
);

// 显示今日详细任务
const today = new Date().toISOString().split('T')[0];
const todayStats = dailyTomatoStats[today];

if (todayStats && todayStats.tasks.length > 0) {
    dv.header(4, "📋 今日任务详情");

    const todayTaskData = todayStats.tasks.map(task => {
        const isSubTask = task.text.trim().startsWith('@');
        const taskIcon = isSubTask ? '📎' : '📋';
        const statusIcon = task.completed ? '✅' : '⏳';

        let cleanText = task.text
            .replace(/🍅\d+/g, '')
            .replace(/⏫|🔼|🔽/g, '')
            .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
            .replace(/#project\/[^\s#]+/g, '')
            .replace(/#重要|#不重要/g, '')
            .trim();

        return [
            `${taskIcon} ${cleanText}`,
            `🍅 ${task.tomatoes}`,
            statusIcon,
            task.completed ? '已完成' : '进行中'
        ];
    });

    dv.table(["任务", "番茄钟", "状态", "进度"], todayTaskData);

    // 今日总结
    const todayTotal = todayStats.planned;
    const todayCompleted = todayStats.completed;
    const todayRemaining = todayTotal - todayCompleted;
    const todayRate = todayTotal > 0 ? Math.round((todayCompleted / todayTotal) * 100) : 0;

    dv.paragraph(`**今日总结**: 计划 ${todayTotal} 个🍅，已完成 ${todayCompleted} 个🍅，剩余 ${todayRemaining} 个🍅，完成率 ${todayRate}%`);
}

// 周趋势分析
const weekTotal = Object.values(dailyTomatoStats).reduce((sum, day) => sum + day.planned, 0);
const weekCompleted = Object.values(dailyTomatoStats).reduce((sum, day) => sum + day.completed, 0);
const weekRate = weekTotal > 0 ? Math.round((weekCompleted / weekTotal) * 100) : 0;

dv.header(4, "📈 本周趋势");
dv.paragraph(`**本周总计**: 计划 ${weekTotal} 个🍅，完成 ${weekCompleted} 个🍅，完成率 ${weekRate}%`);

// 简单的趋势图
const trendData = last7Days.map(date => {
    const stats = dailyTomatoStats[date];
    const rate = stats.planned > 0 ? Math.round((stats.completed / stats.planned) * 100) : 0;
    return rate;
});

const maxRate = Math.max(...trendData);
const trendChart = trendData.map((rate, index) => {
    const barHeight = maxRate > 0 ? Math.round((rate / maxRate) * 10) : 0;
    const dateShort = last7Days[index].split('-')[2];
    return `${dateShort}日\n${'█'.repeat(barHeight)}${' '.repeat(10-barHeight)}\n${rate}%`;
}).join('  ');

dv.paragraph(`\`\`\`\n${trendChart}\n\`\`\``);
```

## 🚨 任务提醒系统

```dataviewjs
const today = new Date();
const todayStr = today.toISOString().split('T')[0];

// 获取逾期任务
const overdueTasks = dv.pages().file.tasks
    .where(t => {
        if (t.completed) return false;
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch) return false;
        return dueDateMatch[1] < todayStr;
    })
    .sort(t => {
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        return dueDateMatch ? dueDateMatch[1] : '9999-99-99';
    });

// 获取即将到期任务（未来3天）
const upcomingTasks = dv.pages().file.tasks
    .where(t => {
        if (t.completed) return false;
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch) return false;
        const dueDate = new Date(dueDateMatch[1]);
        const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
        return diffDays >= 0 && diffDays <= 3;
    })
    .sort(t => {
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        return dueDateMatch ? dueDateMatch[1] : '9999-99-99';
    });

// 显示逾期任务
if (overdueTasks.length > 0) {
    dv.header(3, "🚨 逾期任务提醒");
    const overdueData = overdueTasks.map(task => {
        const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        const dueDate = dueDateMatch ? dueDateMatch[1] : '';
        const overdueDays = Math.floor((today - new Date(dueDate)) / (1000 * 60 * 60 * 24));

        const isSubTask = task.text.trim().startsWith('@');
        const taskIcon = isSubTask ? '📎' : '📋';

        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? projectMatch[1] : '未分类';

        let cleanText = task.text
            .replace(/🍅\d+/g, '')
            .replace(/⏫|🔼|🔽/g, '')
            .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
            .replace(/#project\/[^\s#]+/g, '')
            .replace(/#重要|#不重要/g, '')
            .trim();

        return [
            `${taskIcon} ${cleanText}`,
            dueDate,
            `🔴 逾期${overdueDays}天`,
            project,
            `[[${task.path}|查看]]`
        ];
    });

    dv.table(["任务", "截止日期", "状态", "项目", "来源"], overdueData);
} else {
    dv.paragraph("✅ 暂无逾期任务");
}

// 显示即将到期任务
if (upcomingTasks.length > 0) {
    dv.header(3, "⏰ 即将到期任务（3天内）");
    const upcomingData = upcomingTasks.map(task => {
        const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        const dueDate = dueDateMatch ? dueDateMatch[1] : '';
        const daysLeft = Math.ceil((new Date(dueDate) - today) / (1000 * 60 * 60 * 24));

        const isSubTask = task.text.trim().startsWith('@');
        const taskIcon = isSubTask ? '📎' : '📋';

        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? projectMatch[1] : '未分类';

        let statusIcon = '';
        if (daysLeft === 0) statusIcon = '🔥 今天到期';
        else if (daysLeft === 1) statusIcon = '🟡 明天到期';
        else statusIcon = `🟢 ${daysLeft}天后到期`;

        let cleanText = task.text
            .replace(/🍅\d+/g, '')
            .replace(/⏫|🔼|🔽/g, '')
            .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
            .replace(/#project\/[^\s#]+/g, '')
            .replace(/#重要|#不重要/g, '')
            .trim();

        return [
            `${taskIcon} ${cleanText}`,
            dueDate,
            statusIcon,
            project,
            `[[${task.path}|查看]]`
        ];
    });

    dv.table(["任务", "截止日期", "状态", "项目", "来源"], upcomingData);
} else {
    dv.paragraph("📅 未来3天暂无到期任务");
}
```

## 📝 任务格式规范

### 🎯 标准任务格式

#### 主任务格式
```markdown
- [ ] 主任务名称 📅 YYYY-MM-DD 🍅x ⏫/🔼/🔽 #project/项目名 #重要/不重要
```

#### 子任务格式
```markdown
- [ ] @主任务名称 子任务描述 📅 YYYY-MM-DD 🍅x ⏫/🔼/🔽 #project/项目名
```

### 📋 格式说明

| 元素 | 说明 | 示例 | 必需 |
|------|------|------|------|
| `- [ ]` | 任务标记 | `- [ ]` | ✅ |
| 任务名称 | 任务描述 | `完成年度报告` | ✅ |
| `📅` | 截止日期 | `📅 2025-05-20` | 🔶 |
| `🍅x` | 番茄钟数量 | `🍅3` | 🔶 |
| `⏫🔼🔽` | 优先级 | `⏫` 最高, `🔼` 高, `🔽` 低 | 🔶 |
| `#project/` | 项目标签 | `#project/工作` | 🔶 |
| `#重要/#不重要` | 重要性标签 | `#重要` | 🔶 |
| `@主任务名` | 子任务关联 | `@完成年度报告` | 🔶 |

### 🌟 示例模板

```markdown
# 工作项目示例
- [ ] 完成年度报告 📅 2025-05-20 🍅5 ⏫ #project/工作 #重要
- [ ] @完成年度报告 收集Q1数据 📅 2025-05-15 🍅2 🔼 #project/工作
- [ ] @完成年度报告 分析数据趋势 📅 2025-05-17 🍅2 🔼 #project/工作
- [ ] @完成年度报告 撰写报告初稿 📅 2025-05-19 🍅1 🔽 #project/工作

# 学习项目示例
- [ ] 学习Python编程 📅 2025-06-01 🍅10 🔼 #project/学习 #重要
- [ ] @学习Python编程 完成基础语法学习 📅 2025-05-25 🍅3 ⏫ #project/学习
- [ ] @学习Python编程 练习编程题目 📅 2025-05-30 🍅4 🔼 #project/学习

# 生活任务示例
- [ ] 整理房间 📅 2025-05-18 🍅2 🔽 #project/生活 #不重要
- [ ] 购买生活用品 📅 2025-05-19 🍅1 🔽 #project/生活 #不重要
```

## 🛠️ 快速操作工具

```dataviewjs
// 创建快速操作界面
const container = this.container;

// 创建操作按钮区域
const toolsContainer = document.createElement('div');
toolsContainer.style.display = 'flex';
toolsContainer.style.flexWrap = 'wrap';
toolsContainer.style.gap = '10px';
toolsContainer.style.marginBottom = '20px';

// 任务移动工具
const moveToolsTitle = document.createElement('h4');
moveToolsTitle.textContent = '📋 任务时间调整工具';
container.appendChild(moveToolsTitle);

const moveDescription = document.createElement('p');
moveDescription.innerHTML = '选择任务并调整其截止日期，实现类似"拖拽"的效果：';
container.appendChild(moveDescription);

// 任务选择器
const taskSelector = document.createElement('select');
taskSelector.id = 'task-move-selector';
taskSelector.style.width = '300px';
taskSelector.style.margin = '5px';
taskSelector.style.padding = '5px';

// 目标日期选择器
const targetDateInput = document.createElement('input');
targetDateInput.type = 'date';
targetDateInput.id = 'target-date-input';
targetDateInput.style.margin = '5px';
targetDateInput.style.padding = '5px';

// 快速日期按钮
const todayBtn = document.createElement('button');
todayBtn.textContent = '今天';
todayBtn.style.margin = '5px';
todayBtn.style.padding = '5px 10px';
todayBtn.style.backgroundColor = '#ff6b6b';
todayBtn.style.color = 'white';
todayBtn.style.border = 'none';
todayBtn.style.borderRadius = '4px';
todayBtn.style.cursor = 'pointer';

const tomorrowBtn = document.createElement('button');
tomorrowBtn.textContent = '明天';
tomorrowBtn.style.margin = '5px';
tomorrowBtn.style.padding = '5px 10px';
tomorrowBtn.style.backgroundColor = '#ffa726';
tomorrowBtn.style.color = 'white';
tomorrowBtn.style.border = 'none';
tomorrowBtn.style.borderRadius = '4px';
tomorrowBtn.style.cursor = 'pointer';

const nextWeekBtn = document.createElement('button');
nextWeekBtn.textContent = '下周';
nextWeekBtn.style.margin = '5px';
nextWeekBtn.style.padding = '5px 10px';
nextWeekBtn.style.backgroundColor = '#66bb6a';
nextWeekBtn.style.color = 'white';
nextWeekBtn.style.border = 'none';
nextWeekBtn.style.borderRadius = '4px';
nextWeekBtn.style.cursor = 'pointer';

// 执行移动按钮
const moveBtn = document.createElement('button');
moveBtn.textContent = '📅 调整日期';
moveBtn.style.margin = '5px';
moveBtn.style.padding = '5px 15px';
moveBtn.style.backgroundColor = '#4CAF50';
moveBtn.style.color = 'white';
moveBtn.style.border = 'none';
moveBtn.style.borderRadius = '4px';
moveBtn.style.cursor = 'pointer';

// 结果显示区域
const resultArea = document.createElement('div');
resultArea.id = 'move-result-area';
resultArea.style.marginTop = '15px';
resultArea.style.padding = '10px';
resultArea.style.backgroundColor = '#f5f5f5';
resultArea.style.borderRadius = '4px';
resultArea.style.display = 'none';

// 添加元素到页面
container.appendChild(document.createTextNode('选择任务: '));
container.appendChild(taskSelector);
container.appendChild(document.createElement('br'));
container.appendChild(document.createElement('br'));
container.appendChild(document.createTextNode('目标日期: '));
container.appendChild(targetDateInput);
container.appendChild(todayBtn);
container.appendChild(tomorrowBtn);
container.appendChild(nextWeekBtn);
container.appendChild(document.createElement('br'));
container.appendChild(document.createElement('br'));
container.appendChild(moveBtn);
container.appendChild(resultArea);

// 初始化任务选项
async function initTaskOptions() {
    const allTasks = dv.pages().file.tasks.where(t => !t.completed).array();

    // 添加默认选项
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.text = '-- 选择要调整的任务 --';
    taskSelector.appendChild(defaultOption);

    allTasks.forEach((task, index) => {
        const option = document.createElement('option');
        option.value = JSON.stringify({
            path: task.path,
            text: task.text,
            line: task.line
        });

        // 显示任务信息
        const isSubTask = task.text.trim().startsWith('@');
        const taskIcon = isSubTask ? '📎' : '📋';
        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? `[${projectMatch[1]}]` : '';

        let cleanText = task.text
            .replace(/🍅\d+/g, '')
            .replace(/⏫|🔼|🔽/g, '')
            .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
            .replace(/#project\/[^\s#]+/g, '')
            .replace(/#重要|#不重要/g, '')
            .trim();

        option.text = `${taskIcon} ${cleanText} ${project}`;
        taskSelector.appendChild(option);
    });
}

// 快速日期设置函数
function setQuickDate(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    targetDateInput.value = date.toISOString().split('T')[0];
}

// 事件监听器
todayBtn.addEventListener('click', () => setQuickDate(0));
tomorrowBtn.addEventListener('click', () => setQuickDate(1));
nextWeekBtn.addEventListener('click', () => setQuickDate(7));

moveBtn.addEventListener('click', () => {
    const selectedTask = taskSelector.value;
    const targetDate = targetDateInput.value;

    if (!selectedTask || !targetDate) {
        resultArea.innerHTML = '<p style="color: red;">❌ 请选择任务和目标日期</p>';
        resultArea.style.display = 'block';
        return;
    }

    try {
        const taskInfo = JSON.parse(selectedTask);
        resultArea.innerHTML = `
            <h4>📋 任务调整指南</h4>
            <p><strong>任务:</strong> ${taskInfo.text}</p>
            <p><strong>文件:</strong> ${taskInfo.path}</p>
            <p><strong>目标日期:</strong> ${targetDate}</p>
            <div style="background: #e3f2fd; padding: 10px; border-radius: 4px; margin-top: 10px;">
                <h5>🔧 手动操作步骤:</h5>
                <ol>
                    <li>点击 <a href="obsidian://open?vault=${encodeURIComponent(app.vault.getName())}&file=${encodeURIComponent(taskInfo.path)}" style="color: #1976d2; text-decoration: underline;">打开文件</a></li>
                    <li>找到任务行: <code>${taskInfo.text}</code></li>
                    <li>将日期修改为: <code>📅 ${targetDate}</code></li>
                    <li>保存文件</li>
                </ol>
                <p><em>💡 提示: 由于Obsidian限制，暂时无法自动修改文件，需要手动操作</em></p>
            </div>
        `;
        resultArea.style.display = 'block';
    } catch (error) {
        resultArea.innerHTML = '<p style="color: red;">❌ 操作失败，请重试</p>';
        resultArea.style.display = 'block';
    }
});

// 初始化
initTaskOptions();
```

## 🔧 批量操作工具

```dataviewjs
// 创建批量操作界面
const batchContainer = this.container;

// 批量操作标题
const batchTitle = document.createElement('h4');
batchTitle.textContent = '🔧 批量任务操作';
batchContainer.appendChild(batchTitle);

// 任务选择区域
const taskListContainer = document.createElement('div');
taskListContainer.style.maxHeight = '300px';
taskListContainer.style.overflowY = 'auto';
taskListContainer.style.border = '1px solid #ddd';
taskListContainer.style.borderRadius = '4px';
taskListContainer.style.padding = '10px';
taskListContainer.style.marginBottom = '15px';
taskListContainer.style.backgroundColor = '#f8f9fa';

// 全选/取消全选
const selectAllContainer = document.createElement('div');
selectAllContainer.style.marginBottom = '10px';
selectAllContainer.innerHTML = `
    <label style="font-weight: bold;">
        <input type="checkbox" id="select-all-tasks" style="margin-right: 5px;">
        全选/取消全选
    </label>
    <span id="selected-count" style="margin-left: 15px; color: #666;">已选择: 0 个任务</span>
`;

// 批量操作按钮区域
const batchActionsContainer = document.createElement('div');
batchActionsContainer.style.display = 'flex';
batchActionsContainer.style.flexWrap = 'wrap';
batchActionsContainer.style.gap = '10px';
batchActionsContainer.style.marginBottom = '15px';

// 批量操作按钮 - 直接绑定事件
const batchButtons = [
    {
        id: 'batch-set-priority',
        text: '🔥 设置优先级',
        color: '#dc3545',
        handler: function() {
            alert('设置优先级按钮被点击了！'); // 测试用
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                alert('请先选择要操作的任务');
                return;
            }

            const priority = prompt('请选择优先级:\n1. ⏫ 最高优先级\n2. 🔼 高优先级\n3. 🔽 低优先级\n4. 移除优先级\n\n请输入数字 1-4:');

            if (priority) {
                const priorityMap = {
                    '1': '⏫',
                    '2': '🔼',
                    '3': '🔽',
                    '4': '移除'
                };

                const selectedPriority = priorityMap[priority];
                if (selectedPriority) {
                    const instructions = `
                        <ol>
                            <li>逐个打开以下文件</li>
                            <li>找到对应的任务行</li>
                            <li>${selectedPriority === '移除' ? '移除现有的优先级标记 (⏫🔼🔽)' : `添加或替换优先级标记为: ${selectedPriority}`}</li>
                            <li>保存文件</li>
                        </ol>
                    `;
                    showBatchResult(`设置优先级为 ${selectedPriority}`, selectedTasks, instructions);
                }
            }
        }
    },
    {
        id: 'batch-set-project',
        text: '📁 设置项目',
        color: '#007bff',
        handler: function() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                alert('请先选择要操作的任务');
                return;
            }

            const project = prompt('请输入项目名称 (例如: 工作、学习、生活):');
            if (project) {
                const instructions = `
                    <ol>
                        <li>逐个打开以下文件</li>
                        <li>找到对应的任务行</li>
                        <li>添加或替换项目标签为: <code>#project/${project}</code></li>
                        <li>保存文件</li>
                    </ol>
                `;
                showBatchResult(`设置项目为 ${project}`, selectedTasks, instructions);
            }
        }
    },
    {
        id: 'batch-set-date',
        text: '📅 设置日期',
        color: '#28a745',
        handler: function() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                alert('请先选择要操作的任务');
                return;
            }

            const date = prompt('请输入截止日期 (格式: YYYY-MM-DD):');
            if (date && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
                const instructions = `
                    <ol>
                        <li>逐个打开以下文件</li>
                        <li>找到对应的任务行</li>
                        <li>添加或替换日期标记为: <code>📅 ${date}</code></li>
                        <li>保存文件</li>
                    </ol>
                `;
                showBatchResult(`设置截止日期为 ${date}`, selectedTasks, instructions);
            } else if (date) {
                alert('日期格式不正确，请使用 YYYY-MM-DD 格式');
            }
        }
    },
    {
        id: 'batch-set-tomato',
        text: '🍅 设置番茄钟',
        color: '#fd7e14',
        handler: function() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                alert('请先选择要操作的任务');
                return;
            }

            const tomatoes = prompt('请输入番茄钟数量 (1-20):');
            if (tomatoes && /^\d+$/.test(tomatoes) && parseInt(tomatoes) >= 1 && parseInt(tomatoes) <= 20) {
                const instructions = `
                    <ol>
                        <li>逐个打开以下文件</li>
                        <li>找到对应的任务行</li>
                        <li>添加或替换番茄钟标记为: <code>🍅${tomatoes}</code></li>
                        <li>保存文件</li>
                    </ol>
                `;
                showBatchResult(`设置番茄钟为 ${tomatoes} 个`, selectedTasks, instructions);
            } else if (tomatoes) {
                alert('请输入 1-20 之间的数字');
            }
        }
    },
    {
        id: 'batch-mark-important',
        text: '⭐ 标记重要',
        color: '#ffc107',
        handler: function() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                alert('请先选择要操作的任务');
                return;
            }

            const instructions = `
                <ol>
                    <li>逐个打开以下文件</li>
                    <li>找到对应的任务行</li>
                    <li>添加重要性标记: <code>#重要</code></li>
                    <li>保存文件</li>
                </ol>
            `;
            showBatchResult('标记为重要任务', selectedTasks, instructions);
        }
    },
    {
        id: 'batch-complete',
        text: '✅ 标记完成',
        color: '#6f42c1',
        handler: function() {
            const selectedTasks = getSelectedTasks();
            if (selectedTasks.length === 0) {
                alert('请先选择要操作的任务');
                return;
            }

            const instructions = `
                <ol>
                    <li>逐个打开以下文件</li>
                    <li>找到对应的任务行</li>
                    <li>将 <code>- [ ]</code> 改为 <code>- [x]</code></li>
                    <li>保存文件</li>
                </ol>
            `;
            showBatchResult('标记为已完成', selectedTasks, instructions);
        }
    }
];

batchButtons.forEach(btn => {
    const button = document.createElement('button');
    button.id = btn.id;
    button.textContent = btn.text;
    button.style.padding = '8px 12px';
    button.style.backgroundColor = btn.color;
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '4px';
    button.style.cursor = 'pointer';
    button.style.fontSize = '0.9em';

    // 直接绑定事件处理器
    button.addEventListener('click', btn.handler);

    batchActionsContainer.appendChild(button);
});

// 操作结果显示区域
const batchResultContainer = document.createElement('div');
batchResultContainer.id = 'batch-operation-results';
batchResultContainer.style.marginTop = '15px';
batchResultContainer.style.display = 'none';

// 添加到页面
batchContainer.appendChild(selectAllContainer);
batchContainer.appendChild(taskListContainer);
batchContainer.appendChild(batchActionsContainer);
batchContainer.appendChild(batchResultContainer);

// 初始化任务列表
async function initBatchTaskList() {
    const allTasks = dv.pages().file.tasks.where(t => !t.completed).array();

    taskListContainer.innerHTML = '';

    if (allTasks.length === 0) {
        taskListContainer.innerHTML = '<p style="text-align: center; color: #666;">暂无未完成任务</p>';
        return;
    }

    allTasks.forEach((task, index) => {
        const taskItem = document.createElement('div');
        taskItem.style.display = 'flex';
        taskItem.style.alignItems = 'center';
        taskItem.style.padding = '8px';
        taskItem.style.marginBottom = '5px';
        taskItem.style.backgroundColor = 'white';
        taskItem.style.borderRadius = '4px';
        taskItem.style.border = '1px solid #e9ecef';

        const isSubTask = task.text.trim().startsWith('@');
        const taskIcon = isSubTask ? '📎' : '📋';

        // 提取任务信息
        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? projectMatch[1] : '';

        const dueDateMatch = task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        const dueDate = dueDateMatch ? dueDateMatch[1] : '';

        let priorityIcon = '';
        if (task.text.includes('⏫')) priorityIcon = '⏫';
        else if (task.text.includes('🔼')) priorityIcon = '🔼';
        else if (task.text.includes('🔽')) priorityIcon = '🔽';

        let cleanText = task.text
            .replace(/🍅\d+/g, '')
            .replace(/⏫|🔼|🔽/g, '')
            .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
            .replace(/#project\/[^\s#]+/g, '')
            .replace(/#重要|#不重要/g, '')
            .trim();

        taskItem.innerHTML = `
            <input type="checkbox" class="task-checkbox" data-task-index="${index}"
                   data-task-path="${task.path}" data-task-text="${encodeURIComponent(task.text)}"
                   style="margin-right: 10px;">
            <div style="flex: 1;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                    <span style="font-size: 1.1em;">${taskIcon}</span>
                    <span style="font-weight: bold;">${cleanText}</span>
                    ${priorityIcon ? `<span>${priorityIcon}</span>` : ''}
                </div>
                <div style="font-size: 0.8em; color: #666; display: flex; gap: 15px;">
                    ${dueDate ? `<span>📅 ${dueDate}</span>` : '<span>📅 无日期</span>'}
                    ${project ? `<span>📁 ${project}</span>` : '<span>📁 未分类</span>'}
                    <span>📂 ${task.path}</span>
                </div>
            </div>
        `;

        taskListContainer.appendChild(taskItem);
    });

    // 延迟更新选择计数，确保DOM元素已完全创建
    setTimeout(() => {
        updateSelectedCount();
    }, 50);
}

// 更新选择计数
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.task-checkbox');
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');

    // 安全更新选择计数
    const selectedCountElement = document.getElementById('selected-count');
    if (selectedCountElement) {
        selectedCountElement.textContent = `已选择: ${selectedCheckboxes.length} 个任务`;
    }

    // 更新全选状态
    const selectAllCheckbox = document.getElementById('select-all-tasks');
    if (selectAllCheckbox) {
        if (selectedCheckboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (selectedCheckboxes.length === checkboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
}

// 监听任务复选框变化
taskListContainer.addEventListener('change', function(e) {
    if (e.target.classList.contains('task-checkbox')) {
        updateSelectedCount();
    }
});

// 全选/取消全选功能 - 延迟添加事件监听器
setTimeout(() => {
    const selectAllElement = document.getElementById('select-all-tasks');
    if (selectAllElement) {
        selectAllElement.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.task-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });
    }
}, 100);

// 获取选中的任务
function getSelectedTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    return Array.from(selectedCheckboxes).map(checkbox => ({
        path: checkbox.dataset.taskPath,
        text: decodeURIComponent(checkbox.dataset.taskText),
        index: parseInt(checkbox.dataset.taskIndex)
    }));
}

// 显示批量操作结果
function showBatchResult(operation, selectedTasks, instructions) {
    const resultDiv = document.getElementById('batch-operation-results');

    let html = `
        <div style="background: #e3f2fd; padding: 15px; border-radius: 6px; border-left: 4px solid #2196f3;">
            <h4>📋 批量操作: ${operation}</h4>
            <p><strong>影响任务数:</strong> ${selectedTasks.length} 个</p>
            <div style="background: white; padding: 10px; border-radius: 4px; margin: 10px 0;">
                <h5>🔧 操作指南:</h5>
                ${instructions}
            </div>
            <details style="margin-top: 10px;">
                <summary style="cursor: pointer; font-weight: bold;">📝 查看受影响的任务列表</summary>
                <div style="margin-top: 10px; max-height: 200px; overflow-y: auto;">
    `;

    selectedTasks.forEach(task => {
        html += `
            <div style="padding: 5px; border-bottom: 1px solid #eee;">
                <div style="font-weight: bold;">${task.text}</div>
                <div style="font-size: 0.8em; color: #666;">📂 ${task.path}</div>
            </div>
        `;
    });

    html += `
                </div>
            </details>
        </div>
    `;

    resultDiv.innerHTML = html;
    resultDiv.style.display = 'block';
}

// 事件监听器已在按钮创建时直接绑定

// 延迟初始化批量任务列表，确保所有DOM元素都已创建
setTimeout(() => {
    initBatchTaskList();
}, 300);
```

## 📈 任务效率分析

```dataviewjs
// 获取最近30天的任务数据
const thirtyDaysAgo = new Date();
thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

const recentTasks = dv.pages().file.tasks
    .where(t => {
        if (!t.completed) return false;
        // 这里可以根据完成日期筛选，但Tasks插件可能没有完成日期字段
        // 暂时显示所有已完成任务
        return true;
    });

// 效率统计
let efficiencyStats = {
    totalCompleted: 0,
    withTomatoes: 0,
    totalTomatoesCompleted: 0,
    byPriority: { high: 0, medium: 0, low: 0, none: 0 },
    byProject: {}
};

recentTasks.forEach(task => {
    efficiencyStats.totalCompleted++;

    const tomatoMatch = task.text.match(/🍅(\d+)/);
    if (tomatoMatch) {
        efficiencyStats.withTomatoes++;
        efficiencyStats.totalTomatoesCompleted += parseInt(tomatoMatch[1]);
    }

    // 按优先级统计
    if (task.text.includes('⏫')) efficiencyStats.byPriority.high++;
    else if (task.text.includes('🔼')) efficiencyStats.byPriority.medium++;
    else if (task.text.includes('🔽')) efficiencyStats.byPriority.low++;
    else efficiencyStats.byPriority.none++;

    // 按项目统计
    const projectMatch = task.text.match(/#project\/([^\s#]+)/);
    const project = projectMatch ? projectMatch[1] : '未分类';
    efficiencyStats.byProject[project] = (efficiencyStats.byProject[project] || 0) + 1;
});

// 显示效率分析
dv.header(3, "📊 任务完成效率");

if (efficiencyStats.totalCompleted > 0) {
    const tomatoCompletionRate = efficiencyStats.withTomatoes > 0 ?
        Math.round((efficiencyStats.withTomatoes / efficiencyStats.totalCompleted) * 100) : 0;

    dv.table(
        ["指标", "数值", "说明"],
        [
            ["✅ 总完成任务", efficiencyStats.totalCompleted, "已完成的任务总数"],
            ["🍅 番茄钟任务", efficiencyStats.withTomatoes, "设置了番茄钟的已完成任务"],
            ["📈 番茄钟使用率", `${tomatoCompletionRate}%`, "使用番茄钟规划的任务比例"],
            ["🍅 总完成番茄钟", efficiencyStats.totalTomatoesCompleted, "已完成任务的番茄钟总数"]
        ]
    );

    // 优先级完成分布
    dv.header(4, "⚡ 优先级完成分布");
    const priorityCompletionData = [
        ["⏫ 最高优先级", efficiencyStats.byPriority.high, "█".repeat(Math.min(15, efficiencyStats.byPriority.high))],
        ["🔼 高优先级", efficiencyStats.byPriority.medium, "█".repeat(Math.min(15, efficiencyStats.byPriority.medium))],
        ["🔽 低优先级", efficiencyStats.byPriority.low, "█".repeat(Math.min(15, efficiencyStats.byPriority.low))],
        ["➖ 无优先级", efficiencyStats.byPriority.none, "█".repeat(Math.min(15, efficiencyStats.byPriority.none))]
    ];

    dv.table(["优先级", "完成数量", "分布"], priorityCompletionData);

    // 项目完成分布
    if (Object.keys(efficiencyStats.byProject).length > 0) {
        dv.header(4, "📁 项目完成分布");
        const projectCompletionData = Object.entries(efficiencyStats.byProject)
            .sort((a, b) => b[1] - a[1])
            .map(([project, count]) => [
                project,
                count,
                "█".repeat(Math.min(10, count))
            ]);

        dv.table(["项目", "完成数量", "分布"], projectCompletionData);
    }
} else {
    dv.paragraph("📊 暂无已完成任务数据");
}
```

## 🎯 番茄钟目标提醒

```dataviewjs
// 番茄钟目标设置和提醒
const targetContainer = this.container;

// 创建目标设置界面
const targetTitle = document.createElement('h4');
targetTitle.textContent = '🎯 番茄钟目标管理';
targetContainer.appendChild(targetTitle);

// 目标设置区域
const targetSettingContainer = document.createElement('div');
targetSettingContainer.style.backgroundColor = '#f8f9fa';
targetSettingContainer.style.padding = '15px';
targetSettingContainer.style.borderRadius = '8px';
targetSettingContainer.style.marginBottom = '20px';

targetSettingContainer.innerHTML = `
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
        <div>
            <label style="font-weight: bold; display: block; margin-bottom: 5px;">每日目标 🍅:</label>
            <input type="number" id="daily-tomato-target" min="1" max="20" value="8" style="width: 100%; padding: 5px;">
        </div>
        <div>
            <label style="font-weight: bold; display: block; margin-bottom: 5px;">每周目标 🍅:</label>
            <input type="number" id="weekly-tomato-target" min="1" max="100" value="40" style="width: 100%; padding: 5px;">
        </div>
        <div>
            <label style="font-weight: bold; display: block; margin-bottom: 5px;">每月目标 🍅:</label>
            <input type="number" id="monthly-tomato-target" min="1" max="500" value="160" style="width: 100%; padding: 5px;">
        </div>
    </div>
    <div style="display: flex; gap: 10px;">
        <button id="save-targets" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">💾 保存目标</button>
        <button id="load-targets" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">📥 加载目标</button>
        <button id="reset-targets" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">🔄 重置默认</button>
    </div>
`;

// 目标达成情况显示区域
const targetStatusContainer = document.createElement('div');
targetStatusContainer.id = 'target-status-container';
targetStatusContainer.style.marginTop = '20px';

targetContainer.appendChild(targetSettingContainer);
targetContainer.appendChild(targetStatusContainer);

// 加载保存的目标
function loadSavedTargets() {
    const savedTargets = localStorage.getItem('tomato-targets');
    if (savedTargets) {
        const targets = JSON.parse(savedTargets);
        document.getElementById('daily-tomato-target').value = targets.daily || 8;
        document.getElementById('weekly-tomato-target').value = targets.weekly || 40;
        document.getElementById('monthly-tomato-target').value = targets.monthly || 160;
    }
}

// 保存目标
function saveTargets() {
    const targets = {
        daily: parseInt(document.getElementById('daily-tomato-target').value),
        weekly: parseInt(document.getElementById('weekly-tomato-target').value),
        monthly: parseInt(document.getElementById('monthly-tomato-target').value)
    };
    localStorage.setItem('tomato-targets', JSON.stringify(targets));
    alert('目标已保存！');
    updateTargetStatus();
}

// 重置为默认目标
function resetTargets() {
    document.getElementById('daily-tomato-target').value = 8;
    document.getElementById('weekly-tomato-target').value = 40;
    document.getElementById('monthly-tomato-target').value = 160;
    updateTargetStatus();
}

// 更新目标达成状态
function updateTargetStatus() {
    const dailyTarget = parseInt(document.getElementById('daily-tomato-target').value);
    const weeklyTarget = parseInt(document.getElementById('weekly-tomato-target').value);
    const monthlyTarget = parseInt(document.getElementById('monthly-tomato-target').value);

    // 计算实际完成情况
    const today = new Date().toISOString().split('T')[0];
    const allTasks = dv.pages().file.tasks;

    // 今日完成的番茄钟
    let dailyCompleted = 0;
    allTasks.where(t => t.completed && t.text.includes(`📅 ${today}`)).forEach(task => {
        const tomatoMatch = task.text.match(/🍅(\d+)/);
        if (tomatoMatch) {
            dailyCompleted += parseInt(tomatoMatch[1]);
        }
    });

    // 本周完成的番茄钟
    const now = new Date();
    const dayOfWeek = now.getDay();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - dayOfWeek);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    const startDate = startOfWeek.toISOString().split('T')[0];
    const endDate = endOfWeek.toISOString().split('T')[0];

    let weeklyCompleted = 0;
    allTasks.where(t => {
        if (!t.completed) return false;
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch) return false;
        const dueDate = dueDateMatch[1];
        return dueDate >= startDate && dueDate <= endDate;
    }).forEach(task => {
        const tomatoMatch = task.text.match(/🍅(\d+)/);
        if (tomatoMatch) {
            weeklyCompleted += parseInt(tomatoMatch[1]);
        }
    });

    // 本月完成的番茄钟
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const startOfMonth = new Date(year, month, 1);
    const endOfMonth = new Date(year, month + 1, 0);

    const monthStart = startOfMonth.toISOString().split('T')[0];
    const monthEnd = endOfMonth.toISOString().split('T')[0];

    let monthlyCompleted = 0;
    allTasks.where(t => {
        if (!t.completed) return false;
        const dueDateMatch = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        if (!dueDateMatch) return false;
        const dueDate = dueDateMatch[1];
        return dueDate >= monthStart && dueDate <= monthEnd;
    }).forEach(task => {
        const tomatoMatch = task.text.match(/🍅(\d+)/);
        if (tomatoMatch) {
            monthlyCompleted += parseInt(tomatoMatch[1]);
        }
    });

    // 计算完成率
    const dailyRate = Math.round((dailyCompleted / dailyTarget) * 100);
    const weeklyRate = Math.round((weeklyCompleted / weeklyTarget) * 100);
    const monthlyRate = Math.round((monthlyCompleted / monthlyTarget) * 100);

    // 生成状态显示
    function getStatusBadge(rate) {
        if (rate >= 100) return '<span style="background: #28a745; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">✅ 已达成</span>';
        if (rate >= 80) return '<span style="background: #ffc107; color: black; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">🟡 接近目标</span>';
        if (rate >= 50) return '<span style="background: #fd7e14; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">🟠 进行中</span>';
        return '<span style="background: #dc3545; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">🔴 需努力</span>';
    }

    function getProgressBar(rate) {
        const width = Math.min(100, rate);
        let color = '#dc3545';
        if (rate >= 100) color = '#28a745';
        else if (rate >= 80) color = '#ffc107';
        else if (rate >= 50) color = '#fd7e14';

        return `
            <div style="background: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden; margin: 5px 0;">
                <div style="background: ${color}; height: 100%; width: ${width}%; transition: width 0.3s ease;"></div>
            </div>
        `;
    }

    const statusHtml = `
        <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px;">
            <h4 style="margin-top: 0;">📊 目标达成情况</h4>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                        <h5 style="margin: 0;">📅 今日目标</h5>
                        ${getStatusBadge(dailyRate)}
                    </div>
                    <div style="font-size: 1.2em; margin-bottom: 5px;">
                        <strong>${dailyCompleted}</strong> / ${dailyTarget} 🍅 (${dailyRate}%)
                    </div>
                    ${getProgressBar(dailyRate)}
                    <div style="font-size: 0.9em; color: #666;">
                        还需 ${Math.max(0, dailyTarget - dailyCompleted)} 个🍅达成今日目标
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                        <h5 style="margin: 0;">📅 本周目标</h5>
                        ${getStatusBadge(weeklyRate)}
                    </div>
                    <div style="font-size: 1.2em; margin-bottom: 5px;">
                        <strong>${weeklyCompleted}</strong> / ${weeklyTarget} 🍅 (${weeklyRate}%)
                    </div>
                    ${getProgressBar(weeklyRate)}
                    <div style="font-size: 0.9em; color: #666;">
                        还需 ${Math.max(0, weeklyTarget - weeklyCompleted)} 个🍅达成本周目标
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                        <h5 style="margin: 0;">📅 本月目标</h5>
                        ${getStatusBadge(monthlyRate)}
                    </div>
                    <div style="font-size: 1.2em; margin-bottom: 5px;">
                        <strong>${monthlyCompleted}</strong> / ${monthlyTarget} 🍅 (${monthlyRate}%)
                    </div>
                    ${getProgressBar(monthlyRate)}
                    <div style="font-size: 0.9em; color: #666;">
                        还需 ${Math.max(0, monthlyTarget - monthlyCompleted)} 个🍅达成本月目标
                    </div>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 6px; border-left: 4px solid #2196f3;">
                <h5 style="margin-top: 0;">💡 智能建议</h5>
                ${generateSmartSuggestions(dailyCompleted, dailyTarget, weeklyCompleted, weeklyTarget, monthlyCompleted, monthlyTarget)}
            </div>
        </div>
    `;

    document.getElementById('target-status-container').innerHTML = statusHtml;
}

// 生成智能建议
function generateSmartSuggestions(dailyCompleted, dailyTarget, weeklyCompleted, weeklyTarget, monthlyCompleted, monthlyTarget) {
    const suggestions = [];

    // 今日建议
    const dailyRemaining = dailyTarget - dailyCompleted;
    if (dailyRemaining > 0) {
        if (dailyRemaining <= 2) {
            suggestions.push(`🔥 今日还需 ${dailyRemaining} 个🍅就能达成目标，加油冲刺！`);
        } else if (dailyRemaining <= 4) {
            suggestions.push(`⚡ 今日还需 ${dailyRemaining} 个🍅，建议选择2-3个中等任务完成。`);
        } else {
            suggestions.push(`📋 今日还需 ${dailyRemaining} 个🍅，建议优先处理高优先级任务。`);
        }
    } else {
        suggestions.push(`🎉 恭喜！今日目标已达成，可以考虑处理一些额外任务或休息。`);
    }

    // 本周建议
    const weeklyRate = (weeklyCompleted / weeklyTarget) * 100;
    if (weeklyRate < 50) {
        suggestions.push(`⚠️ 本周进度较慢，建议增加每日番茄钟数量或延长工作时间。`);
    } else if (weeklyRate >= 100) {
        suggestions.push(`🏆 本周目标已超额完成，表现优秀！`);
    }

    // 本月建议
    const monthlyRate = (monthlyCompleted / monthlyTarget) * 100;
    if (monthlyRate < 30) {
        suggestions.push(`📈 本月进度需要加速，建议重新评估任务优先级和时间分配。`);
    }

    return suggestions.map(s => `<p style="margin: 5px 0;">• ${s}</p>`).join('');
}

// 初始化
loadSavedTargets();
updateTargetStatus();

// 事件监听器 - 延迟添加
setTimeout(() => {
    const saveTargetsBtn = document.getElementById('save-targets');
    const loadTargetsBtn = document.getElementById('load-targets');
    const resetTargetsBtn = document.getElementById('reset-targets');

    if (saveTargetsBtn) saveTargetsBtn.addEventListener('click', saveTargets);
    if (loadTargetsBtn) loadTargetsBtn.addEventListener('click', loadSavedTargets);
    if (resetTargetsBtn) resetTargetsBtn.addEventListener('click', resetTargets);
}, 100);

// 自动刷新（每分钟检查一次）
setInterval(updateTargetStatus, 60000);
```

## 📤 数据导出导入

```dataviewjs
// 创建导出导入界面
const exportImportContainer = this.container;

// 创建功能区容器
const functionsContainer = document.createElement('div');
functionsContainer.style.display = 'flex';
functionsContainer.style.gap = '20px';
functionsContainer.style.marginBottom = '20px';
functionsContainer.style.padding = '20px';
functionsContainer.style.backgroundColor = '#f8f9fa';
functionsContainer.style.borderRadius = '8px';

// 导出功能
const exportSection = document.createElement('div');
exportSection.style.flex = '1';
exportSection.innerHTML = `
    <h4 style="margin-top: 0;">📤 导出任务数据</h4>
    <p>导出所有任务数据为JSON格式文件。</p>
    <div style="display: flex; gap: 10px;">
        <button id="export-all" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
            📥 导出所有任务
        </button>
        <button id="export-selected" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
            📋 导出选中任务
        </button>
    </div>
`;

// 导入功能
const importSection = document.createElement('div');
importSection.style.flex = '1';
importSection.innerHTML = `
    <h4 style="margin-top: 0;">📥 导入任务数据</h4>
    <p>从JSON文件导入任务数据。</p>
    <div style="display: flex; gap: 10px;">
        <input type="file" id="import-file" accept=".json" style="display: none;">
        <button id="import-trigger" style="padding: 8px 16px; background: #6f42c1; color: white; border: none; border-radius: 4px; cursor: pointer;">
            📂 选择文件导入
        </button>
    </div>
`;

// 状态显示区域
const statusSection = document.createElement('div');
statusSection.id = 'export-import-status';
statusSection.style.marginTop = '20px';
statusSection.style.padding = '10px';
statusSection.style.borderRadius = '4px';
statusSection.style.display = 'none';

// 添加到页面
functionsContainer.appendChild(exportSection);
functionsContainer.appendChild(importSection);
exportImportContainer.appendChild(functionsContainer);
exportImportContainer.appendChild(statusSection);

// 导出功能实现
function exportTaskData(selectedOnly = false) {
    try {
        let tasks = dv.pages().file.tasks.array();

        if (selectedOnly) {
            // 获取选中的任务
            const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
            const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.taskIndex);
            tasks = tasks.filter((_, index) => selectedIds.includes(index.toString()));
        }

        // 转换任务数据
        const exportData = tasks.map(task => ({
            text: task.text,
            path: task.path,
            completed: task.completed,
            line: task.line,
            // 提取额外信息
            priority: task.text.match(/(⏫|🔼|🔽)/) ? task.text.match(/(⏫|🔼|🔽)/)[1] : null,
            dueDate: task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/) ? task.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/)[1] : null,
            tomatoes: task.text.match(/🍅(\d+)/) ? parseInt(task.text.match(/🍅(\d+)/)[1]) : 0,
            project: task.text.match(/#project\/([^\s#]+)/) ? task.text.match(/#project\/([^\s#]+)/)[1] : null,
            isImportant: task.text.includes('#重要'),
            isSubTask: task.text.trim().startsWith('@')
        }));

        // 创建下载
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `obsidian-tasks-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        // 显示成功消息
        showStatus('✅ 数据导出成功！', 'success');
    } catch (error) {
        console.error('导出失败:', error);
        showStatus('❌ 导出失败: ' + error.message, 'error');
    }
}

// 导入功能实现
function importTaskData(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedData = JSON.parse(e.target.result);

            // 验证导入数据
            if (!Array.isArray(importedData)) {
                throw new Error('无效的数据格式');
            }

            // 显示导入预览
            showImportPreview(importedData);
        } catch (error) {
            console.error('导入失败:', error);
            showStatus('❌ 导入失败: ' + error.message, 'error');
        }
    };
    reader.readAsText(file);
}

// 显示导入预览
function showImportPreview(data) {
    const statusDiv = document.getElementById('export-import-status');
    statusDiv.style.display = 'block';
    statusDiv.style.backgroundColor = '#fff';
    statusDiv.style.border = '1px solid #dee2e6';
    statusDiv.style.padding = '15px';

    let html = `
        <h4>📋 导入预览</h4>
        <p>共 ${data.length} 个任务待导入</p>
        <div style="max-height: 300px; overflow-y: auto; margin: 10px 0;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr>
                        <th style="padding: 8px; border-bottom: 2px solid #dee2e6;">任务</th>
                        <th style="padding: 8px; border-bottom: 2px solid #dee2e6;">项目</th>
                        <th style="padding: 8px; border-bottom: 2px solid #dee2e6;">截止日期</th>
                    </tr>
                </thead>
                <tbody>
    `;

    data.forEach(task => {
        html += `
            <tr>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${task.text}</td>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${task.project || '未分类'}</td>
                <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${task.dueDate || '无'}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
        <div style="margin-top: 15px;">
            <button id="confirm-import" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                ✅ 确认导入
            </button>
            <button id="cancel-import" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                ❌ 取消
            </button>
        </div>
    `;

    statusDiv.innerHTML = html;

    // 添加确认和取消按钮事件
    document.getElementById('confirm-import').addEventListener('click', () => {
        // 这里实现实际的导入逻辑
        showStatus('✅ 数据导入成功！', 'success');
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    });

    document.getElementById('cancel-import').addEventListener('click', () => {
        statusDiv.style.display = 'none';
    });
}

// 状态显示函数
function showStatus(message, type) {
    const statusDiv = document.getElementById('export-import-status');
    statusDiv.style.display = 'block';
    statusDiv.style.backgroundColor = type === 'success' ? '#d4edda' : '#f8d7da';
    statusDiv.style.color = type === 'success' ? '#155724' : '#721c24';
    statusDiv.style.padding = '10px';
    statusDiv.style.borderRadius = '4px';
    statusDiv.innerHTML = message;

    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 3000);
}

// 添加事件监听器
setTimeout(() => {
    const exportAllBtn = document.getElementById('export-all');
    const exportSelectedBtn = document.getElementById('export-selected');
    const importTriggerBtn = document.getElementById('import-trigger');
    const importFileInput = document.getElementById('import-file');

    if (exportAllBtn) {
        exportAllBtn.addEventListener('click', () => exportTaskData(false));
    }

    if (exportSelectedBtn) {
        exportSelectedBtn.addEventListener('click', () => exportTaskData(true));
    }

    if (importTriggerBtn && importFileInput) {
        importTriggerBtn.addEventListener('click', () => importFileInput.click());
        importFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                importTaskData(e.target.files[0]);
            }
        });
    }
}, 100);
```

## 📊 高级数据可视化

```dataviewjs
// 创建图表容器
const chartContainer = this.container;

// 添加Chart.js CDN
const script = document.createElement('script');
script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
script.onload = initCharts;
document.head.appendChild(script);

// 创建图表布局
const chartsLayout = document.createElement('div');
chartsLayout.style.display = 'grid';
chartsLayout.style.gridTemplateColumns = 'repeat(auto-fit, minmax(400px, 1fr))';
chartsLayout.style.gap = '20px';
chartsLayout.style.marginTop = '20px';

// 创建三个图表容器
const trendChartContainer = document.createElement('div');
trendChartContainer.style.padding = '20px';
trendChartContainer.style.backgroundColor = 'white';
trendChartContainer.style.borderRadius = '8px';
trendChartContainer.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
trendChartContainer.innerHTML = '<canvas id="trend-chart"></canvas>';

const distributionChartContainer = document.createElement('div');
distributionChartContainer.style.padding = '20px';
distributionChartContainer.style.backgroundColor = 'white';
distributionChartContainer.style.borderRadius = '8px';
distributionChartContainer.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
distributionChartContainer.innerHTML = '<canvas id="distribution-chart"></canvas>';

const progressChartContainer = document.createElement('div');
progressChartContainer.style.padding = '20px';
progressChartContainer.style.backgroundColor = 'white';
progressChartContainer.style.borderRadius = '8px';
progressChartContainer.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
progressChartContainer.innerHTML = '<canvas id="progress-chart"></canvas>';

// 添加到布局
chartsLayout.appendChild(trendChartContainer);
chartsLayout.appendChild(distributionChartContainer);
chartsLayout.appendChild(progressChartContainer);

// 添加到主容器
chartContainer.appendChild(chartsLayout);

// 初始化图表
function initCharts() {
    // 获取任务数据
    const allTasks = dv.pages().file.tasks;
    const completedTasks = allTasks.where(t => t.completed);
    const pendingTasks = allTasks.where(t => !t.completed);

    // 1. 任务完成趋势图
    createTrendChart();

    // 2. 任务分布图
    createDistributionChart();

    // 3. 项目进度图
    createProgressChart();
}

// 创建任务完成趋势图
function createTrendChart() {
    const ctx = document.getElementById('trend-chart').getContext('2d');

    // 获取最近7天的数据
    const last7Days = [];
    const completedCounts = [];
    const pendingCounts = [];

    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        last7Days.push(dateStr);

        // 统计当天完成和待完成的任务
        const dayTasks = dv.pages().file.tasks.where(t => {
            const taskDate = t.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
            return taskDate && taskDate[1] === dateStr;
        });

        completedCounts.push(dayTasks.where(t => t.completed).length);
        pendingCounts.push(dayTasks.where(t => !t.completed).length);
    }

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: last7Days.map(date => date.split('-').slice(1).join('-')),
            datasets: [
                {
                    label: '已完成任务',
                    data: completedCounts,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: true
                },
                {
                    label: '待完成任务',
                    data: pendingCounts,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '任务完成趋势 (最近7天)'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

// 创建任务分布图
function createDistributionChart() {
    const ctx = document.getElementById('distribution-chart').getContext('2d');

    // 统计不同类型的任务数量
    const tasks = dv.pages().file.tasks;
    const distribution = {
        important: tasks.where(t => t.text.includes('#重要')).length,
        unimportant: tasks.where(t => t.text.includes('#不重要')).length,
        highPriority: tasks.where(t => t.text.includes('⏫')).length,
        mediumPriority: tasks.where(t => t.text.includes('🔼')).length,
        lowPriority: tasks.where(t => t.text.includes('🔽')).length,
        noTomato: tasks.where(t => !t.text.match(/🍅\d+/)).length,
        withTomato: tasks.where(t => t.text.match(/🍅\d+/)).length
    };

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['重要任务', '不重要任务', '高优先级', '中优先级', '低优先级', '无番茄钟', '有番茄钟'],
            datasets: [{
                label: '任务分布',
                data: [
                    distribution.important,
                    distribution.unimportant,
                    distribution.highPriority,
                    distribution.mediumPriority,
                    distribution.lowPriority,
                    distribution.noTomato,
                    distribution.withTomato
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgb(54, 162, 235)',
                pointBackgroundColor: 'rgb(54, 162, 235)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgb(54, 162, 235)'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '任务属性分布'
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

// 创建项目进度图
function createProgressChart() {
    const ctx = document.getElementById('progress-chart').getContext('2d');

    // 统计各项目的完成情况
    const tasks = dv.pages().file.tasks;
    const projects = {};

    tasks.forEach(task => {
        const projectMatch = task.text.match(/#project\/([^\s#]+)/);
        if (projectMatch) {
            const project = projectMatch[1];
            if (!projects[project]) {
                projects[project] = { total: 0, completed: 0 };
            }
            projects[project].total++;
            if (task.completed) {
                projects[project].completed++;
            }
        }
    });

    const projectNames = Object.keys(projects);
    const completionRates = projectNames.map(project =>
        Math.round((projects[project].completed / projects[project].total) * 100)
    );

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: projectNames,
            datasets: [{
                label: '项目完成率 (%)',
                data: completionRates,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgb(75, 192, 192)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '项目进度'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}
```

## 🚀 性能优化

```dataviewjs
// 创建缓存管理器
const CacheManager = {
    cache: new Map(),
    ttl: 5 * 60 * 1000, // 5分钟缓存时间

    set: function(key, value) {
        this.cache.set(key, {
            value: value,
            timestamp: Date.now()
        });
    },

    get: function(key) {
        const item = this.cache.get(key);
        if (!item) return null;

        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }

        return item.value;
    },

    clear: function() {
        this.cache.clear();
    }
};

// 虚拟滚动实现
class VirtualScroller {
    constructor(container, items, itemHeight) {
        this.container = container;
        this.items = items;
        this.itemHeight = itemHeight;

        this.visibleItems = Math.ceil(container.clientHeight / itemHeight);
        this.totalHeight = items.length * itemHeight;
        this.scrollTop = 0;

        this.setupContainer();
        this.render();
        this.attachEvents();
    }

    setupContainer() {
        this.container.style.position = 'relative';
        this.container.style.overflow = 'auto';

        this.content = document.createElement('div');
        this.content.style.height = `${this.totalHeight}px`;
        this.content.style.position = 'relative';

        this.container.appendChild(this.content);
    }

    render() {
        // 清除现有内容
        while (this.content.firstChild) {
            this.content.removeChild(this.content.firstChild);
        }

        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(startIndex + this.visibleItems + 2, this.items.length);

        for (let i = startIndex; i < endIndex; i++) {
            const item = this.items[i];
            const element = this.createItemElement(item);
            element.style.position = 'absolute';
            element.style.top = `${i * this.itemHeight}px`;
            element.style.width = '100%';
            this.content.appendChild(element);
        }
    }

    createItemElement(item) {
        const element = document.createElement('div');
        element.style.height = `${this.itemHeight}px`;
        element.style.padding = '8px';
        element.style.borderBottom = '1px solid #eee';
        element.style.backgroundColor = 'white';

        // 任务内容
        const isSubTask = item.text.trim().startsWith('@');
        const taskIcon = isSubTask ? '📎' : '📋';

        const projectMatch = item.text.match(/#project\/([^\s#]+)/);
        const project = projectMatch ? projectMatch[1] : '未分类';

        const dueDateMatch = item.text.match(/📅\s*(\d{4}-\d{2}-\d{2})/);
        const dueDate = dueDateMatch ? dueDateMatch[1] : '';

        let cleanText = item.text
            .replace(/🍅\d+/g, '')
            .replace(/⏫|🔼|🔽/g, '')
            .replace(/📅\s*\d{4}-\d{2}-\d{2}/g, '')
            .replace(/#project\/[^\s#]+/g, '')
            .replace(/#重要|#不重要/g, '')
            .trim();

        element.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 1.2em;">${taskIcon}</span>
                <span style="flex: 1;">${cleanText}</span>
                ${dueDate ? `<span style="color: #666;">📅 ${dueDate}</span>` : ''}
                <span style="color: #666;">📁 ${project}</span>
            </div>
        `;

        return element;
    }

    attachEvents() {
        this.container.addEventListener('scroll', () => {
            requestAnimationFrame(() => {
                this.scrollTop = this.container.scrollTop;
                this.render();
            });
        });

        // 监听容器大小变化
        const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
                this.visibleItems = Math.ceil(entry.contentRect.height / this.itemHeight);
                this.render();
            }
        });

        resizeObserver.observe(this.container);
    }
}

// 创建性能优化演示界面
const container = this.container;

// 创建性能监控面板
const perfPanel = document.createElement('div');
perfPanel.style.backgroundColor = '#f8f9fa';
perfPanel.style.padding = '15px';
perfPanel.style.borderRadius = '8px';
perfPanel.style.marginBottom = '20px';

perfPanel.innerHTML = `
    <h4 style="margin-top: 0;">🔍 性能监控</h4>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div style="background: white; padding: 10px; border-radius: 4px;">
            <div style="font-weight: bold;">缓存状态</div>
            <div id="cache-status">-</div>
        </div>
        <div style="background: white; padding: 10px; border-radius: 4px;">
            <div style="font-weight: bold;">渲染性能</div>
            <div id="render-performance">-</div>
        </div>
        <div style="background: white; padding: 10px; border-radius: 4px;">
            <div style="font-weight: bold;">内存使用</div>
            <div id="memory-usage">-</div>
        </div>
    </div>
`;

// 创建虚拟滚动演示区域
const scrollDemo = document.createElement('div');
scrollDemo.style.marginTop = '20px';
scrollDemo.innerHTML = `
    <h4>📜 虚拟滚动演示</h4>
    <div id="virtual-scroll-container" style="height: 400px; border: 1px solid #dee2e6; border-radius: 4px;">
    </div>
`;

// 添加到页面
container.appendChild(perfPanel);
container.appendChild(scrollDemo);

// 初始化虚拟滚动
const tasks = dv.pages().file.tasks.array();
const virtualScroller = new VirtualScroller(
    document.getElementById('virtual-scroll-container'),
    tasks,
    60 // 每个项目高度
);

// 更新性能指标
function updatePerformanceMetrics() {
    // 更新缓存状态
    const cacheStatus = document.getElementById('cache-status');
    cacheStatus.textContent = `${CacheManager.cache.size} 个缓存项`;

    // 更新渲染性能
    const renderPerf = document.getElementById('render-performance');
    const fps = Math.round(1000 / (performance.now() - lastFrame));
    renderPerf.textContent = `${fps} FPS`;
    lastFrame = performance.now();

    // 更新内存使用
    const memoryUsage = document.getElementById('memory-usage');
    if (performance.memory) {
        const usedHeap = Math.round(performance.memory.usedJSHeapSize / (1024 * 1024));
        memoryUsage.textContent = `${usedHeap}MB`;
    } else {
        memoryUsage.textContent = '不可用';
    }

    requestAnimationFrame(updatePerformanceMetrics);
}

let lastFrame = performance.now();
updatePerformanceMetrics();

// 使用缓存优化数据查询
function getCachedTaskData(key, callback) {
    const cached = CacheManager.get(key);
    if (cached) {
        return cached;
    }

    const data = callback();
    CacheManager.set(key, data);
    return data;
}

// 示例：使用缓存获取任务统计
function getTaskStats() {
    return getCachedTaskData('taskStats', () => {
        const tasks = dv.pages().file.tasks;
        return {
            total: tasks.length,
            completed: tasks.where(t => t.completed).length,
            pending: tasks.where(t => !t.completed).length
        };
    });
}