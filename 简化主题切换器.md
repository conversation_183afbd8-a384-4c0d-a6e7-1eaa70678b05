# 🎨 简化版主题切换器

> 稳定可靠的主题切换工具，解决DOM访问问题

## 🚀 使用方法

将下面的代码复制到您的项目仪表盘文件开头：

```dataviewjs
// 🎨 简化版主题切换器
const container = this.container;
container.innerHTML = '';

// 主题配置
const THEMES = {
    'milk-tea': {
        name: '🧋 治愈系奶茶风',
        primaryBg: '#FFF9F5',
        secondaryBg: '#F7F3F0',
        textPrimary: '#8B7355',
        textSecondary: '#A0896B',
        goalColor: '#E8D5A5',
        areaColor: '#A8C8A8',
        shadowColor: 'rgba(139, 115, 85, 0.08)'
    },
    'morandi': {
        name: '🎨 Morandi莫兰迪风',
        primaryBg: '#F5F1EB',
        secondaryBg: '#E8E2D5',
        textPrimary: '#8B7D6B',
        textSecondary: '#A69B8A',
        goalColor: '#D4C4A8',
        areaColor: '#B8C5C1',
        shadowColor: 'rgba(139, 125, 107, 0.1)'
    },
    'dark': {
        name: '🌙 深色商务风',
        primaryBg: '#1E1E1E',
        secondaryBg: '#2D2D2D',
        textPrimary: '#E5E5E5',
        textSecondary: '#B3B3B3',
        goalColor: '#FF8C42',
        areaColor: '#4ECDC4',
        shadowColor: 'rgba(0, 0, 0, 0.3)'
    },
    'mint': {
        name: '🌿 清新薄荷风',
        primaryBg: '#F0FFF4',
        secondaryBg: '#E6FFFA',
        textPrimary: '#2D5A3D',
        textSecondary: '#4A7C59',
        goalColor: '#98D8C8',
        areaColor: '#6BCF7F',
        shadowColor: 'rgba(45, 90, 61, 0.1)'
    },
    'violet': {
        name: '💜 优雅紫罗兰风',
        primaryBg: '#F8F6FF',
        secondaryBg: '#F0EBFF',
        textPrimary: '#5D4E75',
        textSecondary: '#7A6B8D',
        goalColor: '#B19CD9',
        areaColor: '#9B8CE8',
        shadowColor: 'rgba(93, 78, 117, 0.1)'
    },
    'orange': {
        name: '🧡 温暖橙色风',
        primaryBg: '#FFF8F0',
        secondaryBg: '#FFF2E6',
        textPrimary: '#8B4513',
        textSecondary: '#A0522D',
        goalColor: '#FFB366',
        areaColor: '#FF9A56',
        shadowColor: 'rgba(139, 69, 19, 0.1)'
    }
};

// 创建主题切换器界面
const themeSwitcher = document.createElement('div');
themeSwitcher.style.cssText = `
    background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(139, 115, 85, 0.08);
    border: 1px solid rgba(232, 180, 160, 0.2);
`;

// 创建标题
const title = document.createElement('div');
title.style.cssText = `
    text-align: center;
    margin-bottom: 15px;
`;
title.innerHTML = `
    <h3 style="margin: 0; color: #8B7355; font-size: 1.1em;">🎨 主题切换器</h3>
    <p style="margin: 5px 0 0 0; color: #A0896B; font-size: 0.9em;">点击下方按钮切换不同风格主题</p>
`;

// 创建按钮容器
const buttonContainer = document.createElement('div');
buttonContainer.style.cssText = `
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-bottom: 15px;
`;

// 创建主题按钮
Object.keys(THEMES).forEach(themeKey => {
    const theme = THEMES[themeKey];
    const button = document.createElement('button');
    button.style.cssText = `
        background: linear-gradient(135deg, ${theme.goalColor} 0%, ${theme.areaColor} 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 8px 16px;
        cursor: pointer;
        font-size: 0.85em;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px ${theme.shadowColor};
    `;
    button.textContent = theme.name;
    
    // 悬浮效果
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px) scale(1.05)';
        this.style.boxShadow = `0 4px 16px ${theme.shadowColor}`;
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = `0 2px 8px ${theme.shadowColor}`;
    });
    
    // 点击切换主题
    button.addEventListener('click', function() {
        applyTheme(theme);
        showNotification(`已切换到 ${theme.name}`);
        // 保存主题到本地存储
        localStorage.setItem('dashboard-theme', themeKey);
    });
    
    buttonContainer.appendChild(button);
});

// 创建功能按钮
const actionContainer = document.createElement('div');
actionContainer.style.cssText = `
    text-align: center;
`;

const resetButton = document.createElement('button');
resetButton.style.cssText = `
    background: #A8C8A8;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 0.8em;
    margin: 0 4px;
`;
resetButton.textContent = '🔄 重置为默认';
resetButton.addEventListener('click', function() {
    applyTheme(THEMES['milk-tea']);
    showNotification('已重置为默认主题');
    localStorage.setItem('dashboard-theme', 'milk-tea');
});

// 组装界面
themeSwitcher.appendChild(title);
themeSwitcher.appendChild(buttonContainer);
actionContainer.appendChild(resetButton);
themeSwitcher.appendChild(actionContainer);
container.appendChild(themeSwitcher);

// 应用主题函数
function applyTheme(theme) {
    // 更新切换器本身的样式
    themeSwitcher.style.background = `linear-gradient(135deg, ${theme.primaryBg} 0%, ${theme.secondaryBg} 100%)`;
    themeSwitcher.style.boxShadow = `0 4px 20px ${theme.shadowColor}`;
    
    // 更新标题颜色
    const titleElement = title.querySelector('h3');
    const subtitleElement = title.querySelector('p');
    if (titleElement) titleElement.style.color = theme.textPrimary;
    if (subtitleElement) subtitleElement.style.color = theme.textSecondary;
    
    // 创建全局样式变量
    let styleElement = document.getElementById('dashboard-theme-style');
    if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = 'dashboard-theme-style';
        document.head.appendChild(styleElement);
    }
    
    // 应用全局主题样式
    styleElement.textContent = `
        :root {
            --dashboard-primary-bg: ${theme.primaryBg};
            --dashboard-secondary-bg: ${theme.secondaryBg};
            --dashboard-text-primary: ${theme.textPrimary};
            --dashboard-text-secondary: ${theme.textSecondary};
            --dashboard-goal-color: ${theme.goalColor};
            --dashboard-area-color: ${theme.areaColor};
            --dashboard-shadow-color: ${theme.shadowColor};
        }
    `;
}

// 显示通知函数
function showNotification(message) {
    // 移除已存在的通知
    const existingNotification = document.querySelector('.theme-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    const notification = document.createElement('div');
    notification.className = 'theme-notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 1000;
        font-size: 0.9em;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 触发动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // 3秒后自动移除
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}

// 页面加载时应用保存的主题
const savedTheme = localStorage.getItem('dashboard-theme');
if (savedTheme && THEMES[savedTheme]) {
    applyTheme(THEMES[savedTheme]);
} else {
    applyTheme(THEMES['milk-tea']);
}
```

## 🎯 使用说明

### 1. **安装步骤**
1. 复制上面的完整代码
2. 粘贴到您的 `项目仪表盘.md` 文件的**最开头**
3. 保存文件并刷新

### 2. **功能特点**
- ✅ **稳定可靠** - 解决了DOM访问错误
- ✅ **即时切换** - 点击按钮立即生效
- ✅ **自动保存** - 主题选择保存到本地存储
- ✅ **视觉反馈** - 切换时显示通知消息
- ✅ **响应式设计** - 适配不同屏幕尺寸

### 3. **主题列表**
- 🧋 治愈系奶茶风（默认）
- 🎨 Morandi莫兰迪风
- 🌙 深色商务风
- 🌿 清新薄荷风
- 💜 优雅紫罗兰风
- 🧡 温暖橙色风

## 🔧 自定义主题

### 添加新主题
在 `THEMES` 对象中添加新配置：
```javascript
'my-theme': {
    name: '🌟 我的主题',
    primaryBg: '#您的主背景色',
    secondaryBg: '#您的次背景色',
    textPrimary: '#您的主文字色',
    textSecondary: '#您的次文字色',
    goalColor: '#您的目标按钮色',
    areaColor: '#您的领域按钮色',
    shadowColor: 'rgba(0,0,0,0.1)'
}
```

### 修改现有主题
直接修改 `THEMES` 对象中对应主题的颜色值即可。

## 💡 故障排除

如果遇到问题：
1. **确保代码完整** - 复制完整的代码块
2. **检查位置** - 代码应该在文件最开头
3. **刷新页面** - 保存后刷新Obsidian页面
4. **检查控制台** - 按F12查看是否有错误信息

---

🎨 **这个简化版本更加稳定可靠，解决了DOM访问问题！**
