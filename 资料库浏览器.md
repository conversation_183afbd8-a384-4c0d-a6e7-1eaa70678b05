---
created: 2025-05-25T16:45
updated: 2025-05-25T16:45
tags:
  - type/dashboard
  - resource/browser
---

# 📚 资料库浏览器

> 🗃️ 学习资料管理中心，知识资源一站式访问

## 🚀 快速操作

```dataviewjs
// 创建快速操作面板
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F8F5FF 0%, #FFF5F8 100%);
    border-radius: 16px;
    border: 2px solid rgba(208, 196, 232, 0.3);
`;

const quickActions = [
    {
        title: '添加资料',
        icon: '➕',
        desc: '新增学习资料',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates/Resource Template', '', false);
            }
        },
        color: '#D0C4E8'
    },
    {
        title: 'Prompt合集',
        icon: '☁️',
        desc: '常用提示词库',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Assets/☁️常用Prompt合集', '', false);
            }
        },
        color: '#A5D6E8'
    },
    {
        title: '工具库',
        icon: '🛠️',
        desc: '实用工具集合',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Assets/工具库', '', false);
            }
        },
        color: '#B8D6B8'
    },
    {
        title: '资源模板',
        icon: '📋',
        desc: '查看资源模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates', '', false);
            }
        },
        color: '#E8B4A0'
    }
];

let actionsHTML = '';
quickActions.forEach((action, index) => {
    actionsHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 12px; padding: 18px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action-btn" data-index="${index}">
            <div style="font-size: 2em; margin-bottom: 8px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickActionsContainer.innerHTML = actionsHTML;

// 添加点击事件
setTimeout(() => {
    const actionBtns = quickActionsContainer.querySelectorAll('.quick-action-btn');
    actionBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            quickActions[index].action();
        });

        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 📁 资源文件夹

```dataview
TABLE WITHOUT ID
    file.link AS "📁 资源名称",
    choice(type, type, "📝 未分类") AS "🏷️ 类型",
    choice(category, category, "📚 未分类") AS "📂 分类",
    file.size AS "📊 大小",
    file.mtime AS "📅 修改时间"
FROM "Assets"
WHERE file.name != "README"
SORT file.mtime DESC
```

## 🛠️ 工具资源

```dataviewjs
// 创建工具展示区域
const toolsContainer = document.createElement('div');
toolsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 20px 0;
`;

// 工具分类
const toolCategories = [
    {
        title: 'AI工具',
        icon: '🤖',
        color: '#D0C4E8',
        tools: [
            { name: 'ChatGPT', desc: 'AI对话助手', link: 'https://chat.openai.com' },
            { name: 'Claude', desc: 'Anthropic AI助手', link: 'https://claude.ai' },
            { name: 'Cursor', desc: 'AI编程工具', link: '6_Project Notes/Cursor' },
            { name: 'Prompt合集', desc: '常用提示词', link: 'Assets/☁️常用Prompt合集' }
        ]
    },
    {
        title: '学习工具',
        icon: '📚',
        color: '#B8D6B8',
        tools: [
            { name: 'Obsidian', desc: '知识管理工具', link: 'https://obsidian.md' },
            { name: 'Anki', desc: '间隔重复记忆', link: 'https://apps.ankiweb.net' },
            { name: 'Notion', desc: '全能工作空间', link: 'https://notion.so' },
            { name: '文献笔记', desc: '学习资料管理', link: '文献笔记浏览器' }
        ]
    },
    {
        title: '效率工具',
        icon: '⚡',
        color: '#E8D5A5',
        tools: [
            { name: '番茄钟', desc: '专注时间管理', link: '番茄钟历史数据仪表盘' },
            { name: '任务管理', desc: '任务跟踪系统', link: '任务仪表盘-治愈系奶茶风' },
            { name: '项目管理', desc: '项目进度跟踪', link: '项目仪表盘' },
            { name: '精力管理', desc: '健康状态监控', link: '6_Project Notes/精力/精力仪表盘' }
        ]
    }
];

toolCategories.forEach(category => {
    const categoryDiv = document.createElement('div');
    categoryDiv.style.cssText = `
        background: rgba(255,255,255,0.8);
        border-radius: 16px;
        padding: 20px;
        border: 2px solid ${category.color}30;
        box-shadow: 0 4px 16px rgba(139, 115, 85, 0.1);
    `;

    let toolsHTML = '';
    category.tools.forEach(tool => {
        const isExternal = tool.link.startsWith('http');
        toolsHTML += `
            <div style="margin: 10px 0; padding: 12px; background: rgba(248,246,243,0.5); 
                        border-radius: 10px; cursor: pointer; transition: all 0.2s ease;"
                 class="tool-item" data-link="${tool.link}" data-external="${isExternal}">
                <div style="font-weight: 600; color: #8B7355; font-size: 1em; margin-bottom: 4px;">
                    ${tool.name}
                </div>
                <div style="font-size: 0.85em; color: #A0896B;">
                    ${tool.desc}
                </div>
                ${isExternal ? '<div style="font-size: 0.75em; color: #999; margin-top: 4px;">🔗 外部链接</div>' : ''}
            </div>
        `;
    });

    categoryDiv.innerHTML = `
        <div style="display: flex; align-items: center; margin-bottom: 15px; padding-bottom: 10px; 
                    border-bottom: 2px solid ${category.color}30;">
            <span style="font-size: 1.8em; margin-right: 12px;">${category.icon}</span>
            <h3 style="margin: 0; color: ${category.color}; font-weight: 600; font-size: 1.2em;">
                ${category.title}
            </h3>
        </div>
        ${toolsHTML}
    `;

    toolsContainer.appendChild(categoryDiv);
});

// 添加点击事件
setTimeout(() => {
    const toolItems = toolsContainer.querySelectorAll('.tool-item');
    toolItems.forEach(item => {
        item.addEventListener('click', function() {
            const link = this.getAttribute('data-link');
            const isExternal = this.getAttribute('data-external') === 'true';
            
            if (isExternal) {
                window.open(link, '_blank');
            } else if (app && app.workspace) {
                app.workspace.openLinkText(link, '', false);
            }
        });

        item.addEventListener('mouseenter', function() {
            this.style.background = `rgba(${this.closest('div').querySelector('h3').style.color.match(/\d+/g).join(',')}, 0.2)`;
            this.style.transform = 'translateX(8px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(248,246,243,0.5)';
            this.style.transform = 'translateX(0)';
        });
    });
}, 100);

this.container.appendChild(toolsContainer);
```

## 📊 资源统计

```dataviewjs
// 获取所有资源文件
const allAssets = dv.pages('"Assets"').where(p => p.file.name !== "README");

// 统计数据
const totalAssets = allAssets.length;

// 按类型统计
const typeGroups = {};
allAssets.forEach(p => {
    const type = p.type || "未分类";
    typeGroups[type] = (typeGroups[type] || 0) + 1;
});

// 创建统计容器
const statsContainer = document.createElement('div');
statsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F8F5FF 0%, #FFF5F8 100%);
    border-radius: 16px;
    border: 2px solid rgba(208, 196, 232, 0.2);
`;

const stats = [
    { label: '总资源', value: totalAssets, icon: '📚', color: '#8B7355' },
    { label: 'AI工具', value: 4, icon: '🤖', color: '#D0C4E8' },
    { label: '学习工具', value: 4, icon: '📖', color: '#B8D6B8' },
    { label: '效率工具', value: 4, icon: '⚡', color: '#E8D5A5' }
];

let statsHTML = '';
stats.forEach(stat => {
    statsHTML += `
        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); 
                    border-radius: 12px; border: 1px solid ${stat.color}20;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${stat.icon}</div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${stat.color}; margin-bottom: 4px;">
                ${stat.value}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500;">
                ${stat.label}
            </div>
        </div>
    `;
});

statsContainer.innerHTML = statsHTML;
this.container.appendChild(statsContainer);
```

---

<div style="text-align: center; margin-top: 30px; padding: 15px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 12px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600; margin-bottom: 6px;">
        🗃️ 资源管理小贴士
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        定期整理和更新资源库，让工具为学习和工作赋能
    </div>
</div>
