{"rules": [{"name": "TaskDecomposer", "description": "将复杂任务分解为可管理步骤并追踪执行进度", "regex": "@TaskDecomposer\\s+(.*)", "example": "@TaskDecomposer 实现一个Vue Router导航系统", "replacement": "我将帮您将\"$1\"任务分解为可管理的步骤，并跟踪进度。\n\n# 任务计划：$1\n创建日期：{{currentDate}}\n\n## 任务目标\n[请简要描述任务的最终目标]\n\n## 任务分解\n1. [第一步名称]\n   - 预计时间：[估计]\n   - 依赖项：[如果有]\n   - 详细说明：[具体操作]\n   \n2. [第二步名称]\n   - 预计时间：[估计]\n   - 依赖项：[如果有]\n   - 详细说明：[具体操作]\n   \n3. [第三步名称]\n   - 预计时间：[估计]\n   - 依赖项：[如果有]\n   - 详细说明：[具体操作]\n\n[继续添加更多步骤...]\n\n## 执行记录格式\n\n当开始执行任务时，我将按照以下格式记录进度：\n\n# 任务进度：$1\n\n## 执行记录\n\n### [日期 时间]\n- Step: [当前步骤名称]\n- Modifications: [具体修改内容]\n- Change Summary: [变更摘要]\n- Reason: [进行此修改的原因]\n- Blockers: [遇到的阻碍，如果没有则为None]\n- Status: [Success/Failed/In Progress]\n\n### [日期 时间]\n[下一步骤记录...]\n\n请告诉我是否需要调整任务计划，或者我们可以开始执行第一个步骤。", "context": {"currentDate": {"type": "date", "format": "YYYY-MM-DD"}}}, {"name": "TaskProgress", "description": "记录任务进度", "regex": "@TaskProgress\\s+(.*)", "example": "@TaskProgress 完成了安装Vue Router步骤", "replacement": "### {{currentDateTime}}\n- Step: $1\n- Modifications: [具体修改内容]\n- Change Summary: [变更摘要]\n- Reason: [进行此修改的原因]\n- Blockers: [遇到的阻碍，如果没有则为None]\n- Status: [待更新]\n\n请更新上述内容并确认状态为Success/Failed/In Progress。", "context": {"currentDateTime": {"type": "date", "format": "YYYY-MM-DD HH:mm"}}}]}