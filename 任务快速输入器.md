---
tags:
  - type/tool
  - tool/task-input
created: 2025-05-16T18:00
updated: 2025-05-16T18:00
---

# 📝 任务快速输入器

> 快速创建标准格式的任务，无需记忆复杂格式

## 🎯 任务生成器

```dataviewjs
// 创建任务输入界面
const container = this.container;
container.innerHTML = '';

// 创建标题
const title = document.createElement('h2');
title.textContent = '📝 任务快速生成器';
title.style.cssText = 'text-align: center; color: #333; margin-bottom: 30px;';
container.appendChild(title);

// 创建表单容器
const formContainer = document.createElement('div');
formContainer.style.cssText = `
    max-width: 600px;
    margin: 0 auto;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

// 表单HTML
formContainer.innerHTML = `
    <div style="display: grid; gap: 20px;">
        <!-- 任务名称 -->
        <div>
            <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333;">
                📋 任务名称 *
            </label>
            <input type="text" id="task-name" placeholder="例如：学习AI课程"
                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
        </div>

        <!-- 截止日期 -->
        <div>
            <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333;">
                📅 截止日期
            </label>
            <input type="date" id="task-date"
                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
        </div>

        <!-- 番茄钟数量 -->
        <div>
            <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333;">
                🍅 番茄钟数量
            </label>
            <select id="task-tomato" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                <option value="">不设置</option>
                <option value="1">🍅1 (25分钟)</option>
                <option value="2">🍅2 (50分钟)</option>
                <option value="3">🍅3 (75分钟)</option>
                <option value="4">🍅4 (100分钟)</option>
                <option value="5">🍅5 (125分钟)</option>
                <option value="6">🍅6 (150分钟)</option>
                <option value="8">🍅8 (200分钟)</option>
            </select>
        </div>

        <!-- 优先级 -->
        <div>
            <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333;">
                ⚡ 优先级
            </label>
            <select id="task-priority" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                <option value="">普通优先级</option>
                <option value="⏫">⏫ 最高优先级</option>
                <option value="🔼">🔼 高优先级</option>
                <option value="🔽">🔽 低优先级</option>
            </select>
        </div>

        <!-- 项目分类 -->
        <div>
            <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333;">
                📁 项目分类
            </label>
            <select id="task-project" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                <option value="">不分类</option>
                <option value="工作">📊 工作</option>
                <option value="学习">📚 学习</option>
                <option value="生活">🏠 生活</option>
                <option value="健康">💪 健康</option>
                <option value="娱乐">🎮 娱乐</option>
                <option value="社交">👥 社交</option>
                <option value="财务">💰 财务</option>
                <option value="其他">📦 其他</option>
            </select>
        </div>

        <!-- 任务状态 -->
        <div>
            <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333;">
                🔄 任务状态
            </label>
            <select id="task-status" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                <option value="">待办</option>
                <option value="🔄">🔄 进行中</option>
                <option value="⚡">⚡ 进行中</option>
                <option value="🚀">🚀 进行中</option>
                <option value="#进行中">#进行中</option>
            </select>
        </div>

        <!-- 重要性 -->
        <div>
            <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333;">
                ⭐ 重要性
            </label>
            <select id="task-important" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                <option value="">普通</option>
                <option value="#重要">⭐ 重要</option>
                <option value="#不重要">➖ 不重要</option>
            </select>
        </div>

        <!-- 子任务标记 -->
        <div>
            <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333;">
                🔗 子任务标记
            </label>
            <input type="text" id="task-parent" placeholder="例如：学习AI（如果这是子任务）"
                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
            <small style="color: #666; font-size: 14px;">如果这是子任务，请输入主任务名称</small>
        </div>

        <!-- 生成按钮 -->
        <div style="display: flex; gap: 15px; margin-top: 20px;">
            <button id="generate-task" style="flex: 1; padding: 15px; background: #28a745; color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer;">
                🚀 生成任务
            </button>
            <button id="clear-form" style="flex: 1; padding: 15px; background: #6c757d; color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer;">
                🔄 清空表单
            </button>
        </div>
    </div>
`;

container.appendChild(formContainer);

// 结果显示区域
const resultContainer = document.createElement('div');
resultContainer.id = 'task-result';
resultContainer.style.cssText = `
    max-width: 600px;
    margin: 30px auto 0;
    display: none;
`;
container.appendChild(resultContainer);

// 生成任务函数
function generateTask() {
    const taskName = document.getElementById('task-name').value.trim();
    if (!taskName) {
        alert('请输入任务名称！');
        return;
    }

    const date = document.getElementById('task-date').value;
    const tomato = document.getElementById('task-tomato').value;
    const priority = document.getElementById('task-priority').value;
    const project = document.getElementById('task-project').value;
    const status = document.getElementById('task-status').value;
    const important = document.getElementById('task-important').value;
    const parent = document.getElementById('task-parent').value.trim();

    // 构建任务字符串
    let taskText = '- [ ] ';

    // 添加子任务标记
    if (parent) {
        taskText += `@${parent} `;
    }

    // 添加任务名称
    taskText += taskName;

    // 添加状态标记
    if (status) {
        taskText += ` ${status}`;
    }

    // 添加日期
    if (date) {
        taskText += ` 📅 ${date}`;
    }

    // 添加番茄钟
    if (tomato) {
        taskText += ` 🍅${tomato}`;
    }

    // 添加优先级
    if (priority) {
        taskText += ` ${priority}`;
    }

    // 添加项目
    if (project) {
        taskText += ` #project/${project}`;
    }

    // 添加重要性
    if (important) {
        taskText += ` ${important}`;
    }

    // 显示结果
    showResult(taskText);
}

// 显示结果函数
function showResult(taskText) {
    const resultContainer = document.getElementById('task-result');

    resultContainer.innerHTML = `
        <div style="background: #d4edda; border: 2px solid #28a745; border-radius: 12px; padding: 25px;">
            <h3 style="margin-top: 0; color: #155724;">✅ 任务生成成功！</h3>

            <div style="background: white; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #28a745;">
                <strong>生成的任务：</strong><br>
                <code style="font-size: 16px; color: #333; background: #f8f9fa; padding: 8px; border-radius: 4px; display: block; margin-top: 8px; word-break: break-all;">
                    ${taskText}
                </code>
            </div>

            <div style="display: flex; gap: 10px; margin-top: 20px;">
                <button onclick="copyToClipboard('${taskText.replace(/'/g, "\\'")}'); this.textContent='✅ 已复制'; this.style.background='#28a745';"
                        style="flex: 1; padding: 12px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: bold;">
                    📋 复制任务
                </button>
                <button onclick="document.getElementById('task-result').style.display='none'"
                        style="flex: 1; padding: 12px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: bold;">
                    关闭
                </button>
            </div>

            <div style="margin-top: 15px; padding: 12px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
                <strong>💡 使用提示：</strong><br>
                1. 点击"📋 复制任务"复制到剪贴板<br>
                2. 在任意笔记中粘贴即可使用<br>
                3. 建议在日记或项目笔记中添加任务
            </div>
        </div>
    `;

    resultContainer.style.display = 'block';
    resultContainer.scrollIntoView({ behavior: 'smooth' });
}

// 复制到剪贴板函数
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        console.log('任务已复制到剪贴板');
    }).catch(function(err) {
        console.error('复制失败: ', err);
        // 备用方法
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    });
}

// 清空表单函数
function clearForm() {
    const today = new Date().toISOString().split('T')[0];

    const taskName = document.getElementById('task-name');
    const taskDate = document.getElementById('task-date');
    const taskTomato = document.getElementById('task-tomato');
    const taskPriority = document.getElementById('task-priority');
    const taskProject = document.getElementById('task-project');
    const taskStatus = document.getElementById('task-status');
    const taskImportant = document.getElementById('task-important');
    const taskParent = document.getElementById('task-parent');
    const taskResult = document.getElementById('task-result');

    if (taskName) taskName.value = '';
    if (taskDate) taskDate.value = today;
    if (taskTomato) taskTomato.value = '';
    if (taskPriority) taskPriority.value = '';
    if (taskProject) taskProject.value = '';
    if (taskStatus) taskStatus.value = '';
    if (taskImportant) taskImportant.value = '';
    if (taskParent) taskParent.value = '';
    if (taskResult) taskResult.style.display = 'none';
}

// 绑定事件和设置默认值
setTimeout(() => {
    // 设置今天为默认日期
    const today = new Date().toISOString().split('T')[0];
    const dateInput = document.getElementById('task-date');
    if (dateInput) {
        dateInput.value = today;
    }

    // 绑定按钮事件
    const generateBtn = document.getElementById('generate-task');
    const clearBtn = document.getElementById('clear-form');
    const taskNameInput = document.getElementById('task-name');

    if (generateBtn) generateBtn.addEventListener('click', generateTask);
    if (clearBtn) clearBtn.addEventListener('click', clearForm);

    // 回车键生成任务
    if (taskNameInput) {
        taskNameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                generateTask();
            }
        });
    }
}, 200);
```

## 📚 常用任务模板

### 🔥 快速模板

```markdown
<!-- 工作任务 -->
- [ ] 任务名称 📅 2025-05-20 🍅3 ⏫ #project/工作 #重要

<!-- 学习任务 -->
- [ ] 学习内容 📅 2025-05-20 🍅2 🔼 #project/学习

<!-- 生活任务 -->
- [ ] 生活事务 📅 2025-05-20 🍅1 #project/生活

<!-- 进行中任务 -->
- [ ] 正在做的事 🔄 📅 2025-05-20 🍅2 ⏫ #project/工作

<!-- 子任务 -->
- [ ] @主任务名 子任务描述 📅 2025-05-20 🍅1 🔼 #project/工作
```

### 📋 符号速查表

| 符号 | 含义 | 示例 |
|------|------|------|
| 📅 | 截止日期 | 📅 2025-05-20 |
| 🍅 | 番茄钟 | 🍅3 |
| ⏫ | 最高优先级 | ⏫ |
| 🔼 | 高优先级 | 🔼 |
| 🔽 | 低优先级 | 🔽 |
| 🔄 | 进行中 | 🔄 |
| ⚡ | 进行中 | ⚡ |
| 🚀 | 进行中 | 🚀 |
| #project/ | 项目标签 | #project/工作 |
| #重要 | 重要任务 | #重要 |
| @ | 子任务 | @主任务名 |
