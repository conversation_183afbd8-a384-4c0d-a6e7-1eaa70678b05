# 🌸 Obsidian项目仪表盘 - 治愈系奶茶风

> 一个基于Obsidian + Dataview的现代化项目管理仪表盘，采用治愈系奶茶风设计，提供直观的项目状态可视化和便捷的项目管理功能。

## 📋 目录

- [功能特性](#-功能特性)
- [环境要求](#-环境要求)
- [安装部署](#-安装部署)
- [使用指南](#-使用指南)
- [技术架构](#-技术架构)
- [自定义配置](#-自定义配置)
- [故障排除](#-故障排除)
- [开发指南](#-开发指南)

## ✨ 功能特性

### 🌸 项目看板
- **状态概览卡片** - 进行中、已完成、风险、警告项目统计
- **治愈系动画** - 悬浮效果和平滑过渡
- **详细信息展示** - 点击卡片查看项目详情
- **智能健康状态** - 自动计算项目健康度

### 🌿 活跃项目展示
- **网格布局** - 响应式项目卡片展示
- **进度可视化** - 渐变进度条和百分比显示
- **时间管理** - 剩余天数和超期提醒
- **一键跳转** - 直接跳转到项目文件

### 🌺 项目快速筛选
- **智能筛选** - 风险、警告、即将到期、已完成项目
- **动态结果** - 实时筛选结果展示
- **批量操作** - 支持批量查看和管理

### 🎯 目标与领域管理
- **可点击标签** - 目标和领域标签支持直接跳转
- **智能解析** - 支持双链、对象链接等多种格式
- **视觉区分** - 不同颜色区分目标和领域

## 🔧 环境要求

### 必需软件
- **Obsidian** v1.0.0+
- **操作系统** Windows 10+, macOS 10.15+, Linux

### 必需插件
1. **Dataview** v0.5.0+
   - 用于数据查询和动态渲染
   - 需要启用 `Enable JavaScript Queries`

### 推荐插件
1. **Templater** - 项目模板创建
2. **Calendar** - 日期管理
3. **Tag Wrangler** - 标签管理
4. **Advanced Tables** - 表格编辑

## 📦 安装部署

### 步骤1：准备Obsidian环境

1. **下载安装Obsidian**
   ```
   官网：https://obsidian.md/
   选择适合您操作系统的版本
   ```

2. **创建新库或使用现有库**
   ```
   建议创建专门的项目管理库
   或在现有库中创建项目管理区域
   ```

### 步骤2：安装必需插件

1. **安装Dataview插件**
   - 打开 `设置` → `第三方插件`
   - 关闭 `安全模式`
   - 点击 `浏览` → 搜索 `Dataview`
   - 安装并启用插件

2. **配置Dataview设置**
   ```
   设置 → Dataview → 启用以下选项：
   ✅ Enable JavaScript Queries
   ✅ Enable Inline Queries
   ✅ Enable Dataview JS Queries
   ```

### 步骤3：创建文件夹结构

```
您的库/
├── 1_Areas/              # 领域文件夹
├── 2_Goals/              # 目标文件夹
├── 6_Project Notes/      # 项目文件夹
├── 7_Daily Notes/        # 日记文件夹
└── 项目仪表盘.md         # 仪表盘文件
```

### 步骤4：创建项目模板

在 `6_Project Notes` 文件夹中创建项目文件，使用以下YAML格式：

```yaml
---
tags: [type/project]
Status: active  # active, completed, risk, warning, cancelled
Goal: "[[目标名称]]"
Area: "[[领域名称]]"
start_date: 2024-01-01
end_date: 2024-12-31
progress: 50
updated: 2024-01-15
---

# 项目名称

项目描述内容...
```

### 步骤5：部署仪表盘

1. **复制仪表盘代码**
   - 将完整的 `项目仪表盘.md` 文件复制到您的库根目录

2. **验证部署**
   - 打开仪表盘文件
   - 确认所有组件正常显示
   - 测试点击和跳转功能

## 📖 使用指南

### 创建新项目

1. **使用模板创建**
   ```markdown
   在 6_Project Notes 文件夹中创建新文件
   复制项目模板YAML头部
   填写项目信息
   ```

2. **必填字段说明**
   - `tags: [type/project]` - 项目标识（必需）
   - `Status` - 项目状态（必需）
   - `Goal` - 关联目标（推荐）
   - `Area` - 所属领域（推荐）

### 项目状态管理

```yaml
Status选项：
- active: 进行中（绿色）
- completed: 已完成（蓝色）
- risk: 风险（红色）
- warning: 警告（黄色）
- cancelled: 已取消（灰色）
```

### 进度跟踪

```yaml
# 手动设置进度
progress: 75

# 或使用自动计算（基于任务完成度）
progress_auto: 60
```

### 目标与领域链接

```yaml
# 双链格式
Goal: "[[2024年度目标]]"
Area: "[[工作]]"

# 多个领域
Area: ["[[工作]]", "[[学习]]"]
```

## 🏗️ 技术架构

### 核心技术栈
- **Markdown** - 文档结构
- **YAML** - 元数据管理
- **DataviewJS** - 动态查询和渲染
- **JavaScript** - 交互逻辑
- **CSS3** - 样式和动画

### 架构设计模式

1. **模块化组件**
   ```javascript
   🌸 项目看板组件 - 状态统计
   🌿 活跃项目组件 - 项目展示
   🌺 筛选组件 - 交互筛选
   ```

2. **数据驱动渲染**
   ```javascript
   数据查询 → 数据处理 → UI渲染 → 事件绑定
   ```

3. **事件驱动交互**
   ```javascript
   用户操作 → 事件处理 → 状态更新 → UI刷新
   ```

### 性能优化

- **延迟加载** - 组件按需渲染
- **事件委托** - 减少事件监听器
- **DOM复用** - 最小化DOM操作
- **CSS动画** - 硬件加速

## ⚙️ 自定义配置

### 修改颜色主题

```css
/* 在仪表盘文件中找到对应的颜色值进行修改 */

/* 主色调 */
background: linear-gradient(135deg, #FFF9F5 0%, #F7F3F0 100%);

/* 目标按钮颜色 */
background: linear-gradient(135deg, #E8D5A5 0%, #D4C574 100%);

/* 领域按钮颜色 */
background: linear-gradient(135deg, #A8C8A8 0%, #8FB88F 100%);
```

### 调整文件夹路径

```javascript
// 修改查询路径
const projects = dv.pages('"您的项目文件夹"')
    .where(p => p.tags && p.tags.includes("type/project"));
```

### 添加自定义字段

```yaml
---
# 在项目模板中添加新字段
priority: high  # 优先级
team: "开发团队"  # 团队
budget: 10000   # 预算
---
```

## 🔍 故障排除

### 常见问题

1. **仪表盘不显示内容**
   ```
   检查项：
   ✅ Dataview插件已安装并启用
   ✅ JavaScript查询已启用
   ✅ 项目文件包含正确的YAML头部
   ✅ 文件夹路径正确
   ```

2. **点击跳转不工作**
   ```
   检查项：
   ✅ 目标/领域文件存在
   ✅ 文件名不包含特殊字符
   ✅ 双链格式正确
   ```

3. **样式显示异常**
   ```
   检查项：
   ✅ CSS代码完整
   ✅ 浏览器兼容性
   ✅ Obsidian版本更新
   ```

### 调试方法

1. **开启开发者工具**
   ```
   Ctrl+Shift+I (Windows/Linux)
   Cmd+Option+I (macOS)
   ```

2. **查看控制台错误**
   ```javascript
   // 在代码中添加调试信息
   console.log('项目数据:', projects);
   console.log('筛选结果:', filteredProjects);
   ```

## 🚀 开发指南

### 扩展新功能

1. **添加新的筛选条件**
   ```javascript
   // 在筛选函数中添加新的case
   case 'high-priority':
       filteredProjects = allProjects.filter(p => p.priority === "high");
       break;
   ```

2. **创建新的组件**
   ```javascript
   // 新建DataviewJS代码块
   ```dataviewjs
   // 您的组件代码
   ```

### 贡献指南

1. **代码规范**
   - 使用一致的缩进（4空格）
   - 添加必要的注释
   - 遵循命名约定

2. **测试要求**
   - 测试不同的数据格式
   - 验证跨平台兼容性
   - 确保性能稳定

## 📝 Prompt模板

### AI助手对话模板

```
我想要优化我的Obsidian项目仪表盘，当前遇到以下问题：

**环境信息：**
- Obsidian版本：[版本号]
- Dataview版本：[版本号]
- 操作系统：[系统信息]

**问题描述：**
[详细描述您遇到的问题]

**期望效果：**
[描述您希望实现的功能或效果]

**相关代码：**
```javascript
[粘贴相关的代码片段]
```

**错误信息：**
[如果有错误，请粘贴错误信息]

请帮我分析问题并提供解决方案。
```

### 功能请求模板

```
我想要为项目仪表盘添加新功能：

**功能名称：**
[功能的简短名称]

**功能描述：**
[详细描述功能的作用和行为]

**使用场景：**
[说明在什么情况下会使用这个功能]

**设计要求：**
- 视觉风格：[保持治愈系奶茶风/其他要求]
- 交互方式：[点击/悬浮/其他]
- 数据来源：[项目文件/日记/其他]

**参考示例：**
[如果有类似的功能或设计，请提供参考]

请帮我设计并实现这个功能。
```

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，欢迎通过以下方式联系：

- **GitHub Issues** - 技术问题和Bug报告
- **社区论坛** - 使用经验分享
- **邮件反馈** - 功能建议和合作

---

**版本信息：** v1.0.0
**更新日期：** 2024-01-15
**兼容性：** Obsidian v1.0.0+, Dataview v0.5.0+

🌸 享受您的项目管理之旅！

## 📋 附录A：完整代码示例

### 项目文件模板示例

```yaml
---
tags: [type/project]
Status: active
Goal: "[[提升编程技能]]"
Area: "[[技术学习]]"
start_date: 2024-01-01
end_date: 2024-06-30
progress: 35
priority: high
team: "个人项目"
budget: 0
updated: 2024-01-15
---

# Obsidian项目仪表盘开发

## 项目概述
开发一个现代化的项目管理仪表盘，采用治愈系设计风格。

## 项目目标
- [ ] 完成基础功能开发
- [ ] 实现数据可视化
- [ ] 优化用户体验
- [x] 设计UI界面

## 进度记录
- 2024-01-01: 项目启动
- 2024-01-10: 完成需求分析
- 2024-01-15: 完成UI设计

## 相关资源
- [[JavaScript学习笔记]]
- [[Obsidian插件开发]]
- [[项目管理方法论]]
```

### 目标文件模板示例

```yaml
---
tags: [type/goal]
status: active
deadline: 2024-12-31
priority: high
area: "[[技术学习]]"
---

# 提升编程技能

## 目标描述
在2024年显著提升编程技能，掌握现代Web开发技术栈。

## 关键结果
1. 完成3个完整的项目
2. 学习并掌握React/Vue框架
3. 贡献开源项目

## 相关项目
- [[Obsidian项目仪表盘开发]]
- [[个人网站重构]]
- [[开源贡献计划]]
```

### 领域文件模板示例

```yaml
---
tags: [type/area]
description: "技术相关的学习和项目"
color: "#4A90E2"
---

# 技术学习

## 领域概述
专注于编程技能提升和技术项目开发的领域。

## 当前项目
```dataview
TABLE Status, progress + "%" as Progress, Goal
FROM "6_Project Notes"
WHERE Area = [[技术学习]]
SORT Status ASC
```

## 相关目标
- [[提升编程技能]]
- [[掌握新技术栈]]
```

## 📋 附录B：高级配置指南

### 自定义筛选器

```javascript
// 添加自定义筛选条件
const customFilters = [
    {
        label: '🔥 高优先级',
        color: '#FF6B6B',
        bgColor: '#FFF5F5',
        filter: (p) => p.priority === 'high'
    },
    {
        label: '👥 团队项目',
        color: '#4ECDC4',
        bgColor: '#F0FDFC',
        filter: (p) => p.team && p.team !== '个人项目'
    },
    {
        label: '💰 预算项目',
        color: '#45B7D1',
        bgColor: '#F0F9FF',
        filter: (p) => p.budget && p.budget > 0
    }
];
```

### 自定义状态配置

```javascript
// 扩展项目状态
const statusConfig = {
    'active': { icon: '🟢', label: '进行中', color: '#10B981' },
    'completed': { icon: '✅', label: '已完成', color: '#3B82F6' },
    'risk': { icon: '🔴', label: '风险', color: '#EF4444' },
    'warning': { icon: '🟡', label: '警告', color: '#F59E0B' },
    'cancelled': { icon: '❌', label: '已取消', color: '#6B7280' },
    'paused': { icon: '⏸️', label: '暂停', color: '#8B5CF6' },
    'planning': { icon: '📋', label: '规划中', color: '#06B6D4' }
};
```

### 数据导出功能

```javascript
// 导出项目数据为CSV
function exportProjectsToCSV() {
    const projects = dv.pages('"6_Project Notes"')
        .where(p => p.tags && p.tags.includes("type/project"));

    let csv = 'Name,Status,Progress,Goal,Area,Start Date,End Date\n';

    projects.forEach(project => {
        const row = [
            project.file.name.replace('.md', ''),
            project.Status || '',
            project.progress || 0,
            project.Goal || '',
            project.Area || '',
            project.start_date || '',
            project.end_date || ''
        ].map(field => `"${field}"`).join(',');

        csv += row + '\n';
    });

    // 创建下载链接
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'projects.csv';
    a.click();
}
```

## 📋 附录C：主题定制指南

### Morandi色彩主题

```css
/* Morandi风格色彩配置 */
:root {
    --morandi-bg-primary: #F5F1EB;
    --morandi-bg-secondary: #E8E2D5;
    --morandi-text-primary: #8B7D6B;
    --morandi-text-secondary: #A69B8A;
    --morandi-accent-warm: #D4C4A8;
    --morandi-accent-cool: #B8C5C1;
    --morandi-accent-soft: #C8B8B8;
}

/* 应用Morandi主题 */
.morandi-theme {
    background: linear-gradient(135deg, var(--morandi-bg-primary) 0%, var(--morandi-bg-secondary) 100%);
    color: var(--morandi-text-primary);
}

.morandi-card {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid var(--morandi-accent-soft);
    box-shadow: 0 4px 20px rgba(139, 125, 107, 0.1);
}
```

### 深色主题适配

```css
/* 深色主题配置 */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-bg-primary: #1A1A1A;
        --dark-bg-secondary: #2D2D2D;
        --dark-text-primary: #E5E5E5;
        --dark-text-secondary: #B3B3B3;
        --dark-accent: #4A9EFF;
    }

    .project-card {
        background: var(--dark-bg-secondary);
        color: var(--dark-text-primary);
        border-color: var(--dark-accent);
    }
}
```

## 📋 附录D：性能优化指南

### 大数据量优化

```javascript
// 虚拟滚动实现
class VirtualScroll {
    constructor(container, itemHeight, renderItem) {
        this.container = container;
        this.itemHeight = itemHeight;
        this.renderItem = renderItem;
        this.visibleItems = Math.ceil(container.clientHeight / itemHeight) + 2;
    }

    render(data) {
        const startIndex = Math.floor(this.container.scrollTop / this.itemHeight);
        const endIndex = Math.min(startIndex + this.visibleItems, data.length);

        // 只渲染可见项目
        const visibleData = data.slice(startIndex, endIndex);
        this.container.innerHTML = visibleData.map(this.renderItem).join('');
    }
}
```

### 缓存策略

```javascript
// 数据缓存实现
class ProjectCache {
    constructor(ttl = 300000) { // 5分钟缓存
        this.cache = new Map();
        this.ttl = ttl;
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;

        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }

        return item.data;
    }

    set(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }
}
```

## 📋 附录E：集成扩展

### 与Calendar插件集成

```javascript
// 日历视图集成
function renderProjectCalendar() {
    const projects = dv.pages('"6_Project Notes"')
        .where(p => p.tags && p.tags.includes("type/project"));

    const events = projects.map(project => ({
        title: project.file.name.replace('.md', ''),
        start: project.start_date,
        end: project.end_date,
        color: getStatusColor(project.Status),
        url: project.file.path
    }));

    // 渲染日历组件
    renderCalendar(events);
}
```

### 与Kanban插件集成

```javascript
// 看板视图集成
function renderProjectKanban() {
    const projects = dv.pages('"6_Project Notes"')
        .where(p => p.tags && p.tags.includes("type/project"));

    const kanbanData = {
        'planning': projects.filter(p => p.Status === 'planning'),
        'active': projects.filter(p => p.Status === 'active'),
        'review': projects.filter(p => p.Status === 'review'),
        'completed': projects.filter(p => p.Status === 'completed')
    };

    // 渲染看板组件
    renderKanban(kanbanData);
}
```

## 📞 社区与支持

### 官方资源
- **Obsidian官网**: https://obsidian.md/
- **Dataview文档**: https://blacksmithgu.github.io/obsidian-dataview/
- **社区论坛**: https://forum.obsidian.md/

### 学习资源
- **JavaScript教程**: https://developer.mozilla.org/zh-CN/docs/Web/JavaScript
- **CSS Grid指南**: https://css-tricks.com/snippets/css/complete-guide-grid/
- **Obsidian插件开发**: https://docs.obsidian.md/Plugins/Getting+started

### 贡献指南
欢迎为项目贡献代码、文档或设计建议：

1. **Fork项目仓库**
2. **创建功能分支**
3. **提交更改**
4. **发起Pull Request**

---

**许可证**: MIT License
**维护者**: Obsidian社区
**最后更新**: 2024-01-15

🌸 感谢您选择我们的项目仪表盘！希望它能让您的项目管理更加高效和愉悦。
