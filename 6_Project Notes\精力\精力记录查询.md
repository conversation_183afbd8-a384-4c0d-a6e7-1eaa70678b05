---
tags:
  - type/dashboard
  - dashboard/energy
created: 2025-05-16T10:58
updated: 2025-05-16T12:44
---

# 精力记录查询

## 按类型查询精力记录

```dataviewjs
// 获取用户选择的类型
const container = this.container;
const typeSelector = document.createElement('select');
typeSelector.id = 'energy-type-selector';
typeSelector.style.margin = '10px 0';
typeSelector.style.padding = '5px';
typeSelector.style.width = '200px';

// 添加选项
const types = ['全部', '补充剂', '中医', '习惯', '恢复', '调理', '西医', '其他'];
types.forEach(type => {
    const option = document.createElement('option');
    option.value = type;
    option.text = type;
    typeSelector.appendChild(option);
});

// 添加查询按钮
const queryButton = document.createElement('button');
queryButton.textContent = '查询';
queryButton.style.margin = '0 10px';
queryButton.style.padding = '5px 15px';

// 创建结果容器
const resultContainer = document.createElement('div');
resultContainer.id = 'energy-query-results';
resultContainer.style.marginTop = '15px';

// 添加到页面
container.appendChild(document.createTextNode('选择精力记录类型: '));
container.appendChild(typeSelector);
container.appendChild(queryButton);
container.appendChild(document.createElement('hr'));
container.appendChild(resultContainer);

// 查询函数
async function queryEnergyRecords() {
    const selectedType = typeSelector.value;
    const resultDiv = document.getElementById('energy-query-results');
    resultDiv.innerHTML = '<p>正在查询...</p>';

    // 获取所有日记文件
    const dailyNotes = await dv.pages('"0_Bullet Journal/Daily Notes"')
        .sort(p => p.file.name, 'desc')
        .array();

    // 查找所有精力记录
    let allRecords = [];

    for (const note of dailyNotes) {
        const content = await dv.io.load(note.file.path);
        const lines = content.split('\n');

        for (const line of lines) {
            // 检查是否是精力记录
            if (line.includes('【健康】精力') || line.includes('#精力/')) {
                let matchesType = false;

                // 处理新格式记录 (包含精力/类型 或 #精力/类型)
                if (selectedType === '全部' ||
                    (selectedType !== '其他' && (
                        line.includes(`精力/${selectedType}`) ||
                        line.includes(`#精力/${selectedType}`)
                    )) ||
                    (selectedType === '其他' && !types.slice(1, -1).some(t =>
                        line.includes(`精力/${t}`) ||
                        line.includes(`#精力/${t}`)
                    ))
                ) {
                    matchesType = true;
                }

                // 处理旧格式记录 (根据关键词判断类型)
                if (!matchesType && selectedType !== '全部') {
                    const lowerLine = line.toLowerCase();

                    // 根据关键词判断类型
                    const typeKeywords = {
                        '补充剂': ['维生素', '钙片', '补充剂', '营养素'],
                        '中医': ['中医', '调理', '健胰玉液', '中药'],
                        '习惯': ['sy', 'sy频率', '梦遗', '习惯'],
                        '恢复': ['恢复', '放松', '冥想', '光脚'],
                        '调理': ['调理', '腹泻', '感冒', '症状'],
                        '西医': ['西医', '医院', '检查', '西药']
                    };

                    // 检查是否包含当前选择类型的关键词
                    if (typeKeywords[selectedType]) {
                        matchesType = typeKeywords[selectedType].some(keyword =>
                            lowerLine.includes(keyword.toLowerCase())
                        );
                    }
                }

                // 如果匹配类型，添加到结果中
                if (matchesType || selectedType === '全部') {
                    allRecords.push({
                        date: note.file.name.split(" ")[0],
                        content: line,
                        link: note.file.path
                    });
                }
            }
        }
    }

    // 显示结果
    if (allRecords.length > 0) {
        let html = `<h3>找到 ${allRecords.length} 条${selectedType === '全部' ? '' : selectedType}精力记录</h3>`;
        html += '<ul style="list-style-type: none; padding-left: 0;">';

        allRecords.forEach(record => {
            // 创建日期链接
            // 从文件路径中提取完整的文件名（包含日期、星期和周数）
            const fullFileName = record.link.split('/').pop().replace('.md', '');
            // 尝试使用Obsidian内部链接格式
            const dateLink = `<span style="font-weight: bold; color: #0077cc; cursor: pointer;" onclick="app.workspace.openLinkText('${record.link}', '', true)">${fullFileName}</span>`;

            // 提取并格式化时间
            let timeStr = "";
            const timeMatch = record.content.match(/\(start::(\d+:\d+)\)/);
            if (timeMatch && timeMatch[1]) {
                timeStr = ` ${timeMatch[1]}`;
            }

            // 提取并格式化内容
            let content = record.content;
            // 移除开头的"- "和时间标记
            content = content.replace(/^- /, '').replace(/\(start::\d+:\d+\) ：/, '');

            // 高亮关键部分
            content = content.replace(/\*\*(.*?)\*\*/g, '<span style="color: #d9534f; font-weight: bold;">$1</span>'); // 红色高亮双星号内容
            content = content.replace(/\*(.*?)\*/g, '<span style="color: #5bc0de;">$1</span>'); // 蓝色高亮单星号内容

            // 格式化标签
            content = content.replace(/#精力\/(\w+)/g, '<span style="background-color: #f0ad4e; color: white; padding: 2px 5px; border-radius: 3px; font-size: 0.8em;">#精力/$1</span>');

            // 格式化效果评分
            content = content.replace(/效果:(\d)/g, '<span style="background-color: #5cb85c; color: white; padding: 2px 5px; border-radius: 3px; font-size: 0.8em;">效果:$1</span>');

            // 组合最终显示
            html += `<li style="margin-bottom: 10px; padding-left: 8px;">
                ${dateLink}${timeStr}: ${content}
            </li>`;
        });

        html += '</ul>';
        resultDiv.innerHTML = html;
    } else {
        resultDiv.innerHTML = `<p>未找到${selectedType === '全部' ? '' : selectedType}精力记录</p>`;
    }
}

// 添加点击事件
queryButton.addEventListener('click', queryEnergyRecords);
```

## 精力记录趋势分析

```dataviewjs
// 获取所有精力记录
const allRecords = dv.pages('"0_Bullet Journal/Daily Notes"')
    .sort(p => p.file.name)
    .file.lists
    .where(li =>
        li.text.includes("【健康】精力") ||
        li.text.includes("#精力/")
    );

// 类别列表
const types = ['补充剂', '中医', '习惯', '恢复', '调理', '西医', '其他'];

// 按月份分组
const monthlyRecords = {};
allRecords.forEach(record => {
    // 从记录链接中提取日期
    const dateMatch = record.link.path.match(/(\d{4}-\d{2})-\d{2}/);
    if (dateMatch) {
        const monthYear = dateMatch[1]; // YYYY-MM
        monthlyRecords[monthYear] = (monthlyRecords[monthYear] || 0) + 1;
    }
});

// 显示月度趋势
dv.header(3, "月度精力记录趋势");

if (Object.keys(monthlyRecords).length > 0) {
    // 按月份排序
    const sortedMonths = Object.keys(monthlyRecords).sort();

    // 显示趋势
    dv.paragraph(
        sortedMonths.map(month =>
            `${month}: ${monthlyRecords[month]}条`
        ).join(" | ")
    );
} else {
    dv.paragraph("暂无足够数据分析月度趋势");
}
```

## 精力记录搜索

```dataviewjs
// 创建搜索界面
const container = this.container;
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '输入关键词搜索精力记录...';
searchInput.style.width = '300px';
searchInput.style.padding = '5px';
searchInput.style.margin = '10px 0';

const searchButton = document.createElement('button');
searchButton.textContent = '搜索';
searchButton.style.margin = '0 10px';
searchButton.style.padding = '5px 15px';

const searchResults = document.createElement('div');
searchResults.id = 'energy-search-results';
searchResults.style.marginTop = '15px';

container.appendChild(document.createTextNode('关键词搜索: '));
container.appendChild(searchInput);
container.appendChild(searchButton);
container.appendChild(document.createElement('hr'));
container.appendChild(searchResults);

// 搜索函数
async function searchEnergyRecords() {
    const keyword = searchInput.value.trim().toLowerCase();
    if (!keyword) {
        searchResults.innerHTML = '<p>请输入搜索关键词</p>';
        return;
    }

    searchResults.innerHTML = '<p>正在搜索...</p>';

    // 获取所有日记文件
    const dailyNotes = await dv.pages('"0_Bullet Journal/Daily Notes"')
        .sort(p => p.file.name, 'desc')
        .array();

    // 搜索精力记录
    let matchedRecords = [];

    for (const note of dailyNotes) {
        const content = await dv.io.load(note.file.path);
        const lines = content.split('\n');

        for (const line of lines) {
            // 检查是否是精力记录且包含关键词
            if ((line.includes('【健康】精力') || line.includes('#精力/')) &&
                line.toLowerCase().includes(keyword)) {
                matchedRecords.push({
                    date: note.file.name.split(" ")[0],
                    content: line,
                    link: note.file.path
                });
            }
        }
    }

    // 显示结果
    if (matchedRecords.length > 0) {
        let html = `<h3>找到 ${matchedRecords.length} 条包含"${keyword}"的精力记录</h3>`;
        html += '<ul style="list-style-type: none; padding-left: 0;">';

        matchedRecords.forEach(record => {
            // 创建日期链接
            // 从文件路径中提取完整的文件名（包含日期、星期和周数）
            const fullFileName = record.link.split('/').pop().replace('.md', '');
            // 尝试使用Obsidian内部链接格式
            const dateLink = `<span style="font-weight: bold; color: #0077cc; cursor: pointer;" onclick="app.workspace.openLinkText('${record.link}', '', true)">${fullFileName}</span>`;

            // 提取并格式化时间
            let timeStr = "";
            const timeMatch = record.content.match(/\(start::(\d+:\d+)\)/);
            if (timeMatch && timeMatch[1]) {
                timeStr = ` ${timeMatch[1]}`;
            }

            // 提取并格式化内容
            let content = record.content;
            // 移除开头的"- "和时间标记
            content = content.replace(/^- /, '').replace(/\(start::\d+:\d+\) ：/, '');

            // 高亮关键词
            content = content.replace(
                new RegExp(keyword, 'gi'),
                match => `<span style="background-color: yellow; font-weight: bold;">${match}</span>`
            );

            // 高亮关键部分
            content = content.replace(/\*\*(.*?)\*\*/g, '<span style="color: #d9534f; font-weight: bold;">$1</span>'); // 红色高亮双星号内容
            content = content.replace(/\*(.*?)\*/g, '<span style="color: #5bc0de;">$1</span>'); // 蓝色高亮单星号内容

            // 格式化标签
            content = content.replace(/#精力\/(\w+)/g, '<span style="background-color: #f0ad4e; color: white; padding: 2px 5px; border-radius: 3px; font-size: 0.8em;">#精力/$1</span>');

            // 格式化效果评分
            content = content.replace(/效果:(\d)/g, '<span style="background-color: #5cb85c; color: white; padding: 2px 5px; border-radius: 3px; font-size: 0.8em;">效果:$1</span>');

            // 组合最终显示
            html += `<li style="margin-bottom: 10px; padding-left: 8px;">
                ${dateLink}${timeStr}: ${content}
            </li>`;
        });

        html += '</ul>';
        searchResults.innerHTML = html;
    } else {
        searchResults.innerHTML = `<p>未找到包含"${keyword}"的精力记录</p>`;
    }
}

// 添加点击事件
searchButton.addEventListener('click', searchEnergyRecords);
searchInput.addEventListener('keypress', e => {
    if (e.key === 'Enter') {
        searchEnergyRecords();
    }
});
```
