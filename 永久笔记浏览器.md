---
created: 2025-05-25T16:35
updated: 2025-05-25T16:35
tags:
  - type/dashboard
  - permanent/browser
---

# 📘 永久笔记浏览器

> 💎 核心知识沉淀，构建个人智慧体系

## 🚀 快速操作

```dataviewjs
// 创建快速操作面板
const quickActionsContainer = document.createElement('div');
quickActionsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F0F8FF 0%, #F8F0FF 100%);
    border-radius: 16px;
    border: 2px solid rgba(165, 214, 232, 0.3);
`;

const quickActions = [
    {
        title: '新建永久笔记',
        icon: '➕',
        desc: '创建核心知识',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates/Permanent Note Template', '', false);
            }
        },
        color: '#A5D6E8'
    },
    {
        title: '目标管理',
        icon: '🎯',
        desc: '查看目标规划',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('3_Permanent notes/3_0_Goal目标', '', false);
            }
        },
        color: '#B8D6B8'
    },
    {
        title: '知识图谱',
        icon: '🕸️',
        desc: '查看知识连接',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('知识库概览仪表板', '', false);
            }
        },
        color: '#E8D5A5'
    },
    {
        title: '笔记模板',
        icon: '📋',
        desc: '查看笔记模板',
        action: () => {
            if (app && app.workspace) {
                app.workspace.openLinkText('Templates', '', false);
            }
        },
        color: '#E8B4A0'
    }
];

let actionsHTML = '';
quickActions.forEach((action, index) => {
    actionsHTML += `
        <div style="background: rgba(255,255,255,0.8); border-radius: 12px; padding: 18px; text-align: center;
                    border: 1px solid ${action.color}30; transition: all 0.2s ease; cursor: pointer;"
             class="quick-action-btn" data-index="${index}">
            <div style="font-size: 2em; margin-bottom: 8px;">${action.icon}</div>
            <div style="font-weight: 600; color: #8B7355; margin-bottom: 4px; font-size: 1em;">
                ${action.title}
            </div>
            <div style="font-size: 0.85em; color: #A0896B; opacity: 0.8;">
                ${action.desc}
            </div>
        </div>
    `;
});

quickActionsContainer.innerHTML = actionsHTML;

// 添加点击事件
setTimeout(() => {
    const actionBtns = quickActionsContainer.querySelectorAll('.quick-action-btn');
    actionBtns.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            quickActions[index].action();
        });

        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(139, 115, 85, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}, 100);

this.container.appendChild(quickActionsContainer);
```

## 📘 所有永久笔记

```dataview
TABLE WITHOUT ID
    file.link AS "💎 笔记标题",
    choice(subject, subject, "📝 未分类") AS "🏷️ 学科",
    choice(importance = "核心", "🔴 核心", choice(importance = "重要", "🟡 重要", "⚪ 一般")) AS "⭐ 重要性",
    choice(status, status, "📝 草稿") AS "📊 状态",
    file.mtime AS "📅 修改时间"
FROM "3_Permanent notes"
WHERE file.name != "README" AND file.name != "3_0_Goal目标"
SORT file.mtime DESC
```

## 🏷️ 按学科分类

```dataview
TABLE WITHOUT ID
    subject AS "🎓 学科",
    length(rows.file.link) AS "📊 笔记数量",
    join(rows.file.link, ", ") AS "💎 笔记列表"
FROM "3_Permanent notes"
WHERE subject AND file.name != "README" AND file.name != "3_0_Goal目标"
GROUP BY subject
SORT length(rows.file.link) DESC
```

## ⭐ 核心知识

```dataview
TABLE WITHOUT ID
    file.link AS "💎 笔记标题",
    choice(subject, subject, "📝 未分类") AS "🏷️ 学科",
    choice(status, status, "📝 草稿") AS "📊 状态",
    file.mtime AS "📅 修改时间"
FROM "3_Permanent notes"
WHERE importance = "核心"
SORT file.mtime DESC
LIMIT 10
```

## 📊 永久笔记统计

```dataviewjs
// 获取所有永久笔记
const allPermanent = dv.pages('"3_Permanent notes"').where(p => p.file.name !== "README" && p.file.name !== "3_0_Goal目标");

// 统计数据
const totalPermanent = allPermanent.length;
const coreNotes = allPermanent.where(p => p.importance === "核心").length;
const importantNotes = allPermanent.where(p => p.importance === "重要").length;
const generalNotes = allPermanent.where(p => !p.importance || p.importance === "一般").length;

// 按状态统计
const statusGroups = {};
allPermanent.forEach(p => {
    const status = p.status || "草稿";
    statusGroups[status] = (statusGroups[status] || 0) + 1;
});

// 创建统计容器
const statsContainer = document.createElement('div');
statsContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #F0F8FF 0%, #F8F0FF 100%);
    border-radius: 16px;
    border: 2px solid rgba(165, 214, 232, 0.2);
`;

const stats = [
    { label: '总笔记', value: totalPermanent, icon: '📘', color: '#8B7355' },
    { label: '核心', value: coreNotes, icon: '🔴', color: '#F44336' },
    { label: '重要', value: importantNotes, icon: '🟡', color: '#FF9800' },
    { label: '一般', value: generalNotes, icon: '⚪', color: '#9E9E9E' }
];

let statsHTML = '';
stats.forEach(stat => {
    statsHTML += `
        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); 
                    border-radius: 12px; border: 1px solid ${stat.color}20;">
            <div style="font-size: 1.8em; margin-bottom: 8px;">${stat.icon}</div>
            <div style="font-size: 1.8em; font-weight: 700; color: ${stat.color}; margin-bottom: 4px;">
                ${stat.value}
            </div>
            <div style="font-size: 0.9em; color: #8B7355; font-weight: 500;">
                ${stat.label}
            </div>
        </div>
    `;
});

statsContainer.innerHTML = statsHTML;
this.container.appendChild(statsContainer);
```

## 📈 最近更新

```dataview
TABLE WITHOUT ID
    file.link AS "💎 笔记标题",
    choice(subject, subject, "📝 未分类") AS "🏷️ 学科",
    choice(importance = "核心", "🔴 核心", choice(importance = "重要", "🟡 重要", "⚪ 一般")) AS "⭐ 重要性",
    file.mtime AS "📅 修改时间"
FROM "3_Permanent notes"
WHERE file.name != "README" AND file.name != "3_0_Goal目标"
SORT file.mtime DESC
LIMIT 8
```

## 🎯 目标管理

```dataview
TABLE WITHOUT ID
    file.link AS "🎯 目标文件"
FROM "3_Permanent notes"
WHERE file.name = "3_0_Goal目标"
```

---

<div style="text-align: center; margin-top: 30px; padding: 15px; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,246,243,0.9) 100%); border-radius: 12px; border: 1px solid rgba(232, 180, 160, 0.2);">
    <div style="font-size: 1.1em; color: #8B7355; font-weight: 600; margin-bottom: 6px;">
        💎 知识沉淀小贴士
    </div>
    <div style="font-size: 0.9em; color: #A0896B;">
        永久笔记是您思考的结晶，定期回顾和完善，让知识真正成为智慧
    </div>
</div>
