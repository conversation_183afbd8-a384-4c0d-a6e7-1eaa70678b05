<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码管理</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            display: flex;
            justify-content: center;
            padding: 40px 0;
            margin: 0;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 600px;
            padding: 30px;
        }
        h1 {
            color: #4169e1;
            text-align: center;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
        }
        input[type="email"],
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            width: 100%;
            background-color: #4169e1;
            color: white;
            border: none;
            padding: 12px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 10px;
        }
        button:hover {
            background-color: #3a5fcd;
        }
        button:disabled {
            background-color: #b0c4de;
            cursor: not-allowed;
        }
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .auth-form {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px dashed #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>验证码管理</h1>
        
        <div class="auth-form" id="authForm">
            <div class="form-group">
                <label for="password">管理密码</label>
                <input type="password" id="password" placeholder="请输入管理密码">
            </div>
            <button id="authBtn">验证</button>
        </div>
        
        <div id="adminPanel" style="display: none;">
            <div class="form-group">
                <label for="email">邮箱地址</label>
                <input type="email" id="email" placeholder="例如: <EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="code">验证码</label>
                <input type="text" id="code" placeholder="例如: 123456">
            </div>
            
            <div class="form-group">
                <label for="service">服务名称</label>
                <input type="text" id="service" placeholder="例如: 域名注册服务">
            </div>
            
            <button id="addBtn">添加验证码</button>
            
            <div id="message" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 简单的管理密码验证
        document.getElementById('authBtn').addEventListener('click', function() {
            const password = document.getElementById('password').value;
            // 这里使用一个简单的密码，实际应用中应该使用更安全的验证方式
            if (password === 'admin123') {
                document.getElementById('authForm').style.display = 'none';
                document.getElementById('adminPanel').style.display = 'block';
            } else {
                alert('密码错误');
            }
        });
        
        // 添加验证码
        document.getElementById('addBtn').addEventListener('click', async function() {
            const email = document.getElementById('email').value.trim();
            const code = document.getElementById('code').value.trim();
            const service = document.getElementById('service').value.trim();
            const messageDiv = document.getElementById('message');
            const addBtn = document.getElementById('addBtn');
            
            if (!email || !code) {
                messageDiv.className = 'message error';
                messageDiv.textContent = '邮箱和验证码不能为空';
                messageDiv.style.display = 'block';
                return;
            }
            
            // 禁用按钮，防止重复提交
            addBtn.disabled = true;
            
            try {
                // 调用后端API
                const response = await fetch('http://localhost:5000/api/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email,
                        code,
                        service: service || '未知服务'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    messageDiv.className = 'message success';
                    messageDiv.textContent = '验证码添加成功';
                    
                    // 清空输入框
                    document.getElementById('email').value = '';
                    document.getElementById('code').value = '';
                    document.getElementById('service').value = '';
                } else {
                    messageDiv.className = 'message error';
                    messageDiv.textContent = result.message || '添加失败';
                }
            } catch (error) {
                console.error('添加出错:', error);
                messageDiv.className = 'message error';
                messageDiv.textContent = '添加失败，请检查网络连接或稍后重试';
            } finally {
                addBtn.disabled = false;
                messageDiv.style.display = 'block';
                
                // 3秒后自动隐藏消息
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 3000);
            }
        });
    </script>
</body>
</html>